import json
from datetime import datetime, timedelta
from unittest import mock

from django.contrib.contenttypes.models import ContentType
from django.urls import reverse_lazy as reverse
from django.utils import timezone
from rest_framework import status

from didero.tasks.models import Task, TaskStatus, TaskTypeV2
from didero.tasks.schemas import (
    TaskCategory,
    TaskContextPanelType,
)
from didero.tasks.schemas import (
    TaskType as TaskTypeName,
)
from didero.tasks.tests.utils.base_task_test import BaseTaskTest
from didero.tasks.utils import create_task_v2
from didero.testing.cases import TestCase


class TestTaskViewset(BaseTaskTest):
    def test_retrieve_task(self):
        url = reverse("task-detail", args=[self.tasks["PO_APPROVAL_REQUEST"].id])
        response = self.client.get(url, format="json")
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data["status"], TaskStatus.PENDING.value)

    def test_get_task_by_model_type_and_id(self):
        url = reverse("task-list")
        response = self.client.get(
            url,
            {
                "model_type": ContentType.objects.get_for_model(self.order).model,
                "model_id": self.order.pk,
            },
            format="json",
        )
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data["count"], len(self.tasks))

    def test_update_task(self):
        url = reverse("task-detail", args=[self.tasks["PO_APPROVAL_REQUEST"].id])
        data = {"status": TaskStatus.COMPLETED.value}
        response = self.client.patch(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.tasks["PO_APPROVAL_REQUEST"].refresh_from_db()
        self.assertEqual(
            self.tasks["PO_APPROVAL_REQUEST"].status, TaskStatus.COMPLETED.value
        )

    def test_delete_task(self):
        url = reverse("task-detail", args=[self.tasks["PO_APPROVAL_REQUEST"].id])
        response = self.client.delete(url, format="json")
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)
        self.assertEqual(Task.objects.count(), len(self.tasks) - 1)

    def test_task_action_complete(self):
        url = reverse("task-action")
        data = {
            "task_id": self.tasks["PO_APPROVAL_REQUEST"].id,
            "task_action_name": "MARK_AS_COMPLETE",
        }
        response = self.client.post(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.tasks["PO_APPROVAL_REQUEST"].refresh_from_db()
        self.assertEqual(
            self.tasks["PO_APPROVAL_REQUEST"].status, TaskStatus.COMPLETED.value
        )

    def test_task_action_reschedule(self):
        url = reverse("task-action")
        data = {
            "task_id": self.tasks["PO_APPROVAL_REQUEST"].id,
            "task_action_name": "RESCHEDULE",
            "remind_again_in": 48,
        }
        response = self.client.post(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.tasks["PO_APPROVAL_REQUEST"].refresh_from_db()
        self.assertIsNotNone(self.tasks["PO_APPROVAL_REQUEST"].next_reminder_at)

    def test_task_action_invalid(self):
        url = reverse("task-action")
        data = {
            "task_id": self.tasks["PO_APPROVAL_REQUEST"].id,
            "task_action_name": "INVALID_ACTION",
        }
        response = self.client.post(url, data, format="json")
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)


class TestTaskV2Viewset(TestCase):
    def setUp(self):
        super().setUp()
        self.user = self.create_user()
        self.auth_as_user(self.user)
        self.team = self.user.teams.first()
        self.supplier = self.create_supplier(team=self.team)
        self.order = self.create_purchase_order(
            supplier=self.supplier,
            team=self.team,
            placed_by=self.user,
        )
        self.tasks = {}
        self.task_type_v2_freight_charge_confirmation = TaskTypeV2.objects.create(
            name=TaskTypeName.FREIGHT_CHARGE_CONFIRMATION,
            preview_description="Freight Charge Confirmation",
            preview_title="Freight Charge Confirmation",
            title="{order_number} includes a freight charge of {freight_charge} that was not on {po_number}",
            category=TaskCategory.PURCHASE_ORDER,
            context_panel_type=TaskContextPanelType.DOCUMENT_COMPARE,
            param_definition=json.dumps(
                {
                    "parameters": [
                        {
                            "name": "order_number",
                            "type": "string",
                            "description": "The order number",
                            "required": True,
                        },
                        {
                            "name": "freight_charge",
                            "type": "number",
                            "description": "The freight charge",
                            "required": True,
                        },
                        {
                            "name": "po_number",
                            "type": "string",
                            "description": "The PO number",
                            "required": True,
                        },
                    ]
                }
            ),
        )
        self.task_type_v2_po_approval_request = TaskTypeV2.objects.create(
            name=TaskTypeName.PO_APPROVAL_REQUEST,
            preview_description="{supplier_name}",
            preview_title="PO Approval Request",
            title="{placed_by} created {po_number} and is awaiting your approval.",
            category=TaskCategory.PURCHASE_ORDER,
            context_panel_type=TaskContextPanelType.DOCUMENT_COMPARE,
            param_definition=json.dumps(
                {
                    "parameters": [
                        {
                            "name": "placed_by",
                            "type": "string",
                            "required": True,
                            "description": "The user who placed the PO",
                        },
                        {
                            "name": "po_number",
                            "type": "string",
                            "description": "The PO number",
                            "required": True,
                        },
                        {
                            "name": "supplier_name",
                            "type": "string",
                            "required": True,
                            "description": "The supplier name",
                        },
                    ]
                }
            ),
        )

        self.tasks = {
            "freight_charge_confirmation": create_task_v2(
                task_type=TaskTypeName.FREIGHT_CHARGE_CONFIRMATION,
                user=self.user,
                model_type=ContentType.objects.get_for_model(self.order),
                model_id=self.order.id,
                task_type_params={
                    "order_number": "M372103",
                    "freight_charge": 100.00,
                    "po_number": self.order.po_number,
                },
            ),
            "po_approval_request": create_task_v2(
                task_type=TaskTypeName.PO_APPROVAL_REQUEST,
                user=self.user,
                model_type=ContentType.objects.get_for_model(self.order),
                model_id=self.order.id,
                task_type_params={
                    "placed_by": self.user.get_full_name(),
                    "po_number": self.order.po_number,
                    "supplier_name": self.supplier.name,
                },
            ),
        }

    def test_retrieve_task_v2(self):
        task = self.tasks["po_approval_request"]

        url = reverse("task-v2-detail", args=[task.id])
        response = self.client.get(url, format="json")
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data["status"], TaskStatus.PENDING.value)
        self.assertIsNotNone(response.data["task_type_v2"])

    def test_task_v2_includes_created_at_and_seconds_since_creation(self):
        """Test that TaskV2 response includes created_at in UTC and seconds_since_creation."""
        task = self.tasks["po_approval_request"]

        # Mock the timezone.now() to return a fixed time exactly 300 seconds after task creation
        fixed_now = task.created_at + timedelta(seconds=300)
        with mock.patch("django.utils.timezone.now", return_value=fixed_now):
            url = reverse("task-v2-detail", args=[task.id])
            response = self.client.get(url, format="json")

            # Check that created_at and seconds_since_creation are in the response
            self.assertIn("created_at", response.data)
            self.assertIn("seconds_since_creation", response.data)

            # Verify created_at is in ISO format with UTC "Z" suffix
            self.assertRegex(
                response.data["created_at"], r"\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}Z"
            )

            # Verify seconds_since_creation is exactly 300 seconds (5 minutes)
            self.assertEqual(response.data["seconds_since_creation"], 300)

    def test_list_tasks_v2(self):
        url = reverse("task-v2-list")
        response = self.client.get(url, format="json")
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data["count"], len(self.tasks))

    def test_task_v2_snooze_with_remind_again_in(self):
        """Test that a task can be snoozed with remind_again_in parameter."""
        task = self.tasks["po_approval_request"]

        # Snooze the task with remind_again_in
        remind_hours = 48
        url = reverse("task-v2-detail", args=[task.id])
        data = {
            "status": TaskStatus.ON_HOLD.value,
            "remind_again_in": remind_hours,
        }
        response = self.client.patch(url, data, format="json")

        # Verify the response
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data["status"], TaskStatus.ON_HOLD.value)

        # Refresh the task from the database
        task.refresh_from_db()

        # Verify the task status and next_reminder_at
        self.assertEqual(task.status, TaskStatus.ON_HOLD.value)
        self.assertIsNotNone(task.next_reminder_at)

        # Verify the next_reminder_at is approximately remind_hours in the future
        # Allow for a small difference due to test execution time
        expected_time = timezone.now() + timedelta(hours=remind_hours)
        time_diff = (task.next_reminder_at - expected_time).total_seconds()
        self.assertLess(abs(time_diff), 10)  # Within 10 seconds

    def test_task_v2_snooze_with_default_reminder(self):
        """Test that a task can be snoozed with default reminder time."""
        task = self.tasks["po_approval_request"]

        # Snooze the task without remind_again_in
        url = reverse("task-v2-detail", args=[task.id])
        data = {
            "status": TaskStatus.ON_HOLD.value,
        }
        response = self.client.patch(url, data, format="json")

        # Verify the response
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data["status"], TaskStatus.ON_HOLD.value)

        # Refresh the task from the database
        task.refresh_from_db()

        # Verify the task status and next_reminder_at
        self.assertEqual(task.status, TaskStatus.ON_HOLD.value)
        self.assertIsNotNone(task.next_reminder_at)

        # Verify the next_reminder_at is approximately 24 hours in the future
        # Allow for a small difference due to test execution time
        expected_time = timezone.now() + timedelta(hours=24)
        time_diff = (task.next_reminder_at - expected_time).total_seconds()
        self.assertLess(abs(time_diff), 10)  # Within 10 seconds

    def test_task_v2_mark_as_completed(self):
        """Test that a task can be marked as completed using PATCH."""
        task = self.tasks["po_approval_request"]

        # Mark the task as completed
        url = reverse("task-v2-detail", args=[task.id])
        data = {
            "status": TaskStatus.COMPLETED.value,
        }
        response = self.client.patch(url, data, format="json")

        # Verify the response
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data["status"], TaskStatus.COMPLETED.value)

        # Refresh the task from the database
        task.refresh_from_db()

        # Verify the task status
        self.assertEqual(task.status, TaskStatus.COMPLETED.value)

    def test_task_v2_mark_as_pending(self):
        """Test that a task can be marked as pending using PATCH."""
        # First set the task to a different status
        task = self.tasks["po_approval_request"]
        task.status = TaskStatus.ON_HOLD.value
        task.save()

        # Mark the task as pending
        url = reverse("task-v2-detail", args=[task.id])
        data = {
            "status": TaskStatus.PENDING.value,
        }
        response = self.client.patch(url, data, format="json")

        # Verify the response
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data["status"], TaskStatus.PENDING.value)

        # Refresh the task from the database
        task.refresh_from_db()

        # Verify the task status
        self.assertEqual(task.status, TaskStatus.PENDING.value)

    def test_task_v2_invalid_remind_again_in(self):
        """Test error handling when an invalid remind_again_in value is provided."""
        task = self.tasks["po_approval_request"]

        # Try to snooze the task with an invalid remind_again_in
        url = reverse("task-v2-detail", args=[task.id])
        data = {
            "status": TaskStatus.ON_HOLD.value,
            "remind_again_in": "not_a_number",
        }
        response = self.client.patch(url, data, format="json")

        # Verify the response indicates an error
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn("error", response.data)
        self.assertIn(
            "remind_again_in must be a valid number of hours", response.data["error"]
        )

    def test_task_v2_invalid_status(self):
        """Test error handling when an invalid status value is provided."""
        task = self.tasks["po_approval_request"]

        # Try to update the task with an invalid status
        url = reverse("task-v2-detail", args=[task.id])
        data = {"status": "INVALID_STATUS"}
        response = self.client.patch(url, data, format="json")

        # Verify the response indicates an error
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

        # Refresh from database to make sure status wasn't changed
        task.refresh_from_db()
        self.assertEqual(task.status, TaskStatus.PENDING.value)

    def test_task_v2_completed_clears_reminder(self):
        """Test that marking a task as completed clears the next_reminder_at field."""
        task = self.tasks["po_approval_request"]

        # First set the task to have a reminder
        task.next_reminder_at = timezone.now() + timedelta(hours=24)
        task.save()

        # Verify the reminder was set
        self.assertIsNotNone(task.next_reminder_at)

        # Mark the task as completed
        url = reverse("task-v2-detail", args=[task.id])
        data = {
            "status": TaskStatus.COMPLETED.value,
        }
        response = self.client.patch(url, data, format="json")

        # Verify the response
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Refresh the task from the database
        task.refresh_from_db()

        # Verify that next_reminder_at is set to None when task is completed
        self.assertEqual(task.status, TaskStatus.COMPLETED.value)
        self.assertIsNone(task.next_reminder_at)

    def test_status_counts_in_response(self):
        """Test that the response includes status counts."""
        url = reverse("task-v2-list")
        response = self.client.get(url, format="json")

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn("status_counts", response.data)

        # Check that all statuses are included in the counts
        self.assertIn(TaskStatus.PENDING.value, response.data["status_counts"])
        self.assertIn(TaskStatus.COMPLETED.value, response.data["status_counts"])
        self.assertIn(TaskStatus.ON_HOLD.value, response.data["status_counts"])

    def test_status_counts_with_status_filter(self):
        """Test that status counts remain accurate when filtering by status."""
        # Get count of pending tasks
        pending_tasks = Task.objects.filter(
            task_type_v2__isnull=False, status=TaskStatus.PENDING.value
        ).count()

        # Get tasks filtered by PENDING status
        url = reverse("task-v2-list")
        response = self.client.get(
            url, {"status": TaskStatus.PENDING.value}, format="json"
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn("status_counts", response.data)

        # Check that the filtered results only include pending tasks
        self.assertEqual(response.data["count"], pending_tasks)

        # But status_counts should still include all tasks regardless of filter
        all_tasks = Task.objects.filter(task_type_v2__isnull=False).count()
        self.assertEqual(sum(response.data["status_counts"].values()), all_tasks)

    def test_status_counts_with_other_filters(self):
        """Test that status counts respect other filters but ignore status filter."""
        # Create a task for a different model_type
        other_supplier = self.create_supplier(team=self.team)
        other_task = Task.objects.create(
            user=self.user,
            task_type_v2=self.task_type_v2_po_approval_request,
            task_config={
                "title": "Other Task",
                "preview_title": "Other",
                "preview_description": "Test other task",
                "category": "PURCHASE_ORDER",
                "context_panel_type": "PO_DETAILS",
                "context_panel_params": None,
            },
            status=TaskStatus.PENDING.value,
            model_type=ContentType.objects.get_for_model(other_supplier),
            model_id=str(other_supplier.id),
        )

        # Filter by model_type = purchaseorder and status = PENDING
        url = reverse("task-v2-list")
        model_type = self.order._meta.model_name
        response = self.client.get(
            url,
            {"model_type": model_type, "status": TaskStatus.PENDING.value},
            format="json",
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Count PO tasks only
        po_tasks = Task.objects.filter(
            task_type_v2__isnull=False,
            model_type=ContentType.objects.get_for_model(self.order),
        ).count()

        # Status counts should include all statuses for purchase order tasks,
        # but not include the task for the other supplier
        total_counts = sum(response.data["status_counts"].values())
        self.assertEqual(total_counts, po_tasks)

        # Check that other_task is not included in the counts
        all_tasks = Task.objects.filter(task_type_v2__isnull=False).count()
        self.assertNotEqual(total_counts, all_tasks)

    def test_task_v2_search_by_title(self):
        """Test the search functionality for TaskV2 by title field."""
        # Create a task with specific searchable content in title
        search_task = Task.objects.create(
            user=self.user,
            task_type_v2=self.task_type_v2_po_approval_request,
            task_config={
                "title": "Unique Searchable Title",
                "preview_title": "Preview",
                "preview_description": "Description",
                "category": "PURCHASE_ORDER",
                "context_panel_type": "PO_DETAILS",
                "context_panel_params": None,
            },
            status=TaskStatus.PENDING.value,
            model_type=ContentType.objects.get_for_model(self.order),
            model_id=str(self.order.id),
        )

        # Search for a term that should match the title
        url = reverse("task-v2-list")
        response = self.client.get(
            url,
            {"search": "Unique Searchable"},
            format="json",
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data["count"], 1)
        self.assertEqual(response.data["results"][0]["id"], search_task.id)

    def test_task_v2_search_by_preview_title(self):
        """Test the search functionality for TaskV2 by preview_title field."""
        # Create a task with specific searchable content in preview_title
        search_task = Task.objects.create(
            user=self.user,
            task_type_v2=self.task_type_v2_po_approval_request,
            task_config={
                "title": "Title",
                "preview_title": "Unique Searchable Preview",
                "preview_description": "Description",
                "category": "PURCHASE_ORDER",
                "context_panel_type": "PO_DETAILS",
                "context_panel_params": None,
            },
            status=TaskStatus.PENDING.value,
            model_type=ContentType.objects.get_for_model(self.order),
            model_id=str(self.order.id),
        )

        # Search for a term that should match the preview_title
        url = reverse("task-v2-list")
        response = self.client.get(
            url,
            {"search": "Searchable Preview"},
            format="json",
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data["count"], 1)
        self.assertEqual(response.data["results"][0]["id"], search_task.id)

    def test_task_v2_search_by_description(self):
        """Test the search functionality for TaskV2 by preview_description field."""
        # Create a task with specific searchable content in preview_description
        search_task = Task.objects.create(
            user=self.user,
            task_type_v2=self.task_type_v2_po_approval_request,
            task_config={
                "title": "Title",
                "preview_title": "Preview",
                "preview_description": "Unique Searchable Description Text",
                "category": "PURCHASE_ORDER",
                "context_panel_type": "PO_DETAILS",
                "context_panel_params": None,
            },
            status=TaskStatus.PENDING.value,
            model_type=ContentType.objects.get_for_model(self.order),
            model_id=str(self.order.id),
        )

        # Search for a term that should match the preview_description
        url = reverse("task-v2-list")
        response = self.client.get(
            url,
            {"search": "Searchable Description"},
            format="json",
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data["count"], 1)
        self.assertEqual(response.data["results"][0]["id"], search_task.id)

    def test_task_v2_search_exact_terms(self):
        """Test searching with exact terms in different fields."""
        # Create tasks with different searchable content
        task1 = Task.objects.create(
            user=self.user,
            task_type_v2=self.task_type_v2_po_approval_request,
            task_config={
                "title": "Apple Task",
                "preview_title": "Fruit Task",
                "preview_description": "This is about apples",
                "category": "PURCHASE_ORDER",
                "context_panel_type": "PO_DETAILS",
                "context_panel_params": None,
            },
            status=TaskStatus.PENDING.value,
            model_type=ContentType.objects.get_for_model(self.order),
            model_id=str(self.order.id),
        )

        task2 = Task.objects.create(
            user=self.user,
            task_type_v2=self.task_type_v2_po_approval_request,
            task_config={
                "title": "Banana Task",
                "preview_title": "Fruit Task",
                "preview_description": "This is about bananas",
                "category": "PURCHASE_ORDER",
                "context_panel_type": "PO_DETAILS",
                "context_panel_params": None,
            },
            status=TaskStatus.PENDING.value,
            model_type=ContentType.objects.get_for_model(self.order),
            model_id=str(self.order.id),
        )

        # Search for a term that should match both tasks (shared term "Fruit")
        url = reverse("task-v2-list")
        response = self.client.get(
            url,
            {"search": "Fruit"},
            format="json",
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data["count"], 2)

        # Search for term that should match only task1
        response = self.client.get(
            url,
            {"search": "Apple"},
            format="json",
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data["count"], 1)
        self.assertEqual(response.data["results"][0]["id"], task1.id)

        # Search for term that should match only task2
        response = self.client.get(
            url,
            {"search": "Banana"},
            format="json",
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data["count"], 1)
        self.assertEqual(response.data["results"][0]["id"], task2.id)

    def test_task_v2_search_with_other_filters(self):
        """Test combining search with other filters."""
        # Create two tasks with searchable content but different statuses
        task1 = Task.objects.create(
            user=self.user,
            task_type_v2=self.task_type_v2_po_approval_request,
            task_config={
                "title": "Searchable Task",
                "preview_title": "Common Term",
                "preview_description": "Description",
                "category": "PURCHASE_ORDER",
                "context_panel_type": "PO_DETAILS",
                "context_panel_params": None,
            },
            status=TaskStatus.PENDING.value,
            model_type=ContentType.objects.get_for_model(self.order),
            model_id=str(self.order.id),
        )

        task2 = Task.objects.create(
            user=self.user,
            task_type_v2=self.task_type_v2_po_approval_request,
            task_config={
                "title": "Searchable Task",
                "preview_title": "Common Term",
                "preview_description": "Description",
                "category": "PURCHASE_ORDER",
                "context_panel_type": "PO_DETAILS",
                "context_panel_params": None,
            },
            status=TaskStatus.COMPLETED.value,
            model_type=ContentType.objects.get_for_model(self.order),
            model_id=str(self.order.id),
        )

        # Search with a status filter that should only match task1
        url = reverse("task-v2-list")
        response = self.client.get(
            url,
            {"search": "Searchable", "status": TaskStatus.PENDING.value},
            format="json",
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data["count"], 1)
        self.assertEqual(response.data["results"][0]["id"], task1.id)

        # Search with a status filter that should only match task2
        response = self.client.get(
            url,
            {"search": "Searchable", "status": TaskStatus.COMPLETED.value},
            format="json",
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data["count"], 1)
        self.assertEqual(response.data["results"][0]["id"], task2.id)

    def test_task_v2_get_available_assignees(self):
        """Test retrieving available assignees for a task."""
        task = self.tasks["po_approval_request"]

        # Create more users in the same team
        user2 = self.create_user(email="<EMAIL>")
        user2.teams.add(self.team)

        user3 = self.create_user(email="<EMAIL>")
        user3.teams.add(self.team)

        # Create a user in a different team
        other_user = self.create_user(email="<EMAIL>")
        # create_user creates a team automatically, so we'll use that instead of create_team
        other_team = other_user.teams.first()

        # Call the available-assignees endpoint
        url = reverse("task-v2-available-assignees", args=[task.id])
        response = self.client.get(url, format="json")

        # Verify the response
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Get user IDs from the response
        user_ids = [user["id"] for user in response.data]

        # Verify that the original user, user2, and user3 are in the list
        self.assertIn(self.user.id, user_ids)
        self.assertIn(user2.id, user_ids)
        self.assertIn(user3.id, user_ids)

        # Verify that the user from a different team is not in the list
        self.assertNotIn(other_user.id, user_ids)

    def test_task_v2_reassign(self):
        """Test reassigning a task to another user."""
        # Create the original task
        task = self.tasks["po_approval_request"]
        original_user = task.user

        # Create another user to reassign the task to
        new_user = self.create_user(email="<EMAIL>")
        # Add the new user to the same team
        new_user.teams.add(self.team)

        # Call the reassign endpoint
        url = reverse("task-v2-reassign", args=[task.id])
        response = self.client.post(
            url,
            {"user_id": new_user.id},
            format="json",
        )

        # Verify the response
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Refresh the task from the database
        task.refresh_from_db()

        # Verify the task has been reassigned
        self.assertEqual(task.user.id, new_user.id)
        self.assertNotEqual(task.user.id, original_user.id)

    def test_task_v2_reassign_without_user_id(self):
        """Test error handling when reassigning without user_id."""
        task = self.tasks["po_approval_request"]

        # Call the reassign endpoint without user_id
        url = reverse("task-v2-reassign", args=[task.id])
        response = self.client.post(url, {}, format="json")

        # Verify the response indicates an error
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn("error", response.data)
        self.assertIn(
            "Either user_id or group_id is required for task reassignment.",
            response.data["error"],
        )

    def test_task_v2_reassign_invalid_user(self):
        """Test error handling when reassigning to a non-existent user."""
        task = self.tasks["po_approval_request"]

        # Call the reassign endpoint with a non-existent user_id
        url = reverse("task-v2-reassign", args=[task.id])
        response = self.client.post(
            url,
            {"user_id": 999999},  # Non-existent user
            format="json",
        )

        # Verify the response indicates an error
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
        self.assertIn("error", response.data)
        self.assertIn("User not found", response.data["error"])

    def test_task_v2_reassign_user_from_different_team(self):
        """Test error handling when reassigning to a user from a different team."""
        task = self.tasks["po_approval_request"]

        # Create a user in a different team
        other_user = self.create_user(email="<EMAIL>")
        # create_user creates a team automatically, so we'll use that instead of create_team
        other_team = other_user.teams.first()

        # Call the reassign endpoint with a user from a different team
        url = reverse("task-v2-reassign", args=[task.id])
        response = self.client.post(
            url,
            {"user_id": other_user.id},
            format="json",
        )

        # Verify the response indicates an error
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn("error", response.data)
        self.assertIn("must belong to the same team", response.data["error"])

    def test_status_counts_equal_actual_task_count_with_user_groups(self):
        """Test that status counts sum equals actual distinct task count when user belongs to multiple groups."""
        from django.db.models import Q

        from didero.users.models import DideroUserGroup

        # Create multiple user groups
        group1 = DideroUserGroup.objects.create(
            name="Test Group 1",
            team=self.team,
        )
        group2 = DideroUserGroup.objects.create(
            name="Test Group 2",
            team=self.team,
        )

        # Add user to both groups (this could cause duplicate counting)
        group1.users.add(self.user)
        group2.users.add(self.user)

        # Create tasks assigned to different entities
        # Task 1: Assigned directly to user
        Task.objects.create(
            user=self.user,
            task_type_v2=self.task_type_v2_po_approval_request,
            task_config={
                "title": "Direct User Task",
                "preview_title": "Direct",
                "preview_description": "Assigned to user directly",
                "category": "PURCHASE_ORDER",
                "context_panel_type": "PO_DETAILS",
                "context_panel_params": None,
            },
            status=TaskStatus.PENDING.value,
            model_type=ContentType.objects.get_for_model(self.order),
            model_id=str(self.order.id),
        )

        # Task 2: Assigned to group1 (user is member)
        Task.objects.create(
            user_group=group1,
            task_type_v2=self.task_type_v2_freight_charge_confirmation,
            task_config={
                "title": "Group 1 Task",
                "preview_title": "Group1",
                "preview_description": "Assigned to group 1",
                "category": "PURCHASE_ORDER",
                "context_panel_type": "DOCUMENT_COMPARE",
                "context_panel_params": None,
            },
            status=TaskStatus.ON_HOLD.value,
            model_type=ContentType.objects.get_for_model(self.order),
            model_id=str(self.order.id),
        )

        # Task 3: Assigned to group2 (user is member)
        Task.objects.create(
            user_group=group2,
            task_type_v2=self.task_type_v2_po_approval_request,
            task_config={
                "title": "Group 2 Task",
                "preview_title": "Group2",
                "preview_description": "Assigned to group 2",
                "category": "PURCHASE_ORDER",
                "context_panel_type": "PO_DETAILS",
                "context_panel_params": None,
            },
            status=TaskStatus.COMPLETED.value,
            model_type=ContentType.objects.get_for_model(self.order),
            model_id=str(self.order.id),
        )

        # Make API request
        url = reverse("task-v2-list")
        response = self.client.get(url, format="json")

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn("status_counts", response.data)

        # Calculate expected counts
        # The user should see all tasks: existing ones + the 3 new ones
        all_user_tasks = Task.objects.filter(
            Q(task_type__isnull=True)
            & (Q(user=self.user) | Q(user_group__users__pk=self.user.pk))
        ).distinct()

        expected_total = all_user_tasks.count()

        # Sum of status counts should equal total distinct tasks
        status_counts_sum = sum(response.data["status_counts"].values())

        self.assertEqual(
            status_counts_sum,
            expected_total,
            f"Status counts sum ({status_counts_sum}) doesn't match actual distinct task count ({expected_total}). "
            f"This indicates duplicate counting when user belongs to multiple groups.",
        )

        # Also verify the paginated results count matches when not filtered
        total_pages = (expected_total + 24) // 25  # Page size is 25
        if total_pages > 1:
            # Get all pages to count total results
            all_results = []
            for page in range(1, total_pages + 1):
                page_response = self.client.get(url, {"page": page}, format="json")
                all_results.extend(page_response.data["results"])

            self.assertEqual(
                len(set(task["id"] for task in all_results)),
                expected_total,
                "Paginated results contain duplicates",
            )

        # Test with filters to ensure counts remain accurate
        # Filter by status
        response_pending = self.client.get(
            url, {"status": TaskStatus.PENDING.value}, format="json"
        )

        # Status counts should still sum to total (ignoring status filter for counts)
        status_counts_with_filter = sum(response_pending.data["status_counts"].values())
        self.assertEqual(
            status_counts_with_filter,
            expected_total,
            "Status counts changed when status filter was applied",
        )

        # Filter by model_type
        model_type = self.order._meta.model_name
        response_model = self.client.get(url, {"model_type": model_type}, format="json")

        # Count should match tasks for this model type only
        expected_model_tasks = all_user_tasks.filter(
            model_type=ContentType.objects.get_for_model(self.order)
        ).count()

        status_counts_model = sum(response_model.data["status_counts"].values())
        self.assertEqual(
            status_counts_model,
            expected_model_tasks,
            "Status counts don't match filtered model type tasks",
        )

    # TODO: Add tests when these are implemented.
    # def test_filter_tasks_v2_by_model_type_and_id(self):
    #     # Ensure tasks have task_type for V2 viewset
    #     for task in self.tasks.values():
    #         task.task_type = self.task_type
    #         task.save()

    #     url = reverse("taskv2-list")
    #     response = self.client.get(
    #         url,
    #         {
    #             "model_type": ContentType.objects.get_for_model(self.order).model,
    #             "model_id": self.order.pk,
    #         },
    #         format="json",
    #     )
    #     self.assertEqual(response.status_code, status.HTTP_200_OK)
    #     self.assertGreaterEqual(response.data["count"], 1)

    # def test_update_task_v2(self):
    #     # Create a task with task_type for V2 viewset
    #     task = self.tasks["PO_APPROVAL_REQUEST"]
    #     task.task_type = self.task_type
    #     task.save()

    #     url = reverse("taskv2-detail", args=[task.id])
    #     data = {"status": TaskStatus.COMPLETED.value}
    #     response = self.client.patch(url, data, format="json")
    #     self.assertEqual(response.status_code, status.HTTP_200_OK)
    #     task.refresh_from_db()
    #     self.assertEqual(task.status, TaskStatus.COMPLETED.value)

    # def test_task_v2_only_returns_tasks_with_task_type(self):
    #     # Create a task without task_type
    #     task_without_type = self.tasks["PO_APPROVAL_REQUEST"]
    #     task_without_type.task_type = None
    #     task_without_type.save()

    #     # Create a task with task_type
    #     task_with_type = self.tasks["INVOICE_APPROVAL_REQUEST"]
    #     task_with_type.task_type = self.task_type
    #     task_with_type.save()

    #     url = reverse("taskv2-list")
    #     response = self.client.get(url, format="json")
    #     self.assertEqual(response.status_code, status.HTTP_200_OK)

    #     # Check that only tasks with task_type are returned
    #     task_ids = [task["id"] for task in response.data["results"]]
    #     self.assertIn(task_with_type.id, task_ids)
    #     self.assertNotIn(task_without_type.id, task_ids)

    def test_status_counts_with_multi_group_membership(self):
        """
        Test that status counts are accurate when a user belongs to multiple groups
        and tasks are assigned both to the user directly and to their groups.
        
        This test prevents regression of the overcounting bug where tasks would
        be counted multiple times in status aggregations.
        """
        from didero.users.models import DideroUserGroup

        # Create two user groups for the same team
        group1 = DideroUserGroup.objects.create(
            name="Group 1",
            team=self.team
        )
        group2 = DideroUserGroup.objects.create(
            name="Group 2", 
            team=self.team
        )

        # Add the user to both groups
        group1.users.add(self.user)
        group2.users.add(self.user)

        # Create a task assigned directly to the user
        direct_task = Task.objects.create(
            user=self.user,  # Directly assigned to user
            task_type_v2=self.task_type_v2_po_approval_request,
            task_config={
                "title": "Direct Task",
                "preview_title": "Direct",
                "preview_description": "Task assigned directly to user",
                "category": "PURCHASE_ORDER",
                "context_panel_type": "PO_DETAILS",
                "context_panel_params": None,
            },
            status=TaskStatus.PENDING.value,
            model_type=ContentType.objects.get_for_model(self.order),
            model_id=str(self.order.id),
        )

        # Create a task assigned to group1 (user is member)
        group1_task = Task.objects.create(
            user_group=group1,  # Assigned to group1
            task_type_v2=self.task_type_v2_po_approval_request,
            task_config={
                "title": "Group 1 Task",
                "preview_title": "Group1",
                "preview_description": "Task assigned to group1",
                "category": "PURCHASE_ORDER",
                "context_panel_type": "PO_DETAILS",
                "context_panel_params": None,
            },
            status=TaskStatus.COMPLETED.value,
            model_type=ContentType.objects.get_for_model(self.order),
            model_id=str(self.order.id),
        )

        # Create a task assigned to group2 (user is member)
        group2_task = Task.objects.create(
            user_group=group2,  # Assigned to group2
            task_type_v2=self.task_type_v2_po_approval_request,
            task_config={
                "title": "Group 2 Task",
                "preview_title": "Group2",
                "preview_description": "Task assigned to group2",
                "category": "PURCHASE_ORDER",
                "context_panel_type": "PO_DETAILS",
                "context_panel_params": None,
            },
            status=TaskStatus.ON_HOLD.value,
            model_type=ContentType.objects.get_for_model(self.order),
            model_id=str(self.order.id),
        )

        # Create a task assigned to both user AND group1
        # This is the problematic case that caused overcounting
        dual_assignment_task = Task.objects.create(
            user=self.user,  # Assigned to user
            user_group=group1,  # AND to group1 (which user is a member of)
            task_type_v2=self.task_type_v2_po_approval_request,
            task_config={
                "title": "Dual Assignment Task",
                "preview_title": "Dual",
                "preview_description": "Task assigned to both user and group",
                "category": "PURCHASE_ORDER",
                "context_panel_type": "PO_DETAILS",
                "context_panel_params": None,
            },
            status=TaskStatus.PENDING.value,
            model_type=ContentType.objects.get_for_model(self.order),
            model_id=str(self.order.id),
        )

        # Get the task list response
        url = reverse("task-v2-list")
        response = self.client.get(url, format="json")

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn("status_counts", response.data)

        # Verify status counts
        status_counts = response.data["status_counts"]
        
        # Count expected tasks by status:
        # PENDING: direct_task + dual_assignment_task = 2 tasks
        # COMPLETED: group1_task = 1 task  
        # ON_HOLD: group2_task = 1 task
        expected_pending = 2  # direct_task + dual_assignment_task
        expected_completed = 1  # group1_task
        expected_on_hold = 1  # group2_task
        
        self.assertEqual(status_counts[TaskStatus.PENDING.value], expected_pending)
        self.assertEqual(status_counts[TaskStatus.COMPLETED.value], expected_completed)
        self.assertEqual(status_counts[TaskStatus.ON_HOLD.value], expected_on_hold)

        # Verify total count matches sum of status counts
        # The dual_assignment_task should only be counted once despite being
        # accessible via both user assignment and group membership
        total_expected = expected_pending + expected_completed + expected_on_hold
        total_from_counts = sum(status_counts.values())
        self.assertEqual(total_from_counts, total_expected)

        # Also verify the actual task count in the response matches
        # Each unique task should appear exactly once in the results
        self.assertEqual(response.data["count"], total_expected)

        # Verify each task is accessible to the user exactly once
        task_ids_in_response = {task["id"] for task in response.data["results"]}
        expected_task_ids = {
            direct_task.id,
            group1_task.id, 
            group2_task.id,
            dual_assignment_task.id
        }
        self.assertEqual(task_ids_in_response, expected_task_ids)
