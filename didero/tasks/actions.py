from typing import Any, Callable, Dict, cast

import structlog
from django.utils import timezone

from didero.tasks.schemas import (
    ActionHandlerContext,
    ActionHandlerResult,
    TaskActionType,
)
from didero.users.models import User

logger = structlog.get_logger(__name__)


def update_netsuite(
    handler_context: ActionHandlerContext, params: dict[str, Any]
) -> ActionHandlerResult:
    """
    Update shipment information in Netsuite for a purchase order.

    Required params:
    - purchase_order_id: ID of the purchase order to update in Netsuite
    - shipment_id: ID of the shipment to update in Netsuite

    Optional:
    - task_action_id: ID of the TaskAction to update status when job completes

    Returns:
        ActionHandlerResult: Result with status and message
    """
    from didero.orders.models import PurchaseOrder

    logger.info(
        "Update Netsuite Action Executed",
        handler_context=handler_context,
        params=params,
    )

    # Validate required parameters
    if "purchase_order_id" not in params:
        return ActionHandlerResult(
            errors=["Missing required parameter: purchase_order_id"], result=None
        )

    if "shipment_id" not in params:
        return ActionHandlerResult(
            errors=["Missing required parameter: shipment_id"], result=None
        )

    purchase_order_id = params["purchase_order_id"]
    shipment_id = params["shipment_id"]

    try:
        # Get the purchase order directly
        try:
            purchase_order = PurchaseOrder.objects.get(id=purchase_order_id)
        except PurchaseOrder.DoesNotExist:
            return ActionHandlerResult(
                errors=[f"Purchase order {purchase_order_id} not found"], result=None
            )

        # Call the method that handles the RPA job submission
        job_id = purchase_order.update_netsuite_shipment_workflow_completed(
            shipment_id=shipment_id
        )

        # Return success result
        logger.info(
            "Netsuite shipment update job submitted successfully",
            purchase_order_id=purchase_order_id,
            shipment_id=shipment_id,
            rpa_job_id=job_id,
        )
        return ActionHandlerResult(
            errors=None,
            result={
                "message": "Netsuite shipment update job submitted successfully",
                "purchase_order_id": purchase_order_id,
                "shipment_id": shipment_id,
                "rpa_job_id": job_id,
            },
        )

    except Exception as e:
        logger.exception("Failed to update Netsuite", error=str(e), params=params)
        return ActionHandlerResult(
            errors=[f"Failed to update Netsuite: {str(e)}"], result=None
        )


def approve_purchase_order(
    handler_context: ActionHandlerContext,
    params: dict[str, Any],
) -> ActionHandlerResult:
    # Import here to avoid circular imports
    from didero.orders.models import PurchaseOrder
    from didero.tasks.models import TaskAction, TaskActionStatus

    logger.info(
        "Approve Purchase Order Action Executed",
        handler_context=handler_context,
        params=params,
    )

    try:
        order_id = params["order_id"]
        approval_id = params["approval_id"]
    except KeyError as e:
        # Remove quotes from the KeyError message
        param_name = str(e).replace("'", "")
        return ActionHandlerResult(
            errors=[f"Missing required parameter: {param_name}"], result=None
        )

    try:
        purchase_order = PurchaseOrder.objects.get(pk=order_id)
    except PurchaseOrder.DoesNotExist:
        return ActionHandlerResult(
            errors=[f"Purchase order {order_id} not found"], result=None
        )

    if not approval_id:
        return ActionHandlerResult(
            errors=["approval_id is required for approving a purchase order"],
            result=None,
        )

    # Approve the purchase order
    purchase_order.approve_by_user(
        user_actor=handler_context["user"], approval_id=approval_id
    )

    # Invalidate the deny action for this task
    if handler_context["task_action_id"]:
        try:
            # Find the task associated with this action
            current_action = TaskAction.objects.get(
                pk=handler_context["task_action_id"]
            )
            if current_action and current_action.task:
                # Find the deny action for this task and invalidate it
                try:
                    deny_action = current_action.task.actions.get(
                        action_type__name="DENY_PURCHASE_ORDER",
                        status=TaskActionStatus.PENDING,
                    )
                    deny_action.status = TaskActionStatus.INVALIDATED
                    deny_action.save(update_fields=["status", "modified_at"])
                except TaskAction.DoesNotExist:
                    # No deny action to invalidate
                    pass
        except Exception as e:
            logger.error(
                "Failed to invalidate deny action",
                error=str(e),
                task_action_id=handler_context["task_action_id"],
            )

    return ActionHandlerResult(errors=None, result=None)


def deny_purchase_order(
    handler_context: ActionHandlerContext,
    params: dict[str, Any],
) -> ActionHandlerResult:
    # Import here to avoid circular imports
    from didero.orders.models import PurchaseOrder
    from didero.tasks.models import TaskAction, TaskActionStatus

    logger.info(
        "Deny Purchase Order Action Executed",
        handler_context=handler_context,
        params=params,
    )

    try:
        order_id = params["order_id"]
        approval_id = params["approval_id"]
    except KeyError as e:
        # Remove quotes from the KeyError message
        param_name = str(e).replace("'", "")
        return ActionHandlerResult(
            errors=[f"Missing required parameter: {param_name}"], result=None
        )

    try:
        purchase_order = PurchaseOrder.objects.get(pk=order_id)
    except PurchaseOrder.DoesNotExist:
        return ActionHandlerResult(
            errors=[f"Purchase order {order_id} not found"], result=None
        )

    if not approval_id:
        return ActionHandlerResult(
            errors=["approval_id is required for denying a purchase order"],
            result=None,
        )

    # Deny the purchase order
    purchase_order.deny_by_user(
        user_actor=handler_context["user"], approval_id=approval_id
    )

    # Invalidate the approve action for this task
    if handler_context["task_action_id"]:
        try:
            # Find the task associated with this action
            current_action = TaskAction.objects.get(
                pk=handler_context["task_action_id"]
            )
            if current_action and current_action.task:
                # Find the approve action for this task and invalidate it
                try:
                    approve_action = current_action.task.actions.get(
                        action_type__name="APPROVE_PURCHASE_ORDER",
                        status=TaskActionStatus.PENDING,
                    )
                    approve_action.status = TaskActionStatus.INVALIDATED
                    approve_action.save(update_fields=["status", "modified_at"])
                except TaskAction.DoesNotExist:
                    # No approve action to invalidate
                    pass
        except Exception as e:
            logger.error(
                "Failed to invalidate approve action",
                error=str(e),
                task_action_id=handler_context["task_action_id"],
            )

    return ActionHandlerResult(errors=None, result=None)


def confirm_purchase_order(
    handler_context: ActionHandlerContext,
    params: dict[str, Any],
) -> ActionHandlerResult:
    """
    Confirm a purchase order by transitioning it from Draft to Issued.
    Note: PO Approval depends on whether the Team has a PO Approval workflow enabled.
    We should only set to APPROVED through that workflow if it exists.
    For now we're keeping it simple with just ISSUED status.

    Required params:
    - order_id: ID of the purchase order to confirm

    Returns:
        ActionHandlerResult: Result with any errors or success
    """
    # Import here to avoid circular imports
    from didero.orders.models import PurchaseOrder
    from didero.orders.schemas import PurchaseOrderStatus
    from didero.tasks.models import TaskAction, TaskActionStatus

    logger.info(
        "Confirm Purchase Order Action Executed",
        handler_context=handler_context,
        params=params,
    )

    try:
        order_id = params["order_id"]
    except KeyError as e:
        # Remove quotes from the KeyError message
        param_name = str(e).replace("'", "")
        return ActionHandlerResult(
            errors=[f"Missing required parameter: {param_name}"], result=None
        )

    try:
        purchase_order = PurchaseOrder.objects.get(pk=order_id)
    except PurchaseOrder.DoesNotExist:
        return ActionHandlerResult(
            errors=[f"Purchase order {order_id} not found"], result=None
        )

    # Transition PO status to Issued and make it editable/visible to all users
    purchase_order.order_status = PurchaseOrderStatus.ISSUED.value
    purchase_order.is_po_editable = True
    purchase_order.save()

    logger.info(
        "Purchase order confirmed and issued",
        order_id=order_id,
        po_number=purchase_order.po_number,
        user_id=handler_context["user"].pk,
    )

    # Invalidate the cancel action for this task
    if handler_context["task_action_id"]:
        try:
            # Find the task associated with this action
            current_action = TaskAction.objects.get(
                pk=handler_context["task_action_id"]
            )
            if current_action and current_action.task:
                # Find the cancel action for this task and invalidate it
                try:
                    cancel_action = current_action.task.actions.get(
                        action_type__name="CANCEL_PURCHASE_ORDER",
                        status=TaskActionStatus.PENDING,
                    )
                    cancel_action.status = TaskActionStatus.INVALIDATED
                    cancel_action.save()
                except TaskAction.DoesNotExist:
                    # No cancel action to invalidate
                    pass
        except Exception as e:
            logger.error(
                "Failed to invalidate cancel action",
                error=str(e),
                task_action_id=handler_context["task_action_id"],
            )

    return ActionHandlerResult(errors=None, result=None)


def cancel_purchase_order(
    handler_context: ActionHandlerContext,
    params: dict[str, Any],
) -> ActionHandlerResult:
    """
    Keep purchase order in Draft status without transitioning to Canceled.
    This effectively cancels the workflow but keeps the PO in draft state.


    #TODO: Current implementaiton is the person can cancel the PO -- which keeps it in draft state,
           and the person can "edit" the PO that gives them the ability to add comments and re-open the PO to edit.

    Required params:
    - order_id: ID of the purchase order to cancel

    Returns:
        ActionHandlerResult: Result with any errors or success
    """
    # Import here to avoid circular imports
    from didero.orders.models import PurchaseOrder
    from didero.tasks.models import TaskAction, TaskActionStatus

    logger.info(
        "Cancel Purchase Order Action Executed",
        handler_context=handler_context,
        params=params,
    )

    try:
        order_id = params["order_id"]
    except KeyError as e:
        # Remove quotes from the KeyError message
        param_name = str(e).replace("'", "")
        return ActionHandlerResult(
            errors=[f"Missing required parameter: {param_name}"], result=None
        )

    try:
        purchase_order = PurchaseOrder.objects.get(pk=order_id)
    except PurchaseOrder.DoesNotExist:
        return ActionHandlerResult(
            errors=[f"Purchase order {order_id} not found"], result=None
        )

    # No status check - we can cancel from any status
    # We don't change the status, just mark it as not editable
    logger.info(f"PO remains in {purchase_order.order_status} status")

    # Make PO not editable
    purchase_order.is_po_editable = False
    purchase_order.save()

    logger.info(
        "Purchase order canceled",
        order_id=order_id,
        po_number=purchase_order.po_number,
        user_id=handler_context["user"].pk,
    )

    # Invalidate the confirm action for this task
    if handler_context["task_action_id"]:
        try:
            # Find the task associated with this action
            current_action = TaskAction.objects.get(
                pk=handler_context["task_action_id"]
            )
            if current_action and current_action.task:
                # Find the confirm action for this task and invalidate it
                try:
                    confirm_action = current_action.task.actions.get(
                        action_type__name="CONFIRM_PURCHASE_ORDER",
                        status=TaskActionStatus.PENDING,
                    )
                    confirm_action.status = TaskActionStatus.INVALIDATED
                    confirm_action.save()
                except TaskAction.DoesNotExist:
                    # No confirm action to invalidate
                    pass
        except Exception as e:
            logger.error(
                "Failed to invalidate confirm action",
                error=str(e),
                task_action_id=handler_context["task_action_id"],
            )

    return ActionHandlerResult(errors=None, result=None)


def add_comment(
    handler_context: ActionHandlerContext, params: dict[str, Any]
) -> ActionHandlerResult:
    logger.info(
        "Add Comment Action Executed", handler_context=handler_context, params=params
    )

    # Get the parent object type from parameters
    parent_object_type = params["parent_object_type"].lower()
    parent_object_id = params["parent_object_id"]
    comment_text = params["comment"]
    user = handler_context["user"]

    try:
        if parent_object_type == "purchaseorder":
            # Import here to avoid circular imports
            from didero.orders.models import PurchaseOrder, PurchaseOrderComment

            parent_obj = PurchaseOrder.objects.get(pk=parent_object_id)
            comment = PurchaseOrderComment.objects.create(
                purchase_order=parent_obj, created_by=user, comment=comment_text
            )

        elif parent_object_type == "document":
            from didero.documents.models import Document, DocumentComment

            parent_obj = Document.objects.get(pk=parent_object_id)
            comment = DocumentComment.objects.create(
                document=parent_obj, created_by=user, comment=comment_text
            )

        elif parent_object_type == "supplier":
            from didero.suppliers.models import Supplier, SupplierComment

            parent_obj = Supplier.objects.get(pk=parent_object_id)
            comment = SupplierComment.objects.create(
                supplier=parent_obj, created_by=user, comment=comment_text
            )

        else:
            return ActionHandlerResult(
                errors=[f"Unsupported object type: {parent_object_type}"], result=None
            )

        # All Django models have a pk (primary key) property
        return ActionHandlerResult(errors=None, result={"comment_id": comment.pk})

    except Exception as e:
        logger.error(
            "Add Comment Action failed",
            handler_context=handler_context,
            params=params,
            error=str(e),
        )
        return ActionHandlerResult(
            errors=[f"Failed to add comment: {str(e)}"], result=None
        )


def auto_comment(
    handler_context: ActionHandlerContext, params: dict[str, Any]
) -> ActionHandlerResult:
    """
    Auto comment action handler that adds a predefined comment to a parent object
    and automatically completes the task.

    Required params:
    - parent_object_type: Type of the parent object (purchaseorder, document, supplier)
    - parent_object_id: ID of the parent object
    - comment_text: The comment text to add

    Returns:
        ActionHandlerResult: Result with any errors or success
    """
    logger.info(
        "Auto Comment Action Executed", handler_context=handler_context, params=params
    )

    # Get the parent object type from parameters
    try:
        parent_object_type = params["parent_object_type"].lower()
        parent_object_id = params["parent_object_id"]
        comment_text = params["comment_text"]
    except KeyError as e:
        param_name = str(e).replace("'", "")
        return ActionHandlerResult(
            errors=[f"Missing required parameter: {param_name}"], result=None
        )

    user = handler_context["user"]

    try:
        if parent_object_type == "purchaseorder":
            # Import here to avoid circular imports
            from didero.orders.models import PurchaseOrder, PurchaseOrderComment

            parent_obj = PurchaseOrder.objects.get(pk=parent_object_id)
            comment = PurchaseOrderComment.objects.create(
                purchase_order=parent_obj, created_by=user, comment=comment_text
            )

        elif parent_object_type == "document":
            from didero.documents.models import Document, DocumentComment

            parent_obj = Document.objects.get(pk=parent_object_id)
            comment = DocumentComment.objects.create(
                document=parent_obj, created_by=user, comment=comment_text
            )

        elif parent_object_type == "supplier":
            from didero.suppliers.models import Supplier, SupplierComment

            parent_obj = Supplier.objects.get(pk=parent_object_id)
            comment = SupplierComment.objects.create(
                supplier=parent_obj, created_by=user, comment=comment_text
            )

        else:
            return ActionHandlerResult(
                errors=[f"Unsupported object type: {parent_object_type}"], result=None
            )

        logger.info(
            "Auto comment added successfully",
            parent_object_type=parent_object_type,
            parent_object_id=parent_object_id,
            comment_id=comment.pk,
            user_id=user.pk,
        )

        # Mark the task as complete after successfully adding the comment
        if handler_context["task_action_id"]:
            try:
                from didero.tasks.models import TaskAction, TaskStatus

                # Get the current task action and its associated task
                current_action = TaskAction.objects.get(
                    pk=handler_context["task_action_id"]
                )
                if current_action and current_action.task:
                    # Mark the task as completed
                    current_action.task.status = TaskStatus.COMPLETED.value
                    current_action.task.save()

                    logger.info(
                        "Task marked as complete after auto comment",
                        task_id=current_action.task.pk,
                        task_action_id=current_action.pk,
                        comment_id=comment.pk,
                    )
            except Exception as e:
                logger.error(
                    "Failed to mark task as complete after auto comment",
                    error=str(e),
                    task_action_id=handler_context["task_action_id"],
                    comment_id=comment.pk,
                )
                # Don't fail the entire action if we can't mark the task complete
                # The comment was still added successfully

        # All Django models have a pk (primary key) property
        return ActionHandlerResult(errors=None, result={"comment_id": comment.pk})

    except Exception as e:
        logger.error(
            "Auto Comment Action failed",
            handler_context=handler_context,
            params=params,
            error=str(e),
        )
        return ActionHandlerResult(
            errors=[f"Failed to add auto comment: {str(e)}"], result=None
        )


def approve_freight_charge(
    handler_context: ActionHandlerContext, params: dict[str, Any]
) -> ActionHandlerResult:
    """
    Approve freight charge by adding a comment to the purchase order.

    Required params:
    - purchase_order_id: ID of the purchase order
    - freight_charge: Freight charge amount (optional)

    Returns:
        ActionHandlerResult: Result with comment_id if successful
    """
    logger.info(
        "Approve Freight Charge Action Executed",
        handler_context=handler_context,
        params=params,
    )

    # Validate required parameters
    if "purchase_order_id" not in params:
        return ActionHandlerResult(
            errors=["Missing required parameter: purchase_order_id"], result=None
        )

    purchase_order_id = params["purchase_order_id"]
    user = handler_context["user"]

    # Predefined comment text with optional freight charge
    if "freight_charge" in params and params["freight_charge"]:
        freight_charge = params["freight_charge"]
        comment_text = f"Please update the purchase order to include the freight charge of ${freight_charge} from the supplier."
    else:
        comment_text = "Please update the purchase order to match order acknowledgement/invoice from the supplier."

    try:
        # Import here to avoid circular imports
        from didero.orders.models import PurchaseOrder, PurchaseOrderComment

        # Fetch the purchase order
        purchase_order = PurchaseOrder.objects.get(pk=purchase_order_id)

        # Create the comment
        comment = PurchaseOrderComment.objects.create(
            purchase_order=purchase_order, created_by=user, comment=comment_text
        )

        return ActionHandlerResult(errors=None, result={"comment_id": comment.pk})

    except Exception as e:
        logger.error(
            "Error approving freight charge",
            error=str(e),
            purchase_order_id=purchase_order_id,
        )
        return ActionHandlerResult(errors=[str(e)], result=None)


def approve_price_change(
    handler_context: ActionHandlerContext, params: dict[str, Any]
) -> ActionHandlerResult:
    """
    Approve price change by adding a comment to the purchase order.

    Required params:
    - purchase_order_id: ID of the purchase order
    - price_change: Price change details
    - system_name: Name of the system to make the price change

    Returns:
        ActionHandlerResult: Result with comment_id if successful
    """
    logger.info(
        "Approve Price Change Action Executed",
        handler_context=handler_context,
        params=params,
    )

    # Validate required parameters
    if "purchase_order_id" not in params:
        return ActionHandlerResult(
            errors=["Missing required parameter: purchase_order_id"], result=None
        )

    if "price_change" not in params:
        return ActionHandlerResult(
            errors=["Missing required parameter: price_change"], result=None
        )

    if "system_name" not in params:
        return ActionHandlerResult(
            errors=["Missing required parameter: system_name"], result=None
        )

    purchase_order_id = params["purchase_order_id"]
    system_name = params["system_name"]
    user = handler_context["user"]

    # Predefined comment text
    comment_text = f"Please update the purchase order and item purchase price in {system_name} to match the order confirmation from the supplier."

    try:
        # Import here to avoid circular imports
        from didero.orders.models import PurchaseOrder, PurchaseOrderComment

        # Fetch the purchase order
        purchase_order = PurchaseOrder.objects.get(pk=purchase_order_id)

        # Create the comment
        comment = PurchaseOrderComment.objects.create(
            purchase_order=purchase_order, created_by=user, comment=comment_text
        )

        return ActionHandlerResult(errors=None, result={"comment_id": comment.pk})

    except Exception as e:
        logger.error(
            "Error approving price change",
            error=str(e),
            purchase_order_id=purchase_order_id,
        )
        return ActionHandlerResult(errors=[str(e)], result=None)


def approve_address_change(
    handler_context: ActionHandlerContext, params: dict[str, Any]
) -> ActionHandlerResult:
    """
    Approve address change by adding a comment to the purchase order.

    Required params:
    - purchase_order_id: ID of the purchase order
    - address_change: Address change details (optional)

    Returns:
        ActionHandlerResult: Result with comment_id if successful
    """
    logger.info(
        "Approve Address Change Action Executed",
        handler_context=handler_context,
        params=params,
    )

    # Validate required parameters
    if "purchase_order_id" not in params:
        return ActionHandlerResult(
            errors=["Missing required parameter: purchase_order_id"], result=None
        )

    purchase_order_id = params["purchase_order_id"]
    user = handler_context["user"]

    # Predefined comment text
    comment_text = "Please update the purchase order to match the shipment address from the supplier."

    try:
        # Import here to avoid circular imports
        from didero.orders.models import PurchaseOrder, PurchaseOrderComment

        # Fetch the purchase order
        purchase_order = PurchaseOrder.objects.get(pk=purchase_order_id)

        # Create the comment
        comment = PurchaseOrderComment.objects.create(
            purchase_order=purchase_order, created_by=user, comment=comment_text
        )

        return ActionHandlerResult(errors=None, result={"comment_id": comment.pk})

    except Exception as e:
        logger.error(
            "Error approving address change",
            error=str(e),
            purchase_order_id=purchase_order_id,
        )
        return ActionHandlerResult(errors=[str(e)], result=None)


def confirm_shipment(
    handler_context: ActionHandlerContext,
    params: dict[str, Any],
) -> ActionHandlerResult:
    """
    Confirm shipment and trigger ERP sync if configured.

    This action now handles both legacy RPA and new ERP sync approaches.
    ERP sync errors are non-blocking - task completes but error is visible.

    Required params:
    - purchase_order_id: ID of the purchase order
    - shipment_id: ID of the shipment

    Returns:
        ActionHandlerResult: Result with any errors or success
    """
    from didero.orders.models import PurchaseOrder, Shipment
    from didero.tasks.models import TaskAction, TaskActionStatus

    logger.info(
        "Confirm Shipment Action Executed",
        handler_context=handler_context,
        params=params,
    )

    try:
        purchase_order_id = params["purchase_order_id"]
        shipment_id = params["shipment_id"]
    except KeyError as e:
        param_name = str(e).replace("'", "")
        return ActionHandlerResult(
            errors=[f"Missing required parameter: {param_name}"], result=None
        )

    try:
        purchase_order = PurchaseOrder.objects.select_related("team").get(
            pk=purchase_order_id
        )
        Shipment.objects.get(id=shipment_id)  # Verify shipment exists
    except PurchaseOrder.DoesNotExist:
        return ActionHandlerResult(
            errors=[f"Purchase order {purchase_order_id} not found"], result=None
        )
    except Shipment.DoesNotExist:
        return ActionHandlerResult(
            errors=[f"Shipment {shipment_id} not found"], result=None
        )

    # Check if team has new ERP integration
    from didero.integrations.models import ERPIntegrationConfig

    has_erp_integration = ERPIntegrationConfig.objects.filter(
        team=purchase_order.team, enabled=True
    ).exists()

    if has_erp_integration:
        # Use new ERP sync system
        from asgiref.sync import async_to_sync

        from didero.workflows.core.activities.erp_sync import sync_erp_purchase_order

        logger.info(
            "Using new ERP integration for shipment confirmation",
            po_number=purchase_order.po_number,
            shipment_id=shipment_id,
        )

        # Execute ERP sync
        sync_result = async_to_sync(sync_erp_purchase_order)(
            {
                "shipment_id": shipment_id,
                "team_id": purchase_order.team.id,
                "sync_mode": "manual",
            }
        )

        # Invalidate the cancel action
        # Fixed: handler_context.invalidate_action_by_action_type() was never implemented
        # Using manual pattern from other action handlers
        if handler_context.get("task_action_id"):
            try:
                from didero.tasks.models import TaskAction, TaskActionStatus

                current_action = TaskAction.objects.get(
                    pk=handler_context["task_action_id"]
                )
                if current_action and current_action.task:
                    try:
                        cancel_action = current_action.task.actions.get(
                            action_type__name="CANCEL_SHIPMENT",
                            status=TaskActionStatus.PENDING,
                        )
                        cancel_action.status = TaskActionStatus.INVALIDATED
                        cancel_action.save(update_fields=["status", "modified_at"])
                        logger.info(
                            "Invalidated cancel shipment action",
                            task_id=current_action.task.id,
                        )
                    except TaskAction.DoesNotExist:
                        logger.debug("No cancel shipment action to invalidate")
            except Exception as e:
                logger.error("Failed to invalidate cancel action", error=str(e))

        if sync_result["success"]:
            # Success - complete the task
            message = (
                f"Shipment confirmed and synced to {sync_result['erp_system'].upper()}"
            )
            if sync_result["synced_fields"]:
                message += f" (fields: {', '.join(sync_result['synced_fields'])})"

            logger.info(
                "Shipment confirmed with successful ERP sync",
                purchase_order_id=purchase_order_id,
                shipment_id=shipment_id,
                erp_system=sync_result["erp_system"],
            )

            return ActionHandlerResult(errors=None, result={"message": message})
        else:
            # Failure - but still complete the task (non-blocking)
            # Error notification task was already created by sync activity
            error_msg = sync_result.get("error_message", "Unknown error")

            logger.warning(
                "Shipment confirmed but ERP sync failed",
                purchase_order_id=purchase_order_id,
                shipment_id=shipment_id,
                error=error_msg,
            )

            # Return success but with warning message
            return ActionHandlerResult(
                errors=None,
                result={
                    "message": f"Shipment confirmed. ERP sync failed: {error_msg}. "
                    f"An error notification has been created for follow-up."
                },
            )

    else:
        # Fall back to legacy RPA method
        logger.info(
            "Using legacy RPA for shipment confirmation",
            po_number=purchase_order.po_number,
            shipment_id=shipment_id,
        )

        job_id = purchase_order.update_netsuite_shipment_workflow_completed(
            shipment_id=shipment_id
        )

        # Invalidate the cancel action (legacy RPA path)
        if handler_context.get("task_action_id"):
            try:
                from didero.tasks.models import TaskAction, TaskActionStatus

                current_action = TaskAction.objects.get(
                    pk=handler_context["task_action_id"]
                )
                if current_action and current_action.task:
                    try:
                        cancel_action = current_action.task.actions.get(
                            action_type__name="CANCEL_SHIPMENT",
                            status=TaskActionStatus.PENDING,
                        )
                        cancel_action.status = TaskActionStatus.INVALIDATED
                        cancel_action.save(update_fields=["status", "modified_at"])
                        logger.info(
                            "Invalidated cancel shipment action",
                            task_id=current_action.task.id,
                        )
                    except TaskAction.DoesNotExist:
                        logger.debug("No cancel shipment action to invalidate")
            except Exception as e:
                logger.error("Failed to invalidate cancel action", error=str(e))

        return ActionHandlerResult(
            errors=None,
            result={
                "rpa_job_id": job_id,
                "message": "Shipment confirmed and NetSuite update job submitted (RPA)",
            },
        )


def cancel_shipment(
    handler_context: ActionHandlerContext,
    params: dict[str, Any],
) -> ActionHandlerResult:
    """
    Cancel a shipment without updating NetSuite.

    Required params:
    - purchase_order_id: ID of the purchase order
    - shipment_id: ID of the shipment

    Returns:
        ActionHandlerResult: Result with any errors or success
    """
    from didero.orders.models import PurchaseOrder, PurchaseOrderComment, Shipment
    from didero.orders.schemas import ShipmentStatus
    from didero.tasks.models import TaskAction, TaskActionStatus

    logger.info(
        "Cancel Shipment Action Executed",
        handler_context=handler_context,
        params=params,
    )

    try:
        purchase_order_id = params["purchase_order_id"]
        shipment_id = params["shipment_id"]
    except KeyError as e:
        param_name = str(e).replace("'", "")
        return ActionHandlerResult(
            errors=[f"Missing required parameter: {param_name}"], result=None
        )

    try:
        purchase_order = PurchaseOrder.objects.get(pk=purchase_order_id)
        shipment = Shipment.objects.get(pk=shipment_id)
    except (PurchaseOrder.DoesNotExist, Shipment.DoesNotExist) as e:
        return ActionHandlerResult(errors=[f"Object not found: {str(e)}"], result=None)

    # Mark the shipment as cancelled
    shipment.status = ShipmentStatus.CANCELED.value
    shipment.save(update_fields=["status", "modified_at"])

    # Add a comment to the purchase order
    PurchaseOrderComment.objects.create(
        purchase_order=purchase_order,
        created_by=handler_context["user"],
        comment=f"Shipment with tracking number {shipment.tracking_number or 'N/A'} was cancelled and not updated in NetSuite.",
    )

    # Update the purchase order status to account for the cancelled shipment
    Shipment.objects.update_purchase_order_status(purchase_order)  # type: ignore[attr-defined]

    # Invalidate the confirm action for this task
    if handler_context["task_action_id"]:
        try:
            # Find the task associated with this action
            current_action = TaskAction.objects.get(
                pk=handler_context["task_action_id"]
            )
            if current_action and current_action.task:
                # Find the confirm action for this task and invalidate it
                try:
                    confirm_action = current_action.task.actions.get(
                        action_type__name="CONFIRM_SHIPMENT",
                        status=TaskActionStatus.PENDING,
                    )
                    confirm_action.status = TaskActionStatus.INVALIDATED
                    confirm_action.save(update_fields=["status", "modified_at"])
                except TaskAction.DoesNotExist:
                    # No confirm action to invalidate
                    pass
        except Exception as e:
            logger.error(
                "Failed to invalidate confirm action",
                error=str(e),
                task_action_id=handler_context["task_action_id"],
            )

    logger.info(
        "Shipment cancelled",
        purchase_order_id=purchase_order_id,
        shipment_id=shipment_id,
    )

    return ActionHandlerResult(
        errors=None,
        result={
            "message": "Shipment cancelled",
            "purchase_order_id": purchase_order_id,
            "shipment_id": shipment_id,
        },
    )


def confirm_order_acknowledgement(
    handler_context: ActionHandlerContext,
    params: dict[str, Any],
) -> ActionHandlerResult:
    """
    Confirm an order acknowledgment, updating the purchase order status to AWAITING_SHIPMENT
    and adding a comment documenting the confirmation.

    Required params:
    - purchase_order_id: ID of the purchase order to update

    Returns:
        ActionHandlerResult: Result with any errors or success
    """
    # Import here to avoid circular imports
    from didero.orders.models import PurchaseOrder, PurchaseOrderComment
    from didero.orders.schemas import PurchaseOrderStatus
    from didero.tasks.models import TaskAction, TaskActionStatus

    logger.info(
        "Confirm Order Acknowledgement Action Executed",
        handler_context=handler_context,
        params=params,
    )

    try:
        purchase_order_id = params["purchase_order_id"]
    except KeyError as e:
        # Remove quotes from the KeyError message
        param_name = str(e).replace("'", "")
        return ActionHandlerResult(
            errors=[f"Missing required parameter: {param_name}"], result=None
        )

    try:
        purchase_order = PurchaseOrder.objects.get(pk=purchase_order_id)
    except PurchaseOrder.DoesNotExist:
        return ActionHandlerResult(
            errors=[f"Purchase order {purchase_order_id} not found"], result=None
        )

    # Store old status for activity log
    old_status = purchase_order.order_status

    # Update the purchase order status to AWAITING_SHIPMENT (no status check)
    purchase_order.order_status = PurchaseOrderStatus.AWAITING_SHIPMENT.value
    purchase_order.save()

    # Create activity log entry for status change
    from didero.activity_log.models import PurchaseOrderStatusUpdate

    PurchaseOrderStatusUpdate.objects.create(
        purchase_order=purchase_order,
        user_actor=handler_context["user"],
        system_actor=None,
        status_changed="order_status",
        old_status=old_status,
        new_status=purchase_order.order_status,
    )

    # Add a comment to document the confirmation
    comment_text = (
        f"Order acknowledgment confirmed. Purchase order {purchase_order.po_number} "
        f"status updated to AWAITING_SHIPMENT."
    )
    PurchaseOrderComment.objects.create(
        purchase_order=purchase_order,
        created_by=handler_context["user"],
        comment=comment_text,
    )

    logger.info(
        "Order acknowledgment confirmed",
        purchase_order_id=purchase_order_id,
        po_number=purchase_order.po_number,
        user_id=handler_context["user"].pk,
    )

    # Invalidate the reject action for this task
    if handler_context["task_action_id"]:
        try:
            # Find the task associated with this action
            current_action = TaskAction.objects.get(
                pk=handler_context["task_action_id"]
            )
            if current_action and current_action.task:
                # Find the reject action for this task and invalidate it
                try:
                    reject_action = current_action.task.actions.get(
                        action_type__name="REJECT_ORDER_ACKNOWLEDGEMENT",
                        status=TaskActionStatus.PENDING,
                    )
                    reject_action.status = TaskActionStatus.INVALIDATED
                    reject_action.save()
                except TaskAction.DoesNotExist:
                    # No reject action to invalidate
                    pass
        except Exception as e:
            logger.error(
                "Failed to invalidate reject action",
                error=str(e),
                task_action_id=handler_context["task_action_id"],
            )

    # Check if ERP sync is needed for date changes
    _check_and_trigger_oa_erp_sync(purchase_order, handler_context["user"])

    return ActionHandlerResult(errors=None, result=None)


def reject_order_acknowledgement(
    handler_context: ActionHandlerContext,
    params: dict[str, Any],
) -> ActionHandlerResult:
    """
    Reject an order acknowledgment, keeping the purchase order in its current state
    and adding a comment documenting the rejection.

    Required params:
    - purchase_order_id: ID of the purchase order

    Returns:
        ActionHandlerResult: Result with any errors or success
    """
    # Import here to avoid circular imports
    from didero.orders.models import PurchaseOrder, PurchaseOrderComment
    from didero.tasks.models import TaskAction, TaskActionStatus

    logger.info(
        "Reject Order Acknowledgement Action Executed",
        handler_context=handler_context,
        params=params,
    )

    try:
        purchase_order_id = params["purchase_order_id"]
    except KeyError as e:
        # Remove quotes from the KeyError message
        param_name = str(e).replace("'", "")
        return ActionHandlerResult(
            errors=[f"Missing required parameter: {param_name}"], result=None
        )

    try:
        purchase_order = PurchaseOrder.objects.get(pk=purchase_order_id)
    except PurchaseOrder.DoesNotExist:
        return ActionHandlerResult(
            errors=[f"Purchase order {purchase_order_id} not found"], result=None
        )

    # Add a comment documenting the rejection
    comment_text = (
        f"Order acknowledgment rejected. Purchase order {purchase_order.po_number} "
        f"will remain in {purchase_order.order_status} status. Further review required."
    )
    PurchaseOrderComment.objects.create(
        purchase_order=purchase_order,
        created_by=handler_context["user"],
        comment=comment_text,
    )

    logger.info(
        "Order acknowledgment rejected",
        purchase_order_id=purchase_order_id,
        po_number=purchase_order.po_number,
        user_id=handler_context["user"].pk,
    )

    # Invalidate the confirm action for this task
    if handler_context["task_action_id"]:
        try:
            # Find the task associated with this action
            current_action = TaskAction.objects.get(
                pk=handler_context["task_action_id"]
            )
            if current_action and current_action.task:
                # Find the confirm action for this task and invalidate it
                try:
                    confirm_action = current_action.task.actions.get(
                        action_type__name="CONFIRM_ORDER_ACKNOWLEDGEMENT",
                        status=TaskActionStatus.PENDING,
                    )
                    confirm_action.status = TaskActionStatus.INVALIDATED
                    confirm_action.save()
                except TaskAction.DoesNotExist:
                    # No confirm action to invalidate
                    pass
        except Exception as e:
            logger.error(
                "Failed to invalidate confirm action",
                error=str(e),
                task_action_id=handler_context["task_action_id"],
            )

    return ActionHandlerResult(errors=None, result=None)


def approve_oa_mismatch(
    handler_context: ActionHandlerContext,
    params: dict[str, Any],
) -> ActionHandlerResult:
    """
    Approve an order acknowledgment despite validation errors, updating the purchase order
    status to AWAITING_SHIPMENT and logging the approval with mismatch details.

    Required params:
    - purchase_order_id: ID of the purchase order
    - validation_error: Description of the validation error being approved

    Returns:
        ActionHandlerResult: Result with any errors or success
    """
    # Import here to avoid circular imports
    from didero.activity_log.models import PurchaseOrderStatusUpdate
    from didero.orders.models import PurchaseOrder, PurchaseOrderComment
    from didero.orders.schemas import PurchaseOrderStatus

    logger.info(
        "Approve OA Mismatch Action Executed",
        handler_context=handler_context,
        params=params,
    )

    try:
        purchase_order_id = params["purchase_order_id"]
        validation_error = params["validation_error"]
    except KeyError as e:
        param_name = str(e).replace("'", "")
        return ActionHandlerResult(
            errors=[f"Missing required parameter: {param_name}"], result=None
        )

    try:
        purchase_order = PurchaseOrder.objects.get(pk=purchase_order_id)
    except PurchaseOrder.DoesNotExist:
        return ActionHandlerResult(
            errors=[f"Purchase order {purchase_order_id} not found"], result=None
        )

    # Store old status for activity log
    old_status = purchase_order.order_status

    # Update the purchase order status to AWAITING_SHIPMENT
    purchase_order.order_status = PurchaseOrderStatus.AWAITING_SHIPMENT.value
    purchase_order.save()

    # Create activity log entry
    PurchaseOrderStatusUpdate.objects.create(
        purchase_order=purchase_order,
        user_actor=handler_context["user"],
        system_actor=None,
        status_changed="order_status",
        old_status=old_status,
        new_status=purchase_order.order_status,
    )

    # Add a detailed comment about the mismatch approval
    comment_text = (
        f"Order acknowledgment mismatch approved despite validation errors. "
        f"Validation issue: {validation_error}. "
        f"Purchase order {purchase_order.po_number} status updated to AWAITING_SHIPMENT. "
        f"Please ensure any discrepancies are resolved with the supplier."
    )

    PurchaseOrderComment.objects.create(
        purchase_order=purchase_order,
        created_by=handler_context["user"],
        comment=comment_text,
    )

    logger.info(
        "Order acknowledgment mismatch approved",
        purchase_order_id=purchase_order_id,
        po_number=purchase_order.po_number,
        validation_error=validation_error,
        user_id=handler_context["user"].pk,
    )

    return ActionHandlerResult(errors=None, result=None)


def approve_document_match(
    handler_context: ActionHandlerContext,
    params: dict[str, Any],
) -> ActionHandlerResult:
    """
    Approve a document match. Only updates PO status to INVOICE_MATCHED
    if the documents being compared are a purchase order and an invoice.

    Required params:
    - document1_id: ID of the first document
    - document1_type: Type of the first document (invoice, purchase_order, etc.)
    - document2_id: ID of the second document
    - document2_type: Type of the second document (invoice, purchase_order, etc.)

    Returns:
        ActionHandlerResult: Result with any errors or success
    """
    # Import here to avoid circular imports
    from didero.activity_log.models import PurchaseOrderStatusUpdate
    from didero.invoices.models import Invoice
    from didero.orders.models import (
        OrderAcknowledgement,
        PurchaseOrder,
        PurchaseOrderComment,
    )
    from didero.orders.schemas import PurchaseOrderStatus
    from didero.tasks.models import TaskAction, TaskActionStatus

    logger.info(
        "Approve Document Match Action Executed",
        handler_context=handler_context,
        params=params,
    )

    try:
        document1_id = params["document1_id"]
        document1_type = params["document1_type"]
        document2_id = params["document2_id"]
        document2_type = params["document2_type"]
    except KeyError as e:
        param_name = str(e).replace("'", "")
        return ActionHandlerResult(
            errors=[f"Missing required parameter: {param_name}"], result=None
        )

    # Determine which document is the PO and what type of comparison this is
    purchase_order_id = None
    invoice_id = None
    order_acknowledgement_id = None
    comparison_type = None

    po_types = ["purchase_order", "po", "PURCHASE_ORDER"]  # Support both forms
    invoice_types = ["invoice", "INVOICE"]
    oa_types = ["ORDER_ACKNOWLEDGEMENT", "order_ack"]

    # Check for PO-Invoice comparison
    if document1_type in po_types and document2_type in invoice_types:
        purchase_order_id = document1_id
        invoice_id = document2_id
        comparison_type = "PO_INVOICE"
    elif document1_type in invoice_types and document2_type in po_types:
        invoice_id = document1_id
        purchase_order_id = document2_id
        comparison_type = "INVOICE_PO"
    # Check for PO-OA comparison
    elif document1_type in po_types and document2_type in oa_types:
        purchase_order_id = document1_id
        order_acknowledgement_id = document2_id
        comparison_type = "PO_OA"
    elif document1_type in oa_types and document2_type in po_types:
        order_acknowledgement_id = document1_id
        purchase_order_id = document2_id
        comparison_type = "OA_PO"
    else:
        # Not a comparison we handle, approve without PO status update
        logger.info(
            "Document match approval for non-PO comparison - no PO status update needed",
            document1_type=document1_type,
            document2_type=document2_type,
            document1_id=document1_id,
            document2_id=document2_id,
        )
        return ActionHandlerResult(
            errors=None,
            result={
                "message": f"Document match approved ({document1_type}-{document2_type} comparison)",
                "po_status_updated": False,
            },
        )

    # Proceed with PO status update
    try:
        # Get the PO
        try:
            purchase_order = PurchaseOrder.objects.get(pk=purchase_order_id)
        except (PurchaseOrder.DoesNotExist, ValueError):
            # Maybe it's a reference number (po_number)
            purchase_order = PurchaseOrder.objects.get(po_number=purchase_order_id)

        # Get the other document based on comparison type
        invoice = None
        order_acknowledgement = None

        if comparison_type in ["PO_INVOICE", "INVOICE_PO"]:
            try:
                invoice = Invoice.objects.get(pk=invoice_id)
            except (Invoice.DoesNotExist, ValueError):
                # Maybe it's a reference number (invoice_number)
                invoice = Invoice.objects.get(invoice_number=invoice_id)
        elif comparison_type in ["PO_OA", "OA_PO"]:
            try:
                order_acknowledgement = OrderAcknowledgement.objects.get(
                    pk=order_acknowledgement_id
                )
            except (OrderAcknowledgement.DoesNotExist, ValueError):
                # Maybe it's a reference number (order_number)
                order_acknowledgement = OrderAcknowledgement.objects.get(
                    order_number=order_acknowledgement_id
                )

    except PurchaseOrder.DoesNotExist:
        return ActionHandlerResult(
            errors=[f"Purchase order {purchase_order_id} not found"], result=None
        )
    except Invoice.DoesNotExist:
        return ActionHandlerResult(
            errors=[f"Invoice {invoice_id} not found"], result=None
        )
    except OrderAcknowledgement.DoesNotExist:
        return ActionHandlerResult(
            errors=[f"Order acknowledgement {order_acknowledgement_id} not found"],
            result=None,
        )

    # Store old status for activity log
    old_status = purchase_order.order_status

    # Update the purchase order status based on comparison type
    if comparison_type in ["PO_INVOICE", "INVOICE_PO"]:
        purchase_order.order_status = PurchaseOrderStatus.INVOICE_MATCHED.value
        new_status_name = "INVOICE_MATCHED"
    elif comparison_type in ["PO_OA", "OA_PO"]:
        purchase_order.order_status = PurchaseOrderStatus.AWAITING_SHIPMENT.value
        new_status_name = "AWAITING_SHIPMENT"
    else:
        # Fallback case - this shouldn't happen, but handle it gracefully
        new_status_name = "UNKNOWN"
        logger.warning(
            "Unknown comparison type in approve_document_match",
            comparison_type=comparison_type,
            purchase_order_id=purchase_order_id,
        )

    purchase_order.save()

    # Create activity log entry for status change
    PurchaseOrderStatusUpdate.objects.create(
        purchase_order=purchase_order,
        user_actor=handler_context["user"],
        system_actor=None,
        status_changed="order_status",
        old_status=old_status,
        new_status=purchase_order.order_status,
    )

    # Add a comment documenting the document match approval
    if comparison_type in ["PO_INVOICE", "INVOICE_PO"]:
        invoice_number = invoice.invoice_number if invoice else "Unknown"
        comment_text = (
            f"Document match approved. Invoice {invoice_number} "
            f"matched to purchase order {purchase_order.po_number}. "
            f"Purchase order status updated to INVOICE_MATCHED."
        )
    elif comparison_type in ["PO_OA", "OA_PO"]:
        order_number = (
            order_acknowledgement.order_number if order_acknowledgement else "Unknown"
        )
        comment_text = (
            f"Document match approved. Order acknowledgement {order_number} "
            f"matched to purchase order {purchase_order.po_number}. "
            f"Purchase order status updated to AWAITING_SHIPMENT."
        )
    else:
        comment_text = (
            f"Document match approved for purchase order {purchase_order.po_number}. "
            f"Purchase order status updated."
        )

    PurchaseOrderComment.objects.create(
        purchase_order=purchase_order,
        created_by=handler_context["user"],
        comment=comment_text,
    )

    # Log based on comparison type
    if comparison_type in ["PO_INVOICE", "INVOICE_PO"]:
        invoice_number = invoice.invoice_number if invoice else "Unknown"
        logger.info(
            "Document match approved - PO status updated to INVOICE_MATCHED",
            invoice_id=invoice_id,
            invoice_number=invoice_number,
            purchase_order_id=purchase_order_id,
            po_number=purchase_order.po_number,
            user_id=handler_context["user"].pk,
        )
    elif comparison_type in ["PO_OA", "OA_PO"]:
        order_number = (
            order_acknowledgement.order_number if order_acknowledgement else "Unknown"
        )
        logger.info(
            "Document match approved - PO status updated to AWAITING_SHIPMENT",
            order_acknowledgement_id=order_acknowledgement_id,
            order_acknowledgement_number=order_number,
            purchase_order_id=purchase_order_id,
            po_number=purchase_order.po_number,
            user_id=handler_context["user"].pk,
        )

    # Mark the current task as completed and invalidate other actions
    try:
        current_action = TaskAction.objects.get(pk=handler_context["task_action_id"])
        if current_action and current_action.task:
            # Mark the task as completed
            from didero.tasks.schemas import TaskStatus

            current_action.task.status = TaskStatus.COMPLETED
            current_action.task.save(update_fields=["status", "modified_at"])

            # Invalidate any other pending actions (mutual exclusion)
            other_pending_actions = current_action.task.actions.filter(
                status=TaskActionStatus.PENDING
            ).exclude(pk=current_action.pk)

            invalidated_count = other_pending_actions.update(
                status=TaskActionStatus.INVALIDATED, modified_at=timezone.now()
            )

            if invalidated_count > 0:
                logger.info(
                    f"Invalidated {invalidated_count} other pending actions after document match approval",
                    task_id=current_action.task.pk,
                    task_action_id=handler_context["task_action_id"],
                )
    except Exception as e:
        logger.error(
            "Failed to complete task and invalidate other actions",
            error=str(e),
            task_action_id=handler_context["task_action_id"],
        )

    # Create return message based on comparison type
    if comparison_type in ["PO_INVOICE", "INVOICE_PO"]:
        message = f"Document match approved - PO status updated to {new_status_name}"
        result_data = {
            "message": message,
            "invoice_id": invoice_id,
            "purchase_order_id": purchase_order_id,
            "po_status_updated": True,
        }
    elif comparison_type in ["PO_OA", "OA_PO"]:
        message = f"Document match approved - PO status updated to {new_status_name}"
        result_data = {
            "message": message,
            "order_acknowledgement_id": order_acknowledgement_id,
            "purchase_order_id": purchase_order_id,
            "po_status_updated": True,
        }
    else:
        # Fallback for backward compatibility
        message = "Document match approved - PO status updated"
        result_data = {
            "message": message,
            "purchase_order_id": purchase_order_id,
            "po_status_updated": True,
        }

    return ActionHandlerResult(
        errors=None,
        result=result_data,
    )


def _check_and_trigger_oa_erp_sync(purchase_order: "PurchaseOrder", user: User) -> None:
    """
    Check if OA dates differ from PO and trigger ERP sync if configured.

    This is a separate function to keep the main handler clean and testable.
    """
    try:
        from asgiref.sync import async_to_sync

        from didero.integrations.models import ERPIntegrationConfig
        from didero.users.models import (
            UserWorkflow,
        )  # TODO: Check the import errors - @Amenti
        from didero.workflows.core.activities.erp_sync import (
            sync_erp_order_acknowledgement,
        )
        from didero.workflows.core_workflows.order_ack.schemas import (
            OrderAcknowledgementBehaviorConfig,
        )

        # Check if team has ERP integration
        if not ERPIntegrationConfig.objects.filter(
            team=purchase_order.team, enabled=True
        ).exists():
            logger.info(
                "No ERP integration configured, skipping OA sync check",
                po_id=purchase_order.id,
                team_id=purchase_order.team_id,
            )
            return

        # Check workflow configuration
        try:
            workflow = UserWorkflow.objects.select_related("behavior_config").get(
                team_id=purchase_order.team_id, workflow_type="order_acknowledgement"
            )

            if workflow.behavior_config:
                config = OrderAcknowledgementBehaviorConfig.model_validate(
                    workflow.behavior_config.config
                )

                if not config.enable_erp_sync:
                    logger.info(
                        "OA ERP sync disabled in workflow config",
                        po_id=purchase_order.id,
                    )
                    return
            else:
                # No config, use defaults (disabled)
                return

        except UserWorkflow.DoesNotExist:
            logger.warning(
                "No OA workflow found for team",
                team_id=purchase_order.team_id,
            )
            return

        # Get the latest OA for this PO
        latest_oa = (
            purchase_order.order_acknowledgements.prefetch_related("items__item")
            .order_by("-created_at")
            .first()
        )

        if not latest_oa:
            logger.warning(
                "No OA found for PO during confirmation",
                po_id=purchase_order.id,
            )
            return

        # Check if PO has requested_date
        if not purchase_order.requested_date:
            logger.warning(
                "PO has no requested_date, cannot compare OA dates",
                po_id=purchase_order.id,
            )
            return

        # Check for date differences
        has_date_changes = False
        date_change_count = 0

        for oa_item in latest_oa.items.all():
            if (
                oa_item.promised_delivery_date
                and oa_item.promised_delivery_date != purchase_order.requested_date
            ):
                has_date_changes = True
                date_change_count += 1

        if not has_date_changes:
            logger.info(
                "No OA date changes detected",
                oa_id=latest_oa.id,
                po_id=purchase_order.id,
            )
            return

        logger.info(
            "OA date changes detected, triggering ERP sync",
            oa_id=latest_oa.id,
            po_id=purchase_order.id,
            changed_items=date_change_count,
            sync_mode="manual",  # Always manual from confirmation
        )

        # Call ERP sync activity
        sync_result = async_to_sync(sync_erp_order_acknowledgement)(
            {
                "oa_id": latest_oa.id,
                "po_id": purchase_order.id,
                "team_id": purchase_order.team_id,
                "sync_mode": "manual",  # Triggered by confirmation
            }
        )

        if sync_result["success"]:
            logger.info(
                "OA ERP sync completed successfully",
                oa_id=latest_oa.id,
                po_id=purchase_order.id,
                synced_lines=sync_result.get("synced_lines", []),
            )
        else:
            logger.error(
                "OA ERP sync failed",
                oa_id=latest_oa.id,
                po_id=purchase_order.id,
                error=sync_result.get("error_message"),
            )
            # Error task was already created by the sync activity

    except Exception as e:
        # Don't fail the confirmation if ERP sync check fails
        logger.exception(
            "Failed to check/execute OA ERP sync",
            po_id=purchase_order.id,
            error=str(e),
        )


def execute_database_update(
    handler_context: ActionHandlerContext, params: dict[str, Any]
) -> ActionHandlerResult:
    """
    Execute an approved database update.

    Required params:
    - model: Model path (e.g., "orders.PurchaseOrder")
    - record_id: Primary key of record to update
    - field_name: Field name to update
    - field_type: Django field type
    - suggested_value: Value to update (as string)

    Returns:
        ActionHandlerResult: Result with success/error status
    """
    from django.apps import apps

    logger.info(
        "Execute Database Update Action",
        handler_context=handler_context,
        params=params,
    )

    # Validate required parameters
    required_params = [
        "model",
        "record_id",
        "field_name",
        "field_type",
        "suggested_value",
    ]
    missing_params = [p for p in required_params if p not in params]
    if missing_params:
        return ActionHandlerResult(
            errors=[f"Missing required parameters: {', '.join(missing_params)}"],
            result=None,
        )

    try:
        # Import field converter
        from didero.ai.reasoning_engine.tools.utils.field_converters import (
            FieldConversionError,
            FieldConverter,
        )

        # Parse model
        model = params["model"]
        record_id = int(params["record_id"])
        field_name = params["field_name"]
        field_type = params["field_type"]
        suggested_value = params["suggested_value"]

        app_label, model_name = model.split(".", 1)
        model_class = apps.get_model(app_label, model_name)

        # Get the record
        record = model_class.objects.get(pk=record_id)

        # SECURITY: Verify record belongs to user's team (team isolation)
        user_team_id = (
            handler_context["user"].teams.first().id
            if handler_context["user"].teams.exists()
            else None
        )
        record_team_id = None

        # Determine record's team based on model type
        if hasattr(record, "team_id"):
            record_team_id = record.team_id
        elif hasattr(record, "team"):
            record_team_id = record.team.id if record.team else None
        elif hasattr(record, "purchase_order") and hasattr(
            record.purchase_order, "team_id"
        ):
            record_team_id = record.purchase_order.team_id  # For shipments, etc.

        if not user_team_id or not record_team_id or user_team_id != record_team_id:
            logger.error(
                "Team isolation violation in database update",
                user_team_id=user_team_id,
                record_team_id=record_team_id,
                model=model,
                record_id=record_id,
                user_id=handler_context["user"].id,
            )
            return ActionHandlerResult(
                errors=["Access denied: Record does not belong to your team"],
                result=None,
            )

        # Use centralized field conversion to ensure consistency
        try:
            converted_value, display_string, field_info = (
                FieldConverter.validate_and_convert(
                    model_class, field_name, suggested_value
                )
            )
        except FieldConversionError as e:
            logger.error(
                "Field conversion failed during database update execution",
                model=model,
                record_id=record_id,
                field_name=field_name,
                suggested_value=suggested_value,
                error=str(e),
                user_id=handler_context["user"].id,
            )
            return ActionHandlerResult(
                errors=[f"Cannot convert value: {str(e)}"], result=None
            )

        # Store old value for audit trail
        old_value = getattr(record, field_name)

        # Update the field
        setattr(record, field_name, converted_value)
        record.save(update_fields=[field_name])

        logger.info(
            "Database update executed successfully",
            model=model,
            record_id=record_id,
            field_name=field_name,
            old_value=str(old_value),
            new_value=str(converted_value),
            user_id=handler_context["user"].id,
        )

        return ActionHandlerResult(
            errors=None,
            result={
                "success": True,
                "message": f"Successfully updated {field_name} to {converted_value}",
                "old_value": str(old_value),
                "new_value": str(converted_value),
            },
        )

    except model_class.DoesNotExist:
        error_msg = f"Record with ID {record_id} not found in {model}"
        logger.error("Database update failed", error=error_msg)
        return ActionHandlerResult(errors=[error_msg], result=None)

    except FieldConversionError as e:
        error_msg = f"Field conversion error: {e}"
        logger.error("Database update failed", error=error_msg)
        return ActionHandlerResult(errors=[error_msg], result=None)

    except Exception as e:
        error_msg = f"Database update failed: {str(e)}"
        logger.error("Database update failed", error=error_msg, exc_info=True)
        return ActionHandlerResult(errors=[error_msg], result=None)


def reject_database_update(
    handler_context: ActionHandlerContext, params: dict[str, Any]
) -> ActionHandlerResult:
    """
    Reject a database update suggestion.

    Optional params:
    - rejection_reason: Reason for rejecting the update

    Returns:
        ActionHandlerResult: Result with success status
    """
    logger.info(
        "Reject Database Update Action",
        handler_context=handler_context,
        params=params,
    )

    rejection_reason = params.get("rejection_reason", "No reason provided")

    logger.info(
        "Database update rejected",
        rejection_reason=rejection_reason,
        user_id=handler_context["user"].id,
    )

    return ActionHandlerResult(
        errors=None,
        result={
            "success": True,
            "message": "Database update suggestion rejected",
            "rejection_reason": rejection_reason,
        },
    )


# Define the handler function type
HandlerFunction = Callable[[ActionHandlerContext, dict[str, Any]], ActionHandlerResult]

# All Handlers should return a tuple of (error, result)
ACTION_TYPE_HANDLER_MAP: Dict[TaskActionType, HandlerFunction] = {
    TaskActionType.UPDATE_NETSUITE: cast(HandlerFunction, update_netsuite),
    TaskActionType.APPROVE_PURCHASE_ORDER: cast(
        HandlerFunction, approve_purchase_order
    ),
    TaskActionType.DENY_PURCHASE_ORDER: cast(HandlerFunction, deny_purchase_order),
    TaskActionType.CONFIRM_PURCHASE_ORDER: cast(
        HandlerFunction, confirm_purchase_order
    ),
    TaskActionType.CANCEL_PURCHASE_ORDER: cast(HandlerFunction, cancel_purchase_order),
    TaskActionType.CONFIRM_ORDER_ACKNOWLEDGEMENT: cast(
        HandlerFunction, confirm_order_acknowledgement
    ),
    TaskActionType.REJECT_ORDER_ACKNOWLEDGEMENT: cast(
        HandlerFunction, reject_order_acknowledgement
    ),
    TaskActionType.APPROVE_OA_MISMATCH: cast(HandlerFunction, approve_oa_mismatch),
    TaskActionType.ADD_COMMENT: cast(HandlerFunction, add_comment),
    TaskActionType.AUTO_COMMENT: cast(HandlerFunction, auto_comment),
    TaskActionType.OPS_ADD_COMMENT: cast(
        HandlerFunction, add_comment
    ),  # Using the same handler as ADD_COMMENT
    TaskActionType.APPROVE_FREIGHT_CHARGE: cast(
        HandlerFunction, approve_freight_charge
    ),
    TaskActionType.APPROVE_PRICE_CHANGE: cast(HandlerFunction, approve_price_change),
    TaskActionType.APPROVE_ADDRESS_CHANGE: cast(
        HandlerFunction, approve_address_change
    ),
    TaskActionType.CONFIRM_SHIPMENT: cast(HandlerFunction, confirm_shipment),
    TaskActionType.CANCEL_SHIPMENT: cast(HandlerFunction, cancel_shipment),
    TaskActionType.APPROVE_DOCUMENT_MATCH: cast(
        HandlerFunction, approve_document_match
    ),
    TaskActionType.EXECUTE_DATABASE_UPDATE: cast(
        HandlerFunction, execute_database_update
    ),
    TaskActionType.REJECT_DATABASE_UPDATE: cast(
        HandlerFunction, reject_database_update
    ),
}
