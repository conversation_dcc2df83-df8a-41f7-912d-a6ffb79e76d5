from datetime import timed<PERSON><PERSON>

import structlog
from django.contrib.contenttypes.models import ContentType
from django.db.models import Count, Q
from django.utils import timezone
from django_filters import rest_framework as filters
from rest_framework import status, viewsets
from rest_framework.decorators import action
from rest_framework.exceptions import ValidationError
from rest_framework.permissions import IsAuthenticated
from rest_framework.request import Request
from rest_framework.response import Response

from didero.filters import BaseFilter, ChoiceInFilter, get_enum_choices
from didero.tasks.models import Task, TaskAction, TaskActionStatus, TaskStatus
from didero.tasks.schemas import ActionExecutionType
from didero.tasks.serializers import (
    TaskActionSerializer,
    TaskSerializer,
    TaskV2Serializer,
)
from didero.users.models.user_models import User
from didero.views import APIView, PagesPagination

logger = structlog.get_logger(__name__)

VALID_TASK_ACTION_NAMES = [
    "MARK_AS_COMPLETE",
    "RESCHEDULE",
    "MARK_AS_PENDING",
]


class TaskFilter(BaseFilter):
    user = filters.NumberFilter(field_name="user")
    status = ChoiceInFilter(field_name="status", choices=get_enum_choices(TaskStatus))
    model_type = filters.CharFilter(field_name="model_type", method="filter_model_type")
    model_id = filters.NumberFilter(field_name="model_id")
    task_type = filters.NumberFilter(field_name="task_type")
    user_group = filters.NumberFilter(field_name="user_group")

    class Meta:
        model = Task
        fields = [
            "user",
            "status",
            "model_type",
            "model_id",
            "task_type",
            "user_group",
        ]

    def filter_model_type(self, queryset, name, value):
        try:
            """
            Get the ContentType based on the model name only.
            ContentType looks something like:
                ContentType.objects.get(model="purchaseorder")
                <ContentType: orders | purchase order>
            But we've designed our api to only take the model, e.g. "purchaseorder"
            This is so that the FE doesn't have to hardcode the ContentType id
            (e.g. purchaseorder = 4).
            However, a downside is that if two apps have a model with the same name,
            we won't differentiate. Still, that is unlikely for now, and this is a better
            solution than having the clunkier `{app}-{model}` format.
            """
            content_type = ContentType.objects.get(model=value)
            return queryset.filter(model_type=content_type)
        except ContentType.DoesNotExist:
            return queryset.none()

    def multi_field_search(self, queryset, name, value):
        """
        Search in task fields for both v1 and v2 tasks.
        For v1 tasks, search in task_config.description
        For v2 tasks, search in task_config.(title, preview_title, preview_description)
        """
        if not value:
            return queryset

        # Build complex search query
        return queryset.filter(
            # For V1 tasks
            (Q(task_type__isnull=False) & Q(task_config__description__icontains=value))
            |
            # For V2 tasks
            (
                Q(task_type_v2__isnull=False)
                & (
                    Q(task_config__title__icontains=value)
                    | Q(task_config__preview_title__icontains=value)
                    | Q(task_config__preview_description__icontains=value)
                )
            )
        ).distinct()


class TaskViewset(APIView, viewsets.ModelViewSet):
    serializer_class = TaskSerializer
    permission_classes = [IsAuthenticated]
    pagination_class = PagesPagination
    filterset_class = TaskFilter

    def get_queryset(self):
        return self.filterset_class.get_filtered_queryset(
            self.request,
            Task.objects.filter(
                # get all task for the user AND user_groups the user belongs to
                # and only v1 tasks
                Q(task_type_v2__isnull=True)
                & (
                    Q(user=self.request.user)
                    | Q(user_group__users__pk=self.request.user.pk)
                )
            ).order_by("-created_at"),
        ).distinct()

    def create(self, request, *args, **kwargs):
        serializer = self.serializer_class(data=request.data)
        if serializer.is_valid(raise_exception=True):
            serializer.save(user=request.user)
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def update(self, request, pk=None, partial=False):
        task = self.get_object()
        serializer = self.serializer_class(task, data=request.data, partial=partial)
        if serializer.is_valid(raise_exception=True):
            serializer.save()
            return Response(serializer.data)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def retrieve(self, request, pk=None):
        task = self.get_object()
        serializer = self.serializer_class(task)
        return Response(serializer.data)

    @action(detail=False, methods=["post"], url_path="action")
    def action(self, request):
        """
        This endpoint allows you to perform various actions on a task, such as marking it as complete,
        rescheduling it, or marking it as pending.

        Request data should include:
        - task_id: The ID of the task to perform the action on.
        - type: The type of action to perform. Valid values are "MARK_AS_COMPLETE", "RESCHEDULE", and "MARK_AS_PENDING".
        - remind_again_in: (Optional) The number of hours to reschedule the task. Required if type is "RESCHEDULE".

        Returns:
        - 200 OK if the action was successful.
        - 400 Bad Request if the request data is invalid or the action type is invalid.
        - 404 Not Found if the task does not exist.
        """
        task_id = request.data.get("task_id")
        task_action_name = request.data.get("task_action_name")
        remind_again_in = request.data.get("remind_again_in", None)

        if not task_id or not task_action_name:
            return Response(
                {"error": "task_id and type are required fields."},
                status=status.HTTP_400_BAD_REQUEST,
            )

        if task_action_name not in VALID_TASK_ACTION_NAMES:
            return Response(
                {
                    "error": "Invalid action type or missing remind_again_in for RESCHEDULE."
                },
                status=status.HTTP_400_BAD_REQUEST,
            )
        try:
            task = self.get_queryset().filter(id=task_id).get()
        except Task.DoesNotExist:
            return Response(
                {"error": "Task not found."}, status=status.HTTP_404_NOT_FOUND
            )

        if task_action_name == "MARK_AS_COMPLETE":
            task.status = TaskStatus.COMPLETED.value
        elif task_action_name == "RESCHEDULE" and remind_again_in:
            task.status = TaskStatus.ON_HOLD.value
            task.next_reminder_at = timezone.now() + timedelta(hours=remind_again_in)
        elif task_action_name == "MARK_AS_PENDING":
            task.status = TaskStatus.PENDING.value

        task.save()
        return Response({"status": "success"}, status=status.HTTP_200_OK)


class TaskV2Viewset(APIView, viewsets.ModelViewSet):
    serializer_class = TaskV2Serializer
    permission_classes = [IsAuthenticated]
    pagination_class = PagesPagination
    filterset_class = TaskFilter

    def get_queryset(self):
        return self.filterset_class.get_filtered_queryset(
            self.request,
            Task.objects.filter(
                # get all task for the user AND user_groups the user belongs to
                Q(task_type__isnull=True)
                & (
                    Q(user=self.request.user)
                    | Q(user_group__users__pk=self.request.user.pk)
                )
            ).order_by("-created_at"),
        ).distinct()

    @action(
        detail=True,
        methods=["get"],
        url_path="available-assignees",
        url_name="available-assignees",
    )
    def get_available_assignees(self, request: Request, pk=None) -> Response:
        """
        Get a list of users that this task can be reassigned to.

        Returns all users that belong to the same team as the task.

        Example:
        GET /api/v2/tasks/{id}/available-assignees/
        """
        task = self.get_object()
        team = task.team

        if not team:
            return Response(
                {"error": "Could not determine team for this task."},
                status=status.HTTP_400_BAD_REQUEST,
            )

        # Get all users in the same team
        users = team.users.all()

        # Serialize the users
        from didero.users.serializers import UserSerializer

        serializer = UserSerializer(users, many=True)

        return Response(serializer.data)

    @action(detail=True, methods=["post"], url_path="reassign", url_name="reassign")
    def reassign(self, request: Request, pk=None) -> Response:
        """
        Reassign a task to another user or user group.

        Either user_id or group_id must be provided in the request body:
        - If user_id is provided, the task is assigned to that user
        - If group_id is provided, the task is assigned to that group
        - If both are provided, the task is assigned to the user, ignoring the group

        The user/group must belong to the same team as the task.

        Example:
        POST /api/v2/tasks/{id}/reassign/
        {
            "user_id": 123  // ID of the new assignee
        }

        OR

        POST /api/v2/tasks/{id}/reassign/
        {
            "group_id": 456  // ID of the new assignee group
        }
        """
        task = self.get_object()
        user_id = request.data.get("user_id")
        group_id = request.data.get("group_id")

        # Check if we have at least one valid ID
        if not user_id and not group_id:
            return Response(
                {
                    "error": "Either user_id or group_id is required for task reassignment."
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        try:
            # If user_id is provided, assign to user
            if user_id:
                new_user = User.objects.get(pk=user_id)

                # Ensure the new user belongs to the task's team
                team = task.team
                if not team.users.filter(id=new_user.id).exists():
                    return Response(
                        {
                            "error": "The new assignee must belong to the same team as the task."
                        },
                        status=status.HTTP_400_BAD_REQUEST,
                    )

                # Update the task's user and clear any group assignment
                task.user = new_user
                task.user_group = None
                task.save(update_fields=["user", "user_group", "modified_at"])

            # Otherwise, assign to group
            elif group_id:
                from didero.users.models import DideroUserGroup

                try:
                    new_group = DideroUserGroup.objects.get(pk=group_id)

                    # Ensure the group belongs to the same team
                    if new_group.team.id != task.team.id:
                        return Response(
                            {
                                "error": "The group must belong to the same team as the task."
                            },
                            status=status.HTTP_400_BAD_REQUEST,
                        )

                    # Update the task's group and clear any user assignment
                    task.user_group = new_group
                    task.user = None
                    task.save(update_fields=["user", "user_group", "modified_at"])

                except DideroUserGroup.DoesNotExist:
                    return Response(
                        {"error": "Group not found."},
                        status=status.HTTP_404_NOT_FOUND,
                    )

            return Response(self.serializer_class(task).data)

        except User.DoesNotExist:
            return Response(
                {"error": "User not found."}, status=status.HTTP_404_NOT_FOUND
            )
        except Exception as e:
            return Response(
                {"error": f"Failed to reassign task: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    def get_status_counts(self) -> dict[str, int]:
        """
        Returns the count of tasks by status, applying all filters EXCEPT status filter.
        Uses distinct counting to handle users belonging to multiple groups.
        """
        # Get the query params, excluding status
        query_params = self.request.query_params.copy()
        if "status" in query_params:
            query_params.pop("status")

        # Start with raw base query without any filterset application
        base_queryset = Task.objects.filter(
            # get all task for the user AND user_groups the user belongs to
            Q(task_type__isnull=True)
            & (
                Q(user=self.request.user)
                | Q(user_group__users__pk=self.request.user.pk)
            )
        )

        # Apply filters (either including or excluding status filter)
        filter_instance = self.filterset_class(
            query_params, queryset=base_queryset, request=self.request
        )
        filtered_queryset = filter_instance.qs

        # Count distinct tasks by status (single query with distinct counting)
        status_counts = filtered_queryset.values("status").annotate(
            count=Count("id", distinct=True)  # Count distinct task IDs per status
        )

        counts_by_status = {
            TaskStatus.PENDING.value: 0,
            TaskStatus.ON_HOLD.value: 0,
            TaskStatus.COMPLETED.value: 0,
        }

        for item in status_counts:
            if item["status"] in counts_by_status:
                counts_by_status[item["status"]] = item["count"]

        return counts_by_status

    def list(self, request, *args, **kwargs):
        # First, get counts by status across all matched tasks
        counts_by_status = self.get_status_counts()

        # Get paginated queryset as normal
        queryset = self.filter_queryset(self.get_queryset())
        page = self.paginate_queryset(queryset)

        if page is not None:
            serializer = self.get_serializer(page, many=True)
            response = self.get_paginated_response(serializer.data)
            # Add counts to the response (at the bottom)
            response.data["status_counts"] = counts_by_status
            return response

        serializer = self.get_serializer(queryset, many=True)
        return Response({"results": serializer.data, "status_counts": counts_by_status})

    def update(
        self, request: Request, pk: int | None = None, partial: bool = False
    ) -> Response:
        """
        Update a Task V2 with standard fields or special operations like snoozing.

        For snoozing a task:
        - Set status to "ON_HOLD"
        - Optionally include 'remind_again_in' (hours as integer) to specify snooze duration
        - Or include 'next_reminder_at' (ISO timestamp) for a specific reminder time
        - If neither is provided, defaults to 24 hours

        Example for snoozing a task for 48 hours:
        PATCH /api/v2/tasks/{id}
        {
            "status": "ON_HOLD",
            "remind_again_in": 48
        }
        """
        task = self.get_object()
        data = request.data.copy()

        # Validate the data first using serializer
        serializer = self.serializer_class(task, data=data, partial=partial)

        try:
            if serializer.is_valid(raise_exception=True):
                # Handle snoozing functionality (status = ON_HOLD) inside validated block
                updated_status = serializer.validated_data.get("status")
                if updated_status == TaskStatus.ON_HOLD:
                    # If remind_again_in is provided, calculate and set next_reminder_at
                    if "remind_again_in" in data:
                        try:
                            hours = int(data["remind_again_in"])
                            task.next_reminder_at = timezone.now() + timedelta(
                                hours=hours
                            )
                        except (ValueError, TypeError):
                            return Response(
                                {
                                    "error": "remind_again_in must be a valid number of hours."
                                },
                                status=status.HTTP_400_BAD_REQUEST,
                            )
                    # If next_reminder_at isn't provided either, set default 24 hours
                    else:
                        task.next_reminder_at = timezone.now() + timedelta(hours=24)
                elif updated_status == TaskStatus.COMPLETED:
                    # If the task is completed, set the next_reminder_at to None
                    task.next_reminder_at = None

                # Apply all validated data to the task instance atomically
                for field, value in serializer.validated_data.items():
                    setattr(task, field, value)

                # Save the task with all updates (status, next_reminder_at, and serializer data)
                task.save()

                logger.info(
                    "Task updated successfully",
                    task_id=task.id,
                    status=task.status,
                )

                # Return the serialized representation
                return Response(self.serializer_class(task).data)
            else:
                logger.error(
                    "Serializer validation failed",
                    task_id=task.id,
                    serializer_errors=serializer.errors,
                )
                return Response(
                    {"error": f"Serializer validation failed: {serializer.errors}"},
                    status=status.HTTP_400_BAD_REQUEST,
                )
        except ValidationError as e:
            logger.error(
                "ValidationError during task update",
                task_id=task.id,
                validation_error=str(e.detail),
            )
            return Response(
                {"error": f"Failed to update task: {str(e.detail)}"},
                status=status.HTTP_400_BAD_REQUEST,
            )
        except Exception as e:
            logger.error(
                "Unexpected error during task update",
                task_id=task.id,
                error=str(e),
                exc_info=True,
            )
            return Response(
                {"error": f"Failed to update task: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    def retrieve(self, request: Request, pk: int | None = None) -> Response:
        task = self.get_object()
        serializer = self.serializer_class(task)
        return Response(serializer.data)


class TaskV2ActionViewset(APIView, viewsets.ModelViewSet):
    serializer_class = TaskActionSerializer
    permission_classes = [IsAuthenticated]
    pagination_class = PagesPagination
    filterset_class = TaskFilter

    def get_queryset(self):
        return TaskAction.objects.filter(
            Q(task__user=self.request.user)
            | Q(task__user_group__users__pk=self.request.user.pk)
        )

    def execute_action(self, request: Request) -> Response:
        task_action_id = request.data.get("task_action_id")
        custom_params = request.data.get("custom_params", {})  # Get custom params
        logger.info(
            f"Executing action {task_action_id} with custom params: {custom_params}"
        )

        # Get the task action
        try:
            task_action = TaskAction.objects.get(id=task_action_id)
        except TaskAction.DoesNotExist:
            return Response(
                {"error": f"Task action {task_action_id} not found."},
                status=status.HTTP_404_NOT_FOUND,
            )
        except Exception as e:
            return Response(
                {
                    "error": f"Unexpected error while getting task action {task_action_id}: {str(e)}"
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

        # Check if the task action is server executable
        if task_action.execution_type == ActionExecutionType.CLIENT:
            return Response(
                {"error": "Task action is supposed to be executed on the client."},
                status=status.HTTP_400_BAD_REQUEST,
            )

        # Check if the user has permission to execute the action
        if not task_action.task.user_belongs_to_task(self.request.user):  # pyright: ignore - task must exist
            return Response(
                {
                    "error": f"User {self.request.user.pk} does not have permission to execute this action."
                },
                status=status.HTTP_403_FORBIDDEN,
            )

        # See if the action is already executed
        if task_action.status == TaskActionStatus.COMPLETED:
            return Response(
                {"error": f"Task action {task_action.id} already executed."},
                status=status.HTTP_400_BAD_REQUEST,
            )

        try:
            # Pass custom params to execute method
            task_action.execute(custom_params=custom_params, executor=self.request.user)
            return Response(
                {
                    "status": "success",
                    "task_action": self.serializer_class(task_action).data,
                },
                status=status.HTTP_200_OK,
            )
        except Exception as e:
            return Response(
                {"error": f"Failed to execute the action: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )
