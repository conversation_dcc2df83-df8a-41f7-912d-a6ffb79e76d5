from enum import Enum, StrEnum
from typing import Any, Dict, List, Optional, TypedDict

from django.core.exceptions import ValidationError
from pydantic import BaseModel as PydanticBaseModel

from didero.users.models import User


class APICallType(Enum):
    GET = "GET"
    POST = "POST"
    PUT = "PUT"
    DELETE = "DELETE"


class ButtonStyle(Enum):
    PRIMARY = "PRIMARY"
    SUCCESS = "SUCCESS"
    DANGER = "DANGER"
    WARNING = "WARNING"


class TaskStatus(StrEnum):
    PENDING = "PENDING"
    ON_HOLD = "ON_HOLD"
    COMPLETED = "COMPLETED"
    FAILED = "FAILED"


class Modals(Enum):
    EMAIL_DIALOG_MODAL = "EMAIL_DIALOG_MODAL"
    ADD_COMMENT_MODAL = "ADD_COMMENT_MODAL"


class TaskType(StrEnum):
    PO_APPROVAL_REQUEST = "PO_APPROVAL_REQUEST"
    PO_RECEIVED_CHECK = "PO_RECEIVED_CHECK"
    PO_CREATED_FROM_QUOTATION = "PO_CREATED_FROM_QUOTATION"
    PO_CREATION_CONFIRMATION = "PO_CREATION_CONFIRMATION"
    USER_MENTIONED_IN_COMMENT = "USER_MENTIONED_IN_COMMENT"
    PO_STATUS_UPDATE_APPROVAL_DENIED = "PO_STATUS_UPDATE_APPROVAL_DENIED"
    PO_STATUS_UPDATE_APPROVED = "PO_STATUS_UPDATE_APPROVED"
    PO_STATUS_UPDATE_SUPPLIER_REJECTED = "PO_STATUS_UPDATE_SUPPLIER_REJECTED"
    PO_STATUS_UPDATE_SHIPPED = "PO_STATUS_UPDATE_SHIPPED"
    PO_STATUS_UPDATE_RECEIVED = "PO_STATUS_UPDATE_RECEIVED"
    PO_STATUS_UPDATE_CANCELED = "PO_STATUS_UPDATE_CANCELED"
    PO_SUPPLIER_UNRESPONSIVE = "PO_SUPPLIER_UNRESPONSIVE"
    SHIP_DATE_CHANGED = "SHIP_DATE_CHANGED"
    SUPPLIER_UNRESPONSIVE_TO_DIDERO_CHECKIN = "SUPPLIER_UNRESPONSIVE_TO_DIDERO_CHECKIN"
    IMPORT_ITEMS_FROM_PRICE_LIST = "IMPORT_ITEMS_FROM_PRICE_LIST"
    SUPPLIER_ONBOARDING_DOCUMENT_EXPIRING = "SUPPLIER_ONBOARDING_DOCUMENT_EXPIRING"
    SUPPLIER_ONBOARDING_DOCUMENT_EXPIRED = "SUPPLIER_ONBOARDING_DOCUMENT_EXPIRED"
    DRAFT_EMAIL_SIMPLE_DISMISS = "DRAFT_EMAIL_SIMPLE_DISMISS"
    DRAFT_EMAIL_OR_COMMENT = "DRAFT_EMAIL_OR_COMMENT"
    PO_SHOULD_HAVE_SHIPPED = "PO_SHOULD_HAVE_SHIPPED"
    MANUAL_NOTIFICATION_SIMPLE_DISMISS = "MANUAL_NOTIFICATION_SIMPLE_DISMISS"
    SHIPMENT_WORKFLOW_TRACKING_INFO_NOT_FOUND = (
        "SHIPMENT_WORKFLOW_TRACKING_INFO_NOT_FOUND"
    )
    SHIPMENT_WORKFLOW_NO_TRACKING_NUMBER_IN_SHIPMENT_UPDATE = (
        "SHIPMENT_WORKFLOW_NO_TRACKING_NUMBER_IN_SHIPMENT_UPDATE"
    )
    OPS_TASK_SHIPMENT_WORKFLOW_COMPLETED = "OPS_TASK_SHIPMENT_WORKFLOW_COMPLETED"
    ORDER_ACKNOWLEDGEMENT_WORKFLOW_COMPLETED = (
        "ORDER_ACKNOWLEDGEMENT_WORKFLOW_COMPLETED"
    )
    ORDER_ACKNOWLEDGEMENT_WORKFLOW_SUCCESS_NOTIFICATION = (
        "ORDER_ACKNOWLEDGEMENT_WORKFLOW_SUCCESS_NOTIFICATION"
    )
    PO_CREATION_WORKFLOW_SUCCESS_NOTIFICATION = (
        "PO_CREATION_WORKFLOW_SUCCESS_NOTIFICATION"
    )
    SHIPMENT_WORKFLOW_SUCCESS_NOTIFICATION = "SHIPMENT_WORKFLOW_SUCCESS_NOTIFICATION"
    PICKUP_WORKFLOW_SUCCESS_NOTIFICATION = "PICKUP_WORKFLOW_SUCCESS_NOTIFICATION"
    ADDRESS_MISMATCH_CONFIRMATION = "ADDRESS_MISMATCH_CONFIRMATION"
    FREIGHT_CHARGE_CONFIRMATION = "FREIGHT_CHARGE_CONFIRMATION"
    PRICE_CHANGE_CONFIRMATION = "PRICE_CHANGE_CONFIRMATION"
    PO_CONFIRMATION_FOLLOWUP = "PO_CONFIRMATION_FOLLOWUP"
    SHIPPING_DETAILS_FOLLOWUP = "SHIPPING_DETAILS_FOLLOWUP"
    DELIVERY_DATE_CONFIRMATION = "DELIVERY_DATE_CONFIRMATION"
    PART_NUMBER_ETA_FOLLOWUP = "PART_NUMBER_ETA_FOLLOWUP"
    ORDER_ACKNOWLEDGEMENT_VALIDATION_ERROR = "ORDER_ACKNOWLEDGEMENT_VALIDATION_ERROR"
    SHIPMENT_WORKFLOW_VALIDATION_ERROR = "SHIPMENT_WORKFLOW_VALIDATION_ERROR"
    FOLLOW_UP = "FOLLOW_UP"
    ERP_SYNC_ERROR = "ERP_SYNC_ERROR"
    ERP_SYNC_SUCCESS = "ERP_SYNC_SUCCESS"
    OA_ERP_SYNC_SUCCESS = "OA_ERP_SYNC_SUCCESS"
    OA_ERP_SYNC_ERROR = "OA_ERP_SYNC_ERROR"
    DOCUMENT_MATCH_REVIEW = "DOCUMENT_MATCH_REVIEW"
    DOCUMENT_UPLOAD_REQUEST = "DOCUMENT_UPLOAD_REQUEST"
    DATABASE_UPDATE_APPROVAL = "DATABASE_UPDATE_APPROVAL"


class ActionExecutionType(StrEnum):
    SERVER = (
        "server"  # Action requires server execution (and is executed synchronously)
    )
    SERVER_ASYNC = "server_async"  # Action requires server execution and is executed asynchronously
    CLIENT = "client"  # Action can be executed client-side


class TaskActionType(StrEnum):
    SEND_EMAIL = "SEND_EMAIL"
    OPS_ADD_COMMENT = "OPS_ADD_COMMENT"  # ops action
    ADD_COMMENT = "ADD_COMMENT"
    AUTO_COMMENT = "AUTO_COMMENT"  # auto comment action
    UPDATE_NETSUITE = "UPDATE_NETSUITE"
    APPROVE_PURCHASE_ORDER = "APPROVE_PURCHASE_ORDER"
    DENY_PURCHASE_ORDER = "DENY_PURCHASE_ORDER"
    CONFIRM_PURCHASE_ORDER = "CONFIRM_PURCHASE_ORDER"
    CANCEL_PURCHASE_ORDER = "CANCEL_PURCHASE_ORDER"
    CONFIRM_ORDER_ACKNOWLEDGEMENT = "CONFIRM_ORDER_ACKNOWLEDGEMENT"
    REJECT_ORDER_ACKNOWLEDGEMENT = "REJECT_ORDER_ACKNOWLEDGEMENT"
    APPROVE_OA_MISMATCH = "APPROVE_OA_MISMATCH"  # approve OA despite validation errors
    APPROVE_FREIGHT_CHARGE = "APPROVE_FREIGHT_CHARGE"  # ops action
    APPROVE_PRICE_CHANGE = "APPROVE_PRICE_CHANGE"  # ops action
    APPROVE_ADDRESS_CHANGE = "APPROVE_ADDRESS_CHANGE"  # ops action
    CONFIRM_SHIPMENT = "CONFIRM_SHIPMENT"  # shipment confirmation action
    CANCEL_SHIPMENT = "CANCEL_SHIPMENT"  # shipment cancellation action
    APPROVE_DOCUMENT_MATCH = "APPROVE_DOCUMENT_MATCH"  # approve document match
    REQUEST_DOCUMENT_CLARIFICATION = (
        "REQUEST_DOCUMENT_CLARIFICATION"  # request clarification for document matching
    )
    UPLOAD_DOCUMENT = "UPLOAD_DOCUMENT"  # upload document action
    EXECUTE_DATABASE_UPDATE = (
        "EXECUTE_DATABASE_UPDATE"  # execute approved database update
    )
    REJECT_DATABASE_UPDATE = (
        "REJECT_DATABASE_UPDATE"  # reject database update suggestion
    )


class TaskActionStatus(StrEnum):
    PENDING = "PENDING"
    COMPLETED = "COMPLETED"
    FAILED = "FAILED"
    INVALIDATED = "INVALIDATED"


class TaskActionExecutionStatus(StrEnum):
    PENDING = "PENDING"
    IN_PROGRESS = "IN_PROGRESS"
    COMPLETED = "COMPLETED"
    FAILED = "FAILED"


class TaskCategory(StrEnum):
    PURCHASE_ORDER = "PURCHASE_ORDER"
    INVOICE = "INVOICE"
    AI_OPERATIONS = "AI_OPERATIONS"
    OTHER = "OTHER"


class TaskContextPanelType(StrEnum):
    DOCUMENT_COMPARE = "DOCUMENT_COMPARE"
    PO_DETAILS = "PO_DETAILS"
    COMMUNICATION_DETAILS = "COMMUNICATION_DETAILS"


class TaskActionButtonType(StrEnum):
    STANDARD = "standard"
    GREEN = "green"
    RED = "red"


class PODetailsContextPanelParams(PydanticBaseModel):
    """
    Parameter definition for PO_DETAILS context panel type.
    """

    purchaseOrderId: str


class DocumentCompareContextPanelParams(PydanticBaseModel):
    """
    Parameter definition for DOCUMENT_COMPARE context panel type.
    """

    documentIds: List[str]


class CommunicationDetailsContextPanelParams(PydanticBaseModel):
    """
    Parameter definition for COMMUNICATION_DETAILS context panel type.
    """

    communicationId: str


class TaskContextPanel(PydanticBaseModel):
    """
    Defines a task context panel with its type and parameters.
    This is used when creating tasks to define context panels with parameter values.
    """

    panel_type: TaskContextPanelType
    param_values: Dict[str, Any] = {}


# Mapping panel types to their parameter models for documentation and validation
CONTEXT_PANEL_PARAM_MODELS = {
    TaskContextPanelType.PO_DETAILS: PODetailsContextPanelParams,
    TaskContextPanelType.DOCUMENT_COMPARE: DocumentCompareContextPanelParams,
    TaskContextPanelType.COMMUNICATION_DETAILS: CommunicationDetailsContextPanelParams,
}


class TaskParamDefinition(PydanticBaseModel):
    name: str
    description: str
    required: bool


class ActionHandlerContext(TypedDict):
    user: User
    task_action_id: str


class ActionHandlerResult(PydanticBaseModel):
    errors: Optional[list[str]] = None
    result: Any


class TaskParamDefinitionConfig(PydanticBaseModel):
    """
    This is the config for the parameters that are used to create a task.
    This is the class that should be used on model fields that are to define parameter requirements for a TaskAction, or TaskTypeV2.
    """

    parameters: List[TaskParamDefinition]

    @staticmethod
    def validate_json(value: str) -> None:
        try:
            TaskParamDefinitionConfig.model_validate_json(value)
        except ValueError as e:
            raise ValidationError(
                "Invalid JSON for TaskActionParamDefinitionConfig",
                params={"error": str(e)},
            )
        except ValidationError as e:
            raise ValidationError(
                "Structure of JSON is invalid for TaskActionParamDefinitionConfig",
                params={"error": str(e)},
            )
        except Exception as e:
            raise ValidationError(
                "Unknown error while validating TaskActionParamDefinitionConfig",
                params={"error": str(e)},
            )
