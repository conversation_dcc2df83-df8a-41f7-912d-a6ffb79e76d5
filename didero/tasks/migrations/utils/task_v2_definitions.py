from typing import Any, Dict, Optional

import structlog
from pydantic import BaseModel, Field

from didero.tasks.models import (
    TaskCategory,
    TaskContextPanelType,
    TaskParamDefinitionConfig,
    TaskTypeName,
    TaskTypeV2,
)
from didero.tasks.schemas import TaskParamDefinition

logger = structlog.get_logger(__name__)


class DocumentUploadRequestParams(BaseModel):
    document_type_required: str = Field(
        ..., description="Required document type (invoice, po, contract, etc.)"
    )
    reference_document_type: Optional[str] = Field(
        None, description="Type of reference document (PO, invoice, etc.)"
    )

    supplier_name: Optional[str] = Field(None, description="Supplier name for context")
    reference_number: Optional[str] = Field(
        None,
        description="Reference number (PO number, invoice number, quote number, etc.)",
    )


class TaskTypeV2Fields(BaseModel):
    name: TaskTypeName
    preview_description: str
    preview_title: str
    title: str
    description: str
    category: Optional[TaskCategory]
    # TODO: Remove this field in a future release as we're migrating to context_panels
    context_panel_type: Optional[TaskContextPanelType]
    param_definition: TaskParamDefinitionConfig


class POApprovalRequestTaskParams(BaseModel):
    supplier_name: str = Field(..., description="Supplier name")
    placed_by: str = Field(..., description="User who placed the order")
    po_number: str = Field(..., description="Purchase Order Number")
    total_cost: str = Field(..., description="Total cost of the purchase order")


class POCreationConfirmationTaskParams(BaseModel):
    supplier_name: str = Field(..., description="Supplier name")
    po_number: str = Field(..., description="Purchase Order Number")
    total_cost: str = Field(..., description="Total cost of the purchase order")


class ShipmentWorkflowCompletedTaskParams(BaseModel):
    po_number: str = Field(..., description="Purchase Order Number")
    tracking_number: Optional[str] = Field(None, description="Tracking Number")
    carrier: Optional[str] = Field(None, description="Carrier Name")
    shipment_date: Optional[str] = Field(None, description="Shipment Date")
    estimated_delivery_date: str = Field("N/A", description="Estimated Delivery Date")
    shipment_data: Optional[dict] = Field(
        None, description="JSON-serialized shipment data"
    )
    po_data: Optional[dict] = Field(
        None, description="JSON-serialized purchase order data"
    )


class ShipmentWorkflowValidationErrorParams(BaseModel):
    validation_error: str = Field(..., description="Validation error message")
    po_number: str = Field(..., description="Purchase Order Number")


class OrderAcknowledgementWorkflowCompletedTaskParams(BaseModel):
    po_number: str = Field(..., description="Purchase Order Number")
    supplier_name: str = Field(..., description="Supplier name")
    total_cost: str = Field(..., description="Total cost of the purchase order")


class OrderAcknowledgementWorkflowSuccessNotificationParams(BaseModel):
    po_number: str = Field(..., description="Purchase Order Number")
    supplier_name: str = Field(..., description="Supplier name")
    total_cost: str = Field(..., description="Total cost of the purchase order")
    details: str = Field(
        ..., description="Detailed information about the order acknowledgement"
    )
    order_acknowledgement_data: Optional[dict] = Field(
        None, description="JSON-serialized order acknowledgement data"
    )
    po_data: Optional[dict] = Field(
        None, description="JSON-serialized purchase order data"
    )


class OrderAcknowledgementValidationErrorParams(BaseModel):
    validation_error: str = Field(..., description="Validation error message")
    po_number: str = Field(..., description="Purchase Order Number")
    error_type_label: str = Field(
        default="OA Validation Error", description="Type of validation error for title"
    )


class FreightChargeConfirmationParams(BaseModel):
    invoice_number: str = Field(..., description="Invoice Number")
    freight_charge: str = Field(..., description="Freight Charge amount")
    supplier_name: str = Field(..., description="Supplier Name")
    po_number: str = Field(..., description="Purchase Order Number")


class PriceChangeConfirmationParams(BaseModel):
    item_number: str = Field(..., description="Item Number")
    system_name: str = Field(..., description="System Name")
    supplier_name: str = Field(..., description="Supplier Name")
    po_number: str = Field(..., description="Purchase Order Number")


class ShippingDetailsFollowupParams(BaseModel):
    supplier_name: str = Field(..., description="Supplier Name")
    po_number: str = Field(..., description="Purchase Order Number")


class DeliveryDateConfirmationParams(BaseModel):
    delivery_date: str = Field(..., description="Expected Delivery Date")
    supplier_name: str = Field(..., description="Supplier Name")
    po_number: str = Field(..., description="Purchase Order Number")


class FollowUpParams(BaseModel):
    po_number: str = Field(..., description="Purchase Order Number")
    supplier_name: str = Field(..., description="Supplier Name")
    follow_up_type: str = Field(
        ..., description="Type of follow-up (e.g. shipping, delivery, etc.)"
    )
    details: str = Field(
        ..., description="Additional details about the follow-up action"
    )


class DocumentMatchReviewParams(BaseModel):
    document1_type: str = Field(
        ..., description="Type of first document (e.g., invoice, receipt)"
    )
    document1_reference: str = Field(
        ..., description="Reference number of first document"
    )
    document1_id: str = Field(..., description="ID of the first document")
    document2_type: str = Field(
        ..., description="Type of second document (e.g., po, contract)"
    )
    document2_reference: str = Field(
        ..., description="Reference number of second document"
    )
    document2_id: str = Field(..., description="ID of the second document")
    match_result: str = Field(
        ..., description="AI matching result (e.g., 'exact_match', 'critical_issues')"
    )
    matching_score: str = Field(
        ..., description="AI confidence score as formatted string"
    )
    supplier_name: str = Field(..., description="Name of the supplier")
    comparison_summary: str = Field(
        ..., description="Summary of the document comparison"
    )
    comparison_data: dict = Field(..., description="Detailed comparison results")


class DatabaseUpdateApprovalParams(BaseModel):
    model: str = Field(..., description="Model to update (e.g., orders.PurchaseOrder)")
    record_id: int = Field(..., description="Primary key of record to update")
    record_display: str = Field(..., description="Human-readable record identifier")
    field_name: str = Field(..., description="Field to update")
    field_type: str = Field(..., description="Django field type")
    current_value: Any = Field(..., description="Current field value")
    current_display: str = Field(..., description="Human-readable current value")
    suggested_value: Any = Field(..., description="AI suggested value")
    suggested_display: str = Field(..., description="Human-readable suggested value")
    ai_reasoning: str = Field(..., description="AI explanation for change")
    confidence: float = Field(..., description="AI confidence score")
    source_context: dict = Field(
        default_factory=dict, description="Email/extraction context"
    )


class AddressMismatchConfirmationParams(BaseModel):
    invoice_number: str = Field(..., description="Invoice Number")
    supplier_name: str = Field(..., description="Supplier Name")
    po_number: str = Field(..., description="Purchase Order Number")


class POCreationWorkflowSuccessNotificationParams(BaseModel):
    supplier_name: str = Field(..., description="Supplier name")
    po_number: str = Field(..., description="Purchase Order Number")
    total_cost: str = Field(..., description="Total cost of the purchase order")
    details: str = Field(..., description="Detailed information about the PO")
    item_count: str = Field(..., description="Number of items in the PO")
    total_quantity: str = Field(..., description="Total quantity of items in the PO")
    purchase_order_data: Optional[dict] = Field(
        None, description="JSON-serialized purchase order data"
    )


class ShipmentWorkflowSuccessNotificationParams(BaseModel):
    po_number: str = Field(..., description="Purchase Order Number")
    tracking_number: str = Field(..., description="Tracking Number")
    carrier: str = Field(..., description="Carrier Name")
    shipment_date: str = Field(..., description="Shipment Date")
    details: str = Field(..., description="Detailed information about the shipment")
    po_data: Optional[dict] = Field(
        None, description="JSON-serialized purchase order data"
    )
    shipment_data: Optional[dict] = Field(
        None, description="JSON-serialized shipment data"
    )


class PickupWorkflowSuccessNotificationParams(BaseModel):
    po_number: str = Field(..., description="Purchase Order Number")
    supplier_name: str = Field(..., description="Supplier name")
    pickup_location: Optional[str] = Field(None, description="Pickup location")
    pickup_instructions: Optional[str] = Field(None, description="Pickup instructions")
    po_data: Optional[dict] = Field(
        None, description="JSON-serialized purchase order data"
    )


class ERPSyncErrorParams(BaseModel):
    po_number: str = Field(..., description="Purchase Order Number")
    error_message: str = Field(..., description="ERP sync error details")
    sync_fields: str = Field(..., description="Fields that failed to sync")
    erp_system: str = Field(..., description="ERP system name (e.g., NetSuite)")


class ERPSyncSuccessParams(BaseModel):
    po_number: str = Field(..., description="Purchase Order Number")
    sync_mode: str = Field(..., description="Sync mode (Automatic/Manual)")
    synced_fields: str = Field(..., description="Fields successfully synced")
    field_values: str = Field(..., description="Field:value pairs that were synced")
    erp_system: str = Field(..., description="ERP system name (e.g., NetSuite)")
    success_message: str = Field(..., description="Success confirmation message")


class OAERPSyncSuccessParams(BaseModel):
    po_number: str = Field(..., description="Purchase Order Number")
    oa_number: str = Field(..., description="Order Acknowledgment Number")
    sync_mode: str = Field(..., description="Sync mode (Automatic/Manual)")
    updated_lines: str = Field(..., description="Number of line items updated")
    date_changes: str = Field(..., description="Summary of date changes")
    erp_system: str = Field(..., description="ERP system name (e.g., NETSUITE)")


class OAERPSyncErrorParams(BaseModel):
    po_number: str = Field(..., description="Purchase Order Number")
    oa_number: str = Field(..., description="Order Acknowledgment Number")
    error_message: str = Field(..., description="ERP sync error details")
    attempted_changes: str = Field(..., description="Date changes that failed to sync")
    erp_system: str = Field(..., description="ERP system name (e.g., NETSUITE)")


# Helper function to convert Pydantic model to TaskParamDefinitionConfig
def pydantic_to_param_definition(model: type[BaseModel]) -> TaskParamDefinitionConfig:
    parameters = []
    for field_name, field in model.model_fields.items():
        parameters.append(
            TaskParamDefinition(
                name=field_name,
                description=field.description or "",
                required=True if field.is_required else False,
            )
        )
    return TaskParamDefinitionConfig(parameters=parameters)


TASK_TYPE_V2_CONFIG: dict[TaskTypeName, TaskTypeV2Fields] = {
    TaskTypeName.PO_APPROVAL_REQUEST: TaskTypeV2Fields(
        name=TaskTypeName.PO_APPROVAL_REQUEST,
        preview_title="{supplier_name}",
        preview_description="Order Approval",
        title="Approve PO {po_number} for {supplier_name}",
        description="Purchase Order {po_number}, created by {placed_by} for {supplier_name} with a total cost of {total_cost}, is awaiting your approval.",
        category=TaskCategory.PURCHASE_ORDER,
        context_panel_type=TaskContextPanelType.PO_DETAILS,
        param_definition=pydantic_to_param_definition(POApprovalRequestTaskParams),
    ),
    TaskTypeName.PO_CREATION_CONFIRMATION: TaskTypeV2Fields(
        name=TaskTypeName.PO_CREATION_CONFIRMATION,
        preview_title="{supplier_name}",
        preview_description="Order Review",
        title="Confirm PO {po_number} for {supplier_name}",
        description="Please review and confirm the details of Purchase Order {po_number} for {supplier_name}, amounting to {total_cost}, before it is issued to the supplier.",
        category=TaskCategory.PURCHASE_ORDER,
        context_panel_type=TaskContextPanelType.PO_DETAILS,
        param_definition=pydantic_to_param_definition(POCreationConfirmationTaskParams),
    ),
    TaskTypeName.OPS_TASK_SHIPMENT_WORKFLOW_COMPLETED: TaskTypeV2Fields(
        name=TaskTypeName.OPS_TASK_SHIPMENT_WORKFLOW_COMPLETED,
        preview_title="{po_number}",
        preview_description="Shipment Confirmation",
        title="Confirm Shipment for PO {po_number}",
        description="A shipment has been created for Purchase Order {po_number}, shipped via {carrier} with tracking number {tracking_number}. Review these details and confirm to update NetSuite.",
        category=TaskCategory.PURCHASE_ORDER,
        context_panel_type=TaskContextPanelType.PO_DETAILS,
        param_definition=pydantic_to_param_definition(
            ShipmentWorkflowCompletedTaskParams
        ),
    ),
    TaskTypeName.ORDER_ACKNOWLEDGEMENT_WORKFLOW_COMPLETED: TaskTypeV2Fields(
        name=TaskTypeName.ORDER_ACKNOWLEDGEMENT_WORKFLOW_COMPLETED,
        preview_title="{supplier_name}",
        preview_description="Order Acknowledgment",
        title="Review OA for PO {po_number} ({supplier_name})",
        description="An order acknowledgment for Purchase Order {po_number} has been received from {supplier_name} (total cost: {total_cost}). Please review the acknowledgment details for accuracy and confirm.",
        category=TaskCategory.PURCHASE_ORDER,
        context_panel_type=TaskContextPanelType.PO_DETAILS,
        param_definition=pydantic_to_param_definition(
            OrderAcknowledgementWorkflowCompletedTaskParams
        ),
    ),
    TaskTypeName.SHIPMENT_WORKFLOW_VALIDATION_ERROR: TaskTypeV2Fields(
        name=TaskTypeName.SHIPMENT_WORKFLOW_VALIDATION_ERROR,
        preview_title="{po_number}",
        preview_description="Shipment Validation Error",
        title="Shipment Validation Error on PO {po_number}",
        description="A validation error occurred in the shipment workflow for PO {po_number}. Error details: '{validation_error}'. Please investigate and resolve.",
        category=TaskCategory.PURCHASE_ORDER,
        context_panel_type=TaskContextPanelType.PO_DETAILS,
        param_definition=pydantic_to_param_definition(
            ShipmentWorkflowValidationErrorParams
        ),
    ),
    TaskTypeName.ORDER_ACKNOWLEDGEMENT_VALIDATION_ERROR: TaskTypeV2Fields(
        name=TaskTypeName.ORDER_ACKNOWLEDGEMENT_VALIDATION_ERROR,
        preview_title="{po_number}",
        preview_description="{error_type_label}",
        title="{error_type_label} on PO {po_number}",
        description="{validation_error}",
        category=TaskCategory.PURCHASE_ORDER,
        context_panel_type=TaskContextPanelType.PO_DETAILS,
        param_definition=pydantic_to_param_definition(
            OrderAcknowledgementValidationErrorParams
        ),
    ),
    # Allows for a completely custom task type to be generated by admins.
    TaskTypeName.MANUAL_NOTIFICATION_SIMPLE_DISMISS: TaskTypeV2Fields(
        name=TaskTypeName.MANUAL_NOTIFICATION_SIMPLE_DISMISS,
        preview_title="{preview_title}",
        preview_description="{preview_description}",
        title="{title}",
        description="{description}",
        category=TaskCategory.OTHER,
        context_panel_type=None,
        param_definition=TaskParamDefinitionConfig(
            parameters=[
                TaskParamDefinition(
                    name="preview_title",
                    description="Short preview title for the notification list.",
                    required=True,
                ),
                TaskParamDefinition(
                    name="preview_description",
                    description="Short preview description for the notification list.",
                    required=True,
                ),
                TaskParamDefinition(
                    name="title",
                    description="Main title of the notification.",
                    required=True,
                ),
                TaskParamDefinition(
                    name="description",
                    description="Detailed content of the notification.",
                    required=True,
                ),
            ]
        ),
    ),
    TaskTypeName.FREIGHT_CHARGE_CONFIRMATION: TaskTypeV2Fields(
        name=TaskTypeName.FREIGHT_CHARGE_CONFIRMATION,
        preview_title="{po_number}",
        preview_description="Freight Charge Review",
        title="Review Freight Charge: PO {po_number}, Inv {invoice_number}",
        description="Invoice {invoice_number}, associated with PO {po_number}, includes a freight charge of ${freight_charge}. Please review this charge and confirm its accuracy.",
        category=TaskCategory.PURCHASE_ORDER,
        context_panel_type=TaskContextPanelType.PO_DETAILS,
        param_definition=pydantic_to_param_definition(FreightChargeConfirmationParams),
    ),
    TaskTypeName.PRICE_CHANGE_CONFIRMATION: TaskTypeV2Fields(
        name=TaskTypeName.PRICE_CHANGE_CONFIRMATION,
        preview_title="{po_number}",
        preview_description="Price Mismatch",
        title="Price Mismatch: PO {po_number}, Item {item_number}",
        description="A price mismatch detected for item {item_number} on PO {po_number}. The price in the order confirmation from {supplier_name} differs from the PO. Please investigate.",
        category=TaskCategory.PURCHASE_ORDER,
        context_panel_type=TaskContextPanelType.PO_DETAILS,
        param_definition=pydantic_to_param_definition(PriceChangeConfirmationParams),
    ),
    TaskTypeName.SHIPPING_DETAILS_FOLLOWUP: TaskTypeV2Fields(
        name=TaskTypeName.SHIPPING_DETAILS_FOLLOWUP,
        preview_title="{po_number}",
        preview_description="Shipment Follow-up",
        title="Follow Up: Shipment Details for PO {po_number}",
        description="No shipment notice has been received for PO {po_number} from {supplier_name} more than 24 hours after order confirmation. Please follow up to obtain shipping details.",
        category=TaskCategory.PURCHASE_ORDER,
        context_panel_type=TaskContextPanelType.PO_DETAILS,
        param_definition=pydantic_to_param_definition(ShippingDetailsFollowupParams),
    ),
    TaskTypeName.FOLLOW_UP: TaskTypeV2Fields(
        name=TaskTypeName.FOLLOW_UP,
        preview_title="{po_number}",
        preview_description="Follow-up on {follow_up_type}",
        title="Follow up on {follow_up_type} for PO {po_number}",
        description="{details}",
        category=TaskCategory.PURCHASE_ORDER,
        context_panel_type=TaskContextPanelType.PO_DETAILS,
        param_definition=pydantic_to_param_definition(FollowUpParams),
    ),
    TaskTypeName.DELIVERY_DATE_CONFIRMATION: TaskTypeV2Fields(
        name=TaskTypeName.DELIVERY_DATE_CONFIRMATION,
        preview_title="{po_number}",
        preview_description="Delivery Date Check",
        title="Confirm Delivery Date: PO {po_number}",
        description="Purchase Order {po_number} from {supplier_name} is scheduled for delivery on {delivery_date}. Please contact the supplier to confirm that the delivery is on track.",
        category=TaskCategory.PURCHASE_ORDER,
        context_panel_type=TaskContextPanelType.PO_DETAILS,
        param_definition=pydantic_to_param_definition(DeliveryDateConfirmationParams),
    ),
    TaskTypeName.ADDRESS_MISMATCH_CONFIRMATION: TaskTypeV2Fields(
        name=TaskTypeName.ADDRESS_MISMATCH_CONFIRMATION,
        preview_title="{po_number}",
        preview_description="Address Mismatch",
        title="Address Mismatch: PO {po_number}, Inv {invoice_number}",
        description="The shipping address on invoice {invoice_number} from {supplier_name} for PO {po_number} does not match the PO details. Please contact the supplier to resolve this discrepancy.",
        category=TaskCategory.PURCHASE_ORDER,
        context_panel_type=TaskContextPanelType.PO_DETAILS,
        param_definition=pydantic_to_param_definition(
            AddressMismatchConfirmationParams
        ),
    ),
    TaskTypeName.PO_CREATION_WORKFLOW_SUCCESS_NOTIFICATION: TaskTypeV2Fields(
        name=TaskTypeName.PO_CREATION_WORKFLOW_SUCCESS_NOTIFICATION,
        preview_title="{supplier_name}",
        preview_description="PO {po_number} Created",
        title="PO {po_number} Created for {supplier_name}",
        description="Purchase Order {po_number} has been successfully created and issued to {supplier_name}. Additional workflow details: {details}.",
        category=TaskCategory.PURCHASE_ORDER,
        context_panel_type=TaskContextPanelType.PO_DETAILS,
        param_definition=pydantic_to_param_definition(
            POCreationWorkflowSuccessNotificationParams
        ),
    ),
    TaskTypeName.ORDER_ACKNOWLEDGEMENT_WORKFLOW_SUCCESS_NOTIFICATION: TaskTypeV2Fields(
        name=TaskTypeName.ORDER_ACKNOWLEDGEMENT_WORKFLOW_SUCCESS_NOTIFICATION,
        preview_title="{supplier_name}",
        preview_description="OA Auto-Accepted: PO {po_number}",
        title="OA Auto-Accepted for PO {po_number}",
        description="The order acknowledgment from {supplier_name} for Purchase Order {po_number} has been automatically accepted based on predefined criteria. Workflow details: {details}.",
        category=TaskCategory.PURCHASE_ORDER,
        context_panel_type=TaskContextPanelType.PO_DETAILS,
        param_definition=pydantic_to_param_definition(
            OrderAcknowledgementWorkflowSuccessNotificationParams
        ),
    ),
    TaskTypeName.SHIPMENT_WORKFLOW_SUCCESS_NOTIFICATION: TaskTypeV2Fields(
        name=TaskTypeName.SHIPMENT_WORKFLOW_SUCCESS_NOTIFICATION,
        preview_title="{po_number}",
        preview_description="Shipment Auto-Confirmed: PO {po_number}",
        title="Shipment Auto-Confirmed for PO {po_number}",
        description="The shipment for Purchase Order {po_number} with tracking number {tracking_number} has been automatically confirmed. Workflow details: {details}.",
        category=TaskCategory.PURCHASE_ORDER,
        context_panel_type=TaskContextPanelType.PO_DETAILS,
        param_definition=pydantic_to_param_definition(
            ShipmentWorkflowSuccessNotificationParams
        ),
    ),
    TaskTypeName.PICKUP_WORKFLOW_SUCCESS_NOTIFICATION: TaskTypeV2Fields(
        name=TaskTypeName.PICKUP_WORKFLOW_SUCCESS_NOTIFICATION,
        preview_title="{po_number}",
        preview_description="Ready for Pickup: PO {po_number}",
        title="Order Ready for Pickup - PO {po_number}",
        description="Purchase Order {po_number} from {supplier_name} is ready for pickup.",
        category=TaskCategory.PURCHASE_ORDER,
        context_panel_type=TaskContextPanelType.PO_DETAILS,
        param_definition=pydantic_to_param_definition(
            PickupWorkflowSuccessNotificationParams
        ),
    ),
    TaskTypeName.ERP_SYNC_ERROR: TaskTypeV2Fields(
        name=TaskTypeName.ERP_SYNC_ERROR,
        preview_title="{po_number}",
        preview_description="ERP Sync Failed",
        title="ERP Sync Failed for PO {po_number}",
        description="Failed to sync shipment data to {erp_system}. Error: {error_message}. Fields affected: {sync_fields}",
        category=TaskCategory.PURCHASE_ORDER,
        context_panel_type=TaskContextPanelType.PO_DETAILS,
        param_definition=pydantic_to_param_definition(ERPSyncErrorParams),
    ),
    TaskTypeName.ERP_SYNC_SUCCESS: TaskTypeV2Fields(
        name=TaskTypeName.ERP_SYNC_SUCCESS,
        preview_title="{po_number}",
        preview_description="ERP Sync Completed",
        title="ERP Sync Completed for PO {po_number}",
        description="{success_message} for {erp_system}. {sync_mode} sync successfully updated: {field_values}",
        category=TaskCategory.PURCHASE_ORDER,
        context_panel_type=TaskContextPanelType.PO_DETAILS,
        param_definition=pydantic_to_param_definition(ERPSyncSuccessParams),
    ),
    TaskTypeName.OA_ERP_SYNC_SUCCESS: TaskTypeV2Fields(
        name=TaskTypeName.OA_ERP_SYNC_SUCCESS,
        preview_title="{po_number}",
        preview_description="OA Date Sync Completed",
        title="Order Acknowledgment Date Sync Completed for PO {po_number}",
        description="{sync_mode} sync successfully updated expected receipt dates in {erp_system} based on Order Acknowledgment {oa_number}. Updated: {date_changes}",
        category=TaskCategory.PURCHASE_ORDER,
        context_panel_type=TaskContextPanelType.PO_DETAILS,
        param_definition=pydantic_to_param_definition(OAERPSyncSuccessParams),
    ),
    TaskTypeName.OA_ERP_SYNC_ERROR: TaskTypeV2Fields(
        name=TaskTypeName.OA_ERP_SYNC_ERROR,
        preview_title="{po_number}",
        preview_description="OA Date Sync Failed",
        title="Order Acknowledgment Date Sync Failed for PO {po_number}",
        description="Failed to sync Order Acknowledgment {oa_number} date changes to {erp_system}. Error: {error_message}. Attempted to update: {attempted_changes}",
        category=TaskCategory.PURCHASE_ORDER,
        context_panel_type=TaskContextPanelType.PO_DETAILS,
        param_definition=pydantic_to_param_definition(OAERPSyncErrorParams),
    ),
    TaskTypeName.DOCUMENT_MATCH_REVIEW: TaskTypeV2Fields(
        name=TaskTypeName.DOCUMENT_MATCH_REVIEW,
        preview_title="{supplier_name}",
        preview_description="Document Review Required - {document1_type}",
        title="Review {document1_reference} vs {document2_reference}",
        description="{document1_type} {document1_reference} from {supplier_name} requires review.",
        category=TaskCategory.OTHER,
        context_panel_type=TaskContextPanelType.DOCUMENT_COMPARE,
        param_definition=pydantic_to_param_definition(DocumentMatchReviewParams),
    ),
    TaskTypeName.DOCUMENT_UPLOAD_REQUEST: TaskTypeV2Fields(
        name=TaskTypeName.DOCUMENT_UPLOAD_REQUEST,
        preview_title="Upload missing {document_type_required}",
        preview_description="Document Upload Required",
        title="Upload missing {document_type_required} for {reference_document_type} {reference_number}",
        description="Please upload the missing {document_type_required} for {reference_document_type} {reference_number} for {supplier_name}.",
        category=TaskCategory.OTHER,
        context_panel_type=None,
        param_definition=pydantic_to_param_definition(DocumentUploadRequestParams),
    ),
    TaskTypeName.DATABASE_UPDATE_APPROVAL: TaskTypeV2Fields(
        name=TaskTypeName.DATABASE_UPDATE_APPROVAL,
        preview_title="{record_display}",
        preview_description="AI Database Update",
        title="Review AI suggestion for {record_display}",
        description="AI suggests updating {field_name} from '{current_display}' to '{suggested_display}' for {record_display}. Confidence: {confidence}%. Reason: {ai_reasoning}",
        category=TaskCategory.AI_OPERATIONS,
        context_panel_type=None,
        param_definition=pydantic_to_param_definition(DatabaseUpdateApprovalParams),
    ),
}


def sync_task_type_v2():
    for task_type_v2 in TASK_TYPE_V2_CONFIG:
        _, created = TaskTypeV2.objects.update_or_create(
            name=task_type_v2,
            defaults={
                "preview_description": TASK_TYPE_V2_CONFIG[
                    task_type_v2
                ].preview_description,
                "preview_title": TASK_TYPE_V2_CONFIG[task_type_v2].preview_title,
                "title": TASK_TYPE_V2_CONFIG[task_type_v2].title,
                "description": TASK_TYPE_V2_CONFIG[task_type_v2].description,
                "category": TASK_TYPE_V2_CONFIG[task_type_v2].category,
                "context_panel_type": TASK_TYPE_V2_CONFIG[
                    task_type_v2
                ].context_panel_type,
                "param_definition": TASK_TYPE_V2_CONFIG[
                    task_type_v2
                ].param_definition.model_dump_json(),
            },
        )
        if created:
            logger.info(f"Created TaskTypeV2: {task_type_v2}")

    # Delete any TaskTypeV2s that are in the DB but not in the CONFIGURATION
    for task_type_v2 in TaskTypeV2.objects.all():
        if task_type_v2.name not in TASK_TYPE_V2_CONFIG:
            logger.info(f"Deleting TaskTypeV2: {task_type_v2.name}")
            task_type_v2.delete()
