from typing import Optional

import structlog
from pydantic import BaseModel, Field

from didero.tasks.models import TaskActionType, TaskActionTypeName
from didero.tasks.schemas import (
    ActionExecutionType,
    TaskParamDefinition,
    TaskParamDefinitionConfig,
)

logger = structlog.get_logger(__name__)


class TaskActionTypeConfig(BaseModel):
    name: TaskActionTypeName
    title_template: str
    sub_text_template: str
    execution_type: ActionExecutionType
    execution_param_definition: TaskParamDefinitionConfig
    param_definition: TaskParamDefinitionConfig


class ApprovePurchaseOrderActionParams(BaseModel):
    order_id: str = Field(..., description="Order ID")
    approval_id: str = Field(..., description="Approval ID")


class DenyPurchaseOrderActionParams(BaseModel):
    order_id: str = Field(..., description="Order ID")
    approval_id: str = Field(..., description="Approval ID")


class OrderIdParams(BaseModel):
    order_id: str = Field(..., description="Order ID")


class ShipmentActionParams(BaseModel):
    purchase_order_id: str = Field(..., description="Purchase Order ID")
    shipment_id: str = Field(..., description="Shipment ID")


class OrderAcknowledgementParams(BaseModel):
    purchase_order_id: str = Field(..., description="Purchase Order ID")


class ApproveOAMismatchParams(BaseModel):
    purchase_order_id: str = Field(..., description="Purchase Order ID")
    validation_error: str = Field(
        ..., description="The validation error being approved"
    )


class ApproveDocumentMatchParams(BaseModel):
    document1_id: str = Field(..., description="First document ID")
    document1_type: str = Field(
        ..., description="First document type (invoice, purchase_order, etc.)"
    )
    document2_id: str = Field(..., description="Second document ID")
    document2_type: str = Field(
        ..., description="Second document type (invoice, purchase_order, etc.)"
    )


class RequestDocumentClarificationParams(BaseModel):
    document1_id: str = Field(..., description="First document ID")
    document1_type: str = Field(
        ..., description="First document type (invoice, purchase_order, etc.)"
    )
    document2_id: str = Field(..., description="Second document ID")
    document2_type: str = Field(
        ..., description="Second document type (invoice, purchase_order, etc.)"
    )
    supplier_name: str = Field(..., description="Supplier name for contact context")
    clarification_reason: str = Field(
        ..., description="Reason why clarification is needed"
    )


class UploadDocumentParams(BaseModel):
    document_type: str = Field(..., description="Type of document to upload")
    context_object_type: Optional[str] = Field(
        None, description="Type of object to link to"
    )
    context_object_id: Optional[str] = Field(
        None, description="ID of object to link to"
    )


class ExecuteDatabaseUpdateParams(BaseModel):
    model: str = Field(..., description="Model to update (e.g., orders.PurchaseOrder)")
    record_id: int = Field(..., description="Primary key of record to update")
    field_name: str = Field(..., description="Field to update")
    field_type: str = Field(..., description="Django field type")
    suggested_value: str = Field(..., description="AI suggested value (as string)")


class RejectDatabaseUpdateParams(BaseModel):
    rejection_reason: str = Field(..., description="Reason for rejecting the update")


# This should have a key for each TaskActionTypeName
# NOTE: If a TaskActionType is here and not in the DB, it will be created and/or updated in the DB
# NOTE: If a TaskActionType is removed from the CONFIGURATION, it will be deleted from the DB. Be careful with this.
TASK_ACTION_CONFIGURATION: dict[TaskActionTypeName, TaskActionTypeConfig] = {
    TaskActionTypeName.APPROVE_PURCHASE_ORDER: TaskActionTypeConfig(
        name=TaskActionTypeName.APPROVE_PURCHASE_ORDER,
        title_template="Approve Purchase Order",
        sub_text_template="Approve the Purchase Order in Didero",
        execution_type=ActionExecutionType.SERVER,
        execution_param_definition=TaskParamDefinitionConfig(
            parameters=[
                TaskParamDefinition(
                    name="order_id",
                    description="Order ID",
                    required=True,
                ),
                TaskParamDefinition(
                    name="approval_id",
                    description="Approval ID",
                    required=True,
                ),
            ]
        ),
        param_definition=TaskParamDefinitionConfig(parameters=[]),
    ),
    TaskActionTypeName.DENY_PURCHASE_ORDER: TaskActionTypeConfig(
        name=TaskActionTypeName.DENY_PURCHASE_ORDER,
        title_template="Deny Purchase Order",
        sub_text_template="Deny the Purchase Order in Didero",
        execution_type=ActionExecutionType.SERVER,
        execution_param_definition=TaskParamDefinitionConfig(
            parameters=[
                TaskParamDefinition(
                    name="order_id",
                    description="Order ID",
                    required=True,
                ),
                TaskParamDefinition(
                    name="approval_id",
                    description="Approval ID",
                    required=True,
                ),
            ]
        ),
        param_definition=TaskParamDefinitionConfig(parameters=[]),
    ),
    TaskActionTypeName.CONFIRM_PURCHASE_ORDER: TaskActionTypeConfig(
        name=TaskActionTypeName.CONFIRM_PURCHASE_ORDER,
        title_template="Confirm",
        sub_text_template="Issue this purchase order",
        execution_type=ActionExecutionType.SERVER,
        execution_param_definition=TaskParamDefinitionConfig(
            parameters=[
                TaskParamDefinition(
                    name="order_id",
                    description="Order ID",
                    required=True,
                ),
            ]
        ),
        param_definition=TaskParamDefinitionConfig(parameters=[]),
    ),
    TaskActionTypeName.CANCEL_PURCHASE_ORDER: TaskActionTypeConfig(
        name=TaskActionTypeName.CANCEL_PURCHASE_ORDER,
        title_template="Cancel",
        sub_text_template="Cancel this purchase order",
        execution_type=ActionExecutionType.SERVER,
        execution_param_definition=TaskParamDefinitionConfig(
            parameters=[
                TaskParamDefinition(
                    name="order_id",
                    description="Order ID",
                    required=True,
                ),
            ]
        ),
        param_definition=TaskParamDefinitionConfig(parameters=[]),
    ),
    TaskActionTypeName.CONFIRM_ORDER_ACKNOWLEDGEMENT: TaskActionTypeConfig(
        name=TaskActionTypeName.CONFIRM_ORDER_ACKNOWLEDGEMENT,
        title_template="Confirm",
        sub_text_template="Confirm this order acknowledgment and update purchase order status",
        execution_type=ActionExecutionType.SERVER,
        execution_param_definition=TaskParamDefinitionConfig(
            parameters=[
                TaskParamDefinition(
                    name="purchase_order_id",
                    description="Purchase Order ID",
                    required=True,
                ),
            ]
        ),
        param_definition=TaskParamDefinitionConfig(parameters=[]),
    ),
    TaskActionTypeName.REJECT_ORDER_ACKNOWLEDGEMENT: TaskActionTypeConfig(
        name=TaskActionTypeName.REJECT_ORDER_ACKNOWLEDGEMENT,
        title_template="Reject",
        sub_text_template="Reject this order acknowledgment and keep current purchase order status",
        execution_type=ActionExecutionType.SERVER,
        execution_param_definition=TaskParamDefinitionConfig(
            parameters=[
                TaskParamDefinition(
                    name="purchase_order_id",
                    description="Purchase Order ID",
                    required=True,
                ),
            ]
        ),
        param_definition=TaskParamDefinitionConfig(parameters=[]),
    ),
    TaskActionTypeName.APPROVE_OA_MISMATCH: TaskActionTypeConfig(
        name=TaskActionTypeName.APPROVE_OA_MISMATCH,
        title_template="Approve Mismatch",
        sub_text_template="Accept the order acknowledgment despite validation errors",
        execution_type=ActionExecutionType.SERVER,
        execution_param_definition=TaskParamDefinitionConfig(
            parameters=[
                TaskParamDefinition(
                    name="purchase_order_id",
                    description="Purchase Order ID",
                    required=True,
                ),
                TaskParamDefinition(
                    name="validation_error",
                    description="The validation error being approved",
                    required=True,
                ),
            ]
        ),
        param_definition=TaskParamDefinitionConfig(parameters=[]),
    ),
    TaskActionTypeName.SEND_EMAIL: TaskActionTypeConfig(
        name=TaskActionTypeName.SEND_EMAIL,
        title_template="Reply to supplier",
        sub_text_template="We'll draft you an email which you can edit before sending",
        execution_type=ActionExecutionType.CLIENT,
        execution_param_definition=TaskParamDefinitionConfig(
            parameters=[
                TaskParamDefinition(
                    name="email_type",
                    description="The type of email to send",
                    required=False,  # Optional since it might come from param_definition
                ),
                TaskParamDefinition(
                    name="email_to",
                    description="The email address to send the email to",
                    required=True,
                ),
                TaskParamDefinition(
                    name="email_cc",
                    description="The email address to send the email to",
                    required=False,
                ),
                TaskParamDefinition(
                    name="email_bcc",
                    description="The email address to send the email to",
                    required=False,
                ),
                TaskParamDefinition(
                    name="email_subject",
                    description="The subject of the email",
                    required=True,
                ),
                TaskParamDefinition(
                    name="email_body",
                    description="The body of the email",
                    required=True,
                ),
                TaskParamDefinition(
                    name="in_reply_to",
                    description="Message ID to reply to for threading",
                    required=False,
                ),
                TaskParamDefinition(
                    name="thread_id",
                    description="Email thread ID for grouping related messages",
                    required=False,
                ),
            ]
        ),
        param_definition=TaskParamDefinitionConfig(parameters=[]),
    ),
    TaskActionTypeName.OPS_ADD_COMMENT: TaskActionTypeConfig(
        name=TaskActionTypeName.OPS_ADD_COMMENT,
        title_template="{button_text}",
        sub_text_template="{button_sub_text}",
        execution_type=ActionExecutionType.SERVER,
        execution_param_definition=TaskParamDefinitionConfig(
            parameters=[
                TaskParamDefinition(
                    name="parent_object_type",
                    description="This field will be automatically populated based on the parent object",
                    required=True,
                ),
                TaskParamDefinition(
                    name="parent_object_id",
                    description="This field will be automatically populated based on the parent object",
                    required=True,
                ),
                TaskParamDefinition(
                    name="comment",
                    description="The comment to add",
                    required=True,
                ),
            ]
        ),
        param_definition=TaskParamDefinitionConfig(
            parameters=[
                TaskParamDefinition(
                    name="button_text",
                    description="The text to display on the button",
                    required=True,
                ),
                TaskParamDefinition(
                    name="button_sub_text",
                    description="The subtext to display on the button (displayed below the button text)",
                    required=True,
                ),
            ]
        ),
    ),
    TaskActionTypeName.ADD_COMMENT: TaskActionTypeConfig(
        name=TaskActionTypeName.ADD_COMMENT,
        title_template="Add a comment to the {object_type}",
        sub_text_template="Add a comment to the {object_type}",
        execution_type=ActionExecutionType.CLIENT,
        execution_param_definition=TaskParamDefinitionConfig(
            parameters=[
                TaskParamDefinition(
                    name="parent_object_type",
                    description="This field will be automatically populated based on the parent object",
                    required=True,
                ),
                TaskParamDefinition(
                    name="parent_object_id",
                    description="This field will be automatically populated based on the parent object",
                    required=True,
                ),
            ]
        ),
        param_definition=TaskParamDefinitionConfig(
            parameters=[
                TaskParamDefinition(
                    name="object_type",
                    description="The type of object to add the comment to",
                    required=True,
                ),
            ]
        ),
    ),
    TaskActionTypeName.AUTO_COMMENT: TaskActionTypeConfig(
        name=TaskActionTypeName.AUTO_COMMENT,
        title_template="{button_text}",
        sub_text_template="{button_sub_text}",
        execution_type=ActionExecutionType.SERVER,
        execution_param_definition=TaskParamDefinitionConfig(
            parameters=[
                TaskParamDefinition(
                    name="parent_object_type",
                    description="This field will be automatically populated based on the parent object",
                    required=True,
                ),
                TaskParamDefinition(
                    name="parent_object_id",
                    description="This field will be automatically populated based on the parent object",
                    required=True,
                ),
                TaskParamDefinition(
                    name="comment_text",
                    description="The comment text to add automatically",
                    required=True,
                ),
            ]
        ),
        param_definition=TaskParamDefinitionConfig(
            parameters=[
                TaskParamDefinition(
                    name="button_text",
                    description="The text to display on the button",
                    required=True,
                ),
                TaskParamDefinition(
                    name="button_sub_text",
                    description="The subtext to display on the button (displayed below the button text)",
                    required=True,
                ),
            ]
        ),
    ),
    TaskActionTypeName.APPROVE_FREIGHT_CHARGE: TaskActionTypeConfig(
        name=TaskActionTypeName.APPROVE_FREIGHT_CHARGE,
        title_template="Approve Freight Charge",
        sub_text_template="Approve the freight charge for the purchase order",
        execution_type=ActionExecutionType.SERVER,
        execution_param_definition=TaskParamDefinitionConfig(
            parameters=[
                TaskParamDefinition(
                    name="purchase_order_id",
                    description="The ID of the purchase order",
                    required=True,
                ),
                TaskParamDefinition(
                    name="freight_charge",
                    description="The freight charge amount",
                    required=False,
                ),
            ]
        ),
        param_definition=TaskParamDefinitionConfig(parameters=[]),
    ),
    TaskActionTypeName.APPROVE_PRICE_CHANGE: TaskActionTypeConfig(
        name=TaskActionTypeName.APPROVE_PRICE_CHANGE,
        title_template="Approve Price Change",
        sub_text_template="Approve the price change for the purchase order",
        execution_type=ActionExecutionType.SERVER,
        execution_param_definition=TaskParamDefinitionConfig(
            parameters=[
                TaskParamDefinition(
                    name="purchase_order_id",
                    description="The ID of the purchase order",
                    required=True,
                ),
                TaskParamDefinition(
                    name="price_change",
                    description="The price change details",
                    required=True,
                ),
                TaskParamDefinition(
                    name="system_name",
                    description="The name of the system to make the price change",
                    required=True,
                ),
            ]
        ),
        param_definition=TaskParamDefinitionConfig(parameters=[]),
    ),
    TaskActionTypeName.APPROVE_ADDRESS_CHANGE: TaskActionTypeConfig(
        name=TaskActionTypeName.APPROVE_ADDRESS_CHANGE,
        title_template="Approve Address Change",
        sub_text_template="Approve the address change for the purchase order",
        execution_type=ActionExecutionType.SERVER,
        execution_param_definition=TaskParamDefinitionConfig(
            parameters=[
                TaskParamDefinition(
                    name="purchase_order_id",
                    description="The ID of the purchase order",
                    required=True,
                ),
                TaskParamDefinition(
                    name="address_change",
                    description="The address change details",
                    required=False,
                ),
            ]
        ),
        param_definition=TaskParamDefinitionConfig(parameters=[]),
    ),
    TaskActionTypeName.UPDATE_NETSUITE: TaskActionTypeConfig(
        name=TaskActionTypeName.UPDATE_NETSUITE,
        title_template="Update ERP",
        sub_text_template="Update ERP with the details of the purchase order",
        execution_type=ActionExecutionType.SERVER_ASYNC,
        execution_param_definition=TaskParamDefinitionConfig(
            parameters=[
                TaskParamDefinition(
                    name="purchase_order_id",
                    description="The ID of the purchase order to update",
                    required=True,
                ),
                TaskParamDefinition(
                    name="shipment_id",
                    description="The ID of the shipment to update",
                    required=True,
                ),
            ]
        ),
        param_definition=TaskParamDefinitionConfig(parameters=[]),
    ),
    TaskActionTypeName.CONFIRM_SHIPMENT: TaskActionTypeConfig(
        name=TaskActionTypeName.CONFIRM_SHIPMENT,
        title_template="Confirm Shipment",
        sub_text_template="Verify shipment details and update NetSuite",
        execution_type=ActionExecutionType.SERVER,
        execution_param_definition=TaskParamDefinitionConfig(
            parameters=[
                TaskParamDefinition(
                    name="purchase_order_id",
                    description="The ID of the purchase order",
                    required=True,
                ),
                TaskParamDefinition(
                    name="shipment_id",
                    description="The ID of the shipment to confirm",
                    required=True,
                ),
            ]
        ),
        param_definition=TaskParamDefinitionConfig(parameters=[]),
    ),
    TaskActionTypeName.CANCEL_SHIPMENT: TaskActionTypeConfig(
        name=TaskActionTypeName.CANCEL_SHIPMENT,
        title_template="Cancel Shipment",
        sub_text_template="Mark shipment as invalid and do not update NetSuite",
        execution_type=ActionExecutionType.SERVER,
        execution_param_definition=TaskParamDefinitionConfig(
            parameters=[
                TaskParamDefinition(
                    name="purchase_order_id",
                    description="The ID of the purchase order",
                    required=True,
                ),
                TaskParamDefinition(
                    name="shipment_id",
                    description="The ID of the shipment to cancel",
                    required=True,
                ),
            ]
        ),
        param_definition=TaskParamDefinitionConfig(parameters=[]),
    ),
    TaskActionTypeName.APPROVE_DOCUMENT_MATCH: TaskActionTypeConfig(
        name=TaskActionTypeName.APPROVE_DOCUMENT_MATCH,
        title_template="Approve Match",
        sub_text_template="Approve the document match and proceed with processing",
        execution_type=ActionExecutionType.SERVER,
        execution_param_definition=TaskParamDefinitionConfig(
            parameters=[
                TaskParamDefinition(
                    name="document1_id",
                    description="First document ID",
                    required=True,
                ),
                TaskParamDefinition(
                    name="document1_type",
                    description="First document type (invoice, purchase_order, etc.)",
                    required=True,
                ),
                TaskParamDefinition(
                    name="document2_id",
                    description="Second document ID",
                    required=True,
                ),
                TaskParamDefinition(
                    name="document2_type",
                    description="Second document type (invoice, purchase_order, etc.)",
                    required=True,
                ),
            ]
        ),
        param_definition=TaskParamDefinitionConfig(parameters=[]),
    ),
    TaskActionTypeName.REQUEST_DOCUMENT_CLARIFICATION: TaskActionTypeConfig(
        name=TaskActionTypeName.REQUEST_DOCUMENT_CLARIFICATION,
        title_template="Request Document Clarification",
        sub_text_template="Contact supplier for clarification on document matching",
        execution_type=ActionExecutionType.CLIENT,
        execution_param_definition=TaskParamDefinitionConfig(
            parameters=[
                TaskParamDefinition(
                    name="email_type",
                    description="The type of email to send",
                    required=False,  # Optional since it might come from param_definition
                ),
                TaskParamDefinition(
                    name="email_to",
                    description="The email address to send the email to",
                    required=True,
                ),
                TaskParamDefinition(
                    name="email_cc",
                    description="The email address to send the email to",
                    required=False,
                ),
                TaskParamDefinition(
                    name="email_bcc",
                    description="The email address to send the email to",
                    required=False,
                ),
                TaskParamDefinition(
                    name="email_subject",
                    description="The subject of the email",
                    required=True,
                ),
                TaskParamDefinition(
                    name="email_body",
                    description="The body of the email",
                    required=True,
                ),
            ]
        ),
        param_definition=TaskParamDefinitionConfig(parameters=[]),
    ),
    TaskActionTypeName.UPLOAD_DOCUMENT: TaskActionTypeConfig(
        name=TaskActionTypeName.UPLOAD_DOCUMENT,
        title_template="Upload Document",
        sub_text_template="Upload the requested document",
        execution_type=ActionExecutionType.CLIENT,
        execution_param_definition=TaskParamDefinitionConfig(
            parameters=[
                TaskParamDefinition(
                    name="document_type",
                    description="Type of document to upload",
                    required=True,
                ),
                TaskParamDefinition(
                    name="context_object_type",
                    description="Type of object to link to",
                    required=False,
                ),
                TaskParamDefinition(
                    name="context_object_id",
                    description="ID of object to link to",
                    required=False,
                ),
            ]
        ),
        param_definition=TaskParamDefinitionConfig(parameters=[]),
    ),
    TaskActionTypeName.EXECUTE_DATABASE_UPDATE: TaskActionTypeConfig(
        name=TaskActionTypeName.EXECUTE_DATABASE_UPDATE,
        title_template="Approve Update",
        sub_text_template="Apply the AI suggested database update",
        execution_type=ActionExecutionType.SERVER,
        execution_param_definition=TaskParamDefinitionConfig(
            parameters=[
                TaskParamDefinition(
                    name="model",
                    description="Model to update (e.g., orders.PurchaseOrder)",
                    required=True,
                ),
                TaskParamDefinition(
                    name="record_id",
                    description="Primary key of record to update",
                    required=True,
                ),
                TaskParamDefinition(
                    name="field_name",
                    description="Field to update",
                    required=True,
                ),
                TaskParamDefinition(
                    name="field_type",
                    description="Django field type",
                    required=True,
                ),
                TaskParamDefinition(
                    name="suggested_value",
                    description="AI suggested value (as string)",
                    required=True,
                ),
            ]
        ),
        param_definition=TaskParamDefinitionConfig(parameters=[]),
    ),
    TaskActionTypeName.REJECT_DATABASE_UPDATE: TaskActionTypeConfig(
        name=TaskActionTypeName.REJECT_DATABASE_UPDATE,
        title_template="Reject Update",
        sub_text_template="Reject the AI suggested database update",
        execution_type=ActionExecutionType.SERVER,
        execution_param_definition=TaskParamDefinitionConfig(
            parameters=[
                TaskParamDefinition(
                    name="rejection_reason",
                    description="Reason for rejecting the update",
                    required=False,
                ),
            ]
        ),
        param_definition=TaskParamDefinitionConfig(parameters=[]),
    ),
}


# This function is called on app startup to ensure that the TaskActionType objects are up to date
# If a TaskActionType is here and not in the DB, it will be created and/or updated in the DB
# NOTE: If a TaskActionType is removed from the CONFIGURATION, it will be deleted from the DB. Be careful with this.
def sync_task_action_types():
    for task_action_type in TASK_ACTION_CONFIGURATION:
        _, created = TaskActionType.objects.update_or_create(
            name=task_action_type,
            defaults={
                "title_template": TASK_ACTION_CONFIGURATION[
                    task_action_type
                ].title_template,
                "sub_text_template": TASK_ACTION_CONFIGURATION[
                    task_action_type
                ].sub_text_template,
                "execution_type": TASK_ACTION_CONFIGURATION[
                    task_action_type
                ].execution_type,
                "execution_param_definition": TASK_ACTION_CONFIGURATION[
                    task_action_type
                ].execution_param_definition.model_dump_json(),
                "param_definition": TASK_ACTION_CONFIGURATION[
                    task_action_type
                ].param_definition.model_dump_json(),
            },
        )
        if created:
            logger.info(f"Created TaskActionType: {task_action_type}")

    # Delete any TaskActionTypes that are in the DB but not in the CONFIGURATION
    for task_action_type in TaskActionType.objects.all():
        if task_action_type.name not in TASK_ACTION_CONFIGURATION:
            task_action_type.delete()
