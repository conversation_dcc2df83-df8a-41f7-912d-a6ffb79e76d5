"""
Django settings for clippet project.

Generated by 'django-admin startproject' using Django 4.1.5.

For more information on this file, see
https://docs.djangoproject.com/en/4.1/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/4.1/ref/settings/
"""

from os import environ

import boto3
from kombu.utils.url import safequote
from opensearchpy import RequestsHttpConnection
from requests_aws4auth import AWS4Auth

from didero.settings.common import *  # noqa: F401, F403
from didero.settings.common import LOGGING, OPENSEARCH_DSL

# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/4.1/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
# WARNING: Rotating the secret key affects encryption - the lib needs patching
# https://github.com/georgemarshall/django-cryptography/issues/75#issuecomment-1113963744

# SECURITY WARNING: debug should always be False in production
DEBUG = False

SECRET_KEY = environ.get("DJANGO_SECRET_KEY", ".")

_API_HOST = "api.didero.ai"
API_URL = f"https://{_API_HOST}"
DEMO_APP_URL = "https://demo-app.didero.ai"
APP_URL = "https://app.didero.ai"
AWS_APP_URL = "https://app.mvp1.didero.ai"
CSRF_TRUSTED_ORIGINS = [API_URL, AWS_APP_URL]

ALLOWED_HOSTS = [_API_HOST]
PAUSE_MAIL_INGESTION = False

STORAGES = {
    "default": {
        "BACKEND": "storages.backends.s3.S3Storage",
        "OPTIONS": {
            "region_name": "us-east-2",
            "bucket_name": "didero-docs-prod",
            "object_parameters": {
                "ContentDisposition": "inline",
            },
            "access_key": environ.get("S3_ACCESS_KEY"),
            "secret_key": environ.get("S3_SECRET_KEY"),
            "default_acl": "private",
            "max_memory_size": 5000000,
            "querystring_auth": True,
        },
    },
    "staticfiles": {
        "BACKEND": "django.contrib.staticfiles.storage.StaticFilesStorage",
    },
}

LOGGING["loggers"].update(
    {
        "didero": {
            "handlers": ["stdout", "stderr"],
            "level": "INFO",
            "propagate": False,
        },
    }
)

DIDERO_ENVIRONMENT = environ.get("DIDERO_ENVIRONMENT")
safe_quoted_access_key = safequote(environ.get("AWS_ACCESS_KEY"))
safe_quoted_secret_key = safequote(environ.get("AWS_SECRET_ACCESS_KEY"))
CELERY_BROKER_URL = f"sqs://{safe_quoted_access_key}:{safe_quoted_secret_key}@"  # not to worry, Celery does not log the secret key.
CELERY_BROKER_TRANSPORT_OPTIONS = {
    "region": "us-east-2",
    "worker_enable_remote_control": False,
    "predefined_queues": {  #
        "default": {
            "url": "https://sqs.us-east-2.amazonaws.com/855701137767/didero-prod-default",
            "access_key": environ.get("AWS_ACCESS_KEY"),
            "secret_key": environ.get("AWS_SECRET_ACCESS_KEY"),
        },
        "ai_workflows": {
            "url": "https://sqs.us-east-2.amazonaws.com/855701137767/didero-prod-ai_workflows",
            "access_key": environ.get("AWS_ACCESS_KEY"),
            "secret_key": environ.get("AWS_SECRET_ACCESS_KEY"),
        },
        "bulk_supplier_imports": {
            "url": "https://sqs.us-east-2.amazonaws.com/855701137767/didero-prod-bulk_supplier_imports",
            "access_key": environ.get("AWS_ACCESS_KEY"),
            "secret_key": environ.get("AWS_SECRET_ACCESS_KEY"),
        },
        "email_backfills": {
            "url": "https://sqs.us-east-2.amazonaws.com/855701137767/didero-prod-email_backfills",
            "access_key": environ.get("AWS_ACCESS_KEY"),
            "secret_key": environ.get("AWS_SECRET_ACCESS_KEY"),
        },
        "periodic_tasks": {
            "url": "https://sqs.us-east-2.amazonaws.com/855701137767/didero-prod-periodic_tasks",
            "access_key": environ.get("AWS_ACCESS_KEY"),
            "secret_key": environ.get("AWS_SECRET_ACCESS_KEY"),
        },
        "task_email_notifications": {
            "url": "https://sqs.us-east-2.amazonaws.com/855701137767/didero-prod-task_email_notifications",
            "access_key": environ.get("AWS_ACCESS_KEY"),
            "secret_key": environ.get("AWS_SECRET_ACCESS_KEY"),
        },
    },
}

CORS_ALLOWED_ORIGINS = [
    APP_URL,
    AWS_APP_URL,
    "https://didero.ai",
    "http://localhost:3000",
    "https://didero.vercel.app",
    "https://app.didero.ai",
    "https://demo-app.didero.ai",
]

# OpenTelemetry metrics configuration
# Set OTEL_METRICS_ENABLED=true in environment to enable metrics
OTEL_METRICS_ENABLED = environ.get("OTEL_METRICS_ENABLED", "false").lower() == "true"

# OpenSearch IAM Authentication Configuration for Production
# Set OPENSEARCH_USE_IAM_AUTH=true in environment to enable
if environ.get("OPENSEARCH_USE_IAM_AUTH", "false").lower() == "true":
    # Override the IAM auth setting from common.py
    OPENSEARCH_USE_IAM_AUTH = True

    # Get AWS credentials for IAM authentication
    session = boto3.Session()
    credentials = session.get_credentials()

    # Configure AWS4Auth for OpenSearch
    # Note: Update the region if production OpenSearch is in a different region
    awsauth = AWS4Auth(
        credentials.access_key,
        credentials.secret_key,
        "us-east-2",  # Update if production is in different region
        "es",
        session_token=credentials.token,
    )

    # Parse the host to get just the domain
    host = OPENSEARCH_HOST.replace("https://", "").replace("http://", "").rstrip("/")

    # Update OpenSearch DSL configuration for IAM auth
    # Need to provide host in correct format for RequestsHttpConnection
    OPENSEARCH_DSL["default"] = {
        "hosts": [{"host": host, "port": 443}],
        "http_auth": awsauth,
        "connection_class": RequestsHttpConnection,
        "use_ssl": True,
        "verify_certs": True,
        "timeout": 30,
        "max_retries": 3,
        "retry_on_timeout": True,
        "http_compress": True,
        "maxsize": 25,
        "pool_maxsize": 25,
    }
