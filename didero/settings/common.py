"""
Django settings for didero project.

Generated by 'django-admin startproject' using Django 4.2.3.

For more information on this file, see
https://docs.djangoproject.com/en/4.2/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/4.2/ref/settings/
"""

import json
import os
import sys
from os import environ
from pathlib import Path

import structlog
from celery.schedules import crontab

from didero.encryption import derive_key

# todo change back to True
PAUSE_MAIL_INGESTION = False
# This is how long one cycle is for us to go through every (credential, supplier) pair
INBOX_REFRESH_TIME_INTERVAL_IN_SECONDS = 5 * 60

# This is currently used just for the demo account; we may later need to exclude @didero or others
DEMO_PRIMARY_EMAIL_DOMAIN = "ecotechinnovations.com"
IGNORED_EMAIL_DOMAINS = [DEMO_PRIMARY_EMAIL_DOMAIN]

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent

# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/4.2/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = environ.get("DJANGO_SECRET_KEY", "didero-dev-12aa9758")
OPENAI_API_KEY = environ.get("OPENAI_API_KEY")
ANTHROPIC_API_KEY = environ.get("ANTHROPIC_API_KEY")
PINECONE_API_KEY = environ.get("PINECONE_API_KEY")
DIDERO_ENVIRONMENT = environ.get("DIDERO_ENVIRONMENT")

LANGFUSE_SECRET_KEY = environ.get("LANGFUSE_SECRET_KEY")
LANGFUSE_PUBLIC_KEY = environ.get("LANGFUSE_PUBLIC_KEY")
LANGFUSE_HOST = environ.get("LANGFUSE_HOST")

ZAPIER_WEBHOOK_ALERT_OPS_TEAM = environ.get("ZAPIER_WEBHOOK_ALERT_OPS_TEAM")
ZAPIER_WEBHOOK_POST_TO_SPREADSHEET = environ.get("ZAPIER_WEBHOOK_POST_TO_SPREADSHEET")
ZAPIER_WEBHOOK_ALERT_OPS_TEAM_GENERIC_MESSAGE_FOR_SLACK = environ.get(
    "ZAPIER_WEBHOOK_ALERT_OPS_TEAM_GENERIC_MESSAGE_FOR_SLACK"
)

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = True

DEMO_APP_URL = "http://localhost:3000"
APP_URL = "http://localhost:3000"
API_URL = "http://localhost:8000"
ALLOWED_HOSTS = ["0.0.0.0", "127.0.0.1", "localhost", "staging-api.didero.ai"]

AUTH_USER_MODEL = "users.User"
# Application definition

INSTALLED_APPS = [
    "django.contrib.admin",
    "django.contrib.auth",
    "django.contrib.contenttypes",
    "django.contrib.sessions",
    "django.contrib.messages",
    "django.contrib.staticfiles",
    "django_cleanup.apps.CleanupConfig",
    "django_structlog",
    "rest_framework",
    "rest_framework.authtoken",
    "drf_standardized_errors",
    "drf_spectacular",
    "actstream",
    "auditlog",
    "admin_extra_buttons",
    "django_admin_inline_paginator",
    "corsheaders",
    "django_filters",
    "djmoney",
    "djmoney.contrib.exchange",
    "didero.activity_log",
    "didero.addresses",
    "didero.bulk_uploads",
    "didero.common",
    "didero.demo",
    "didero.documents",
    "didero.emails",
    "didero.notifications",
    "didero.orders",
    "didero.insights",
    "didero.integrations",
    "didero.invoices",
    "didero.items",
    "didero.runtime_configs",
    "didero.search",
    "didero.shipments",
    "didero.shipping_documents",
    "didero.suppliers",
    "didero.tasks",
    "didero.users",
    "didero.workflows",
    "django_opensearch_dsl",
    "django_celery_beat",
    "django_celery_results",
    "django_extensions",
    "taggit",
]
TAGGIT_CASE_INSENSITIVE = True

SERIALIZATION_MODULES = {"json": "djmoney.serializers"}

MIDDLEWARE = [
    "didero.middleware.HealthCheckMiddleware",
    "didero.telemetry.middleware.OpenTelemetryMiddleware",  # Add OpenTelemetry metrics middleware
    "corsheaders.middleware.CorsMiddleware",
    "django.middleware.security.SecurityMiddleware",
    "whitenoise.middleware.WhiteNoiseMiddleware",
    "django.contrib.sessions.middleware.SessionMiddleware",
    "django.middleware.common.CommonMiddleware",
    # "django.middleware.csrf.CsrfViewMiddleware",
    "django.contrib.auth.middleware.AuthenticationMiddleware",
    "django_currentuser.middleware.ThreadLocalUserMiddleware",
    "django.contrib.messages.middleware.MessageMiddleware",
    "django.middleware.clickjacking.XFrameOptionsMiddleware",
    "didero.middleware.AdminInterfaceDetectionMiddleware",  # Detect admin interface requests
    "djangorestframework_camel_case.middleware.CamelCaseMiddleWare",
    "django_structlog.middlewares.RequestMiddleware",
    "auditlog.middleware.AuditlogMiddleware",
]

ROOT_URLCONF = "didero.urls"

TEMPLATES = [
    {
        "BACKEND": "django.template.backends.django.DjangoTemplates",
        "DIRS": [os.path.join(BASE_DIR, "templates")],
        "APP_DIRS": True,
        "OPTIONS": {
            "context_processors": [
                "django.template.context_processors.debug",
                "django.template.context_processors.request",
                "django.contrib.auth.context_processors.auth",
                "django.contrib.messages.context_processors.messages",
            ],
        },
    },
]

WSGI_APPLICATION = "didero.wsgi.application"

SECURE_PROXY_SSL_HEADER = ("HTTP_X_FORWARDED_PROTO", "https")

# Database
# https://docs.djangoproject.com/en/4.2/ref/settings/#databases

DATABASES = {
    "default": {
        "ENGINE": "django.db.backends.postgresql",
        "HOST": environ.get("POSTGRES_HOST", "localhost"),
        "PASSWORD": environ.get("POSTGRES_PASSWORD", ""),
        "USER": "didero",
        "NAME": environ.get("POSTGRES_DB", "didero"),
    }
}

SESSION_ENGINE = "django.contrib.sessions.backends.cached_db"
CACHES = {
    "default": {
        "BACKEND": "django.core.cache.backends.redis.RedisCache",
        "LOCATION": environ.get("DJANGO_CACHE_BACKEND_URL", "redis://localhost:6379/0"),
    }
}

# Password validation
# https://docs.djangoproject.com/en/4.2/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        "NAME": "django.contrib.auth.password_validation.UserAttributeSimilarityValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.MinimumLengthValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.CommonPasswordValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.NumericPasswordValidator",
    },
]


# Internationalization
# https://docs.djangoproject.com/en/4.2/topics/i18n/

LANGUAGE_CODE = "en-us"

TIME_ZONE = "UTC"

USE_I18N = True

USE_TZ = True

# OpenTelemetry settings
OTEL_SETTINGS = {
    "SERVICE_NAME": "didero-api",
    "ENVIRONMENT": DIDERO_ENVIRONMENT,
    "EXPORTER_OTLP_ENDPOINT": os.environ.get(
        "OTEL_EXPORTER_OTLP_ENDPOINT", "http://localhost:4317"
    ),
}


# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/4.2/howto/static-files/

STATIC_URL = "static/"
STATIC_ROOT = "_static/"

# Default primary key field type
# https://docs.djangoproject.com/en/4.2/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = "django.db.models.BigAutoField"

REST_FRAMEWORK = {
    "EXCEPTION_HANDLER": "drf_standardized_errors.handler.exception_handler",
    "DEFAULT_AUTHENTICATION_CLASSES": [
        "rest_framework.authentication.TokenAuthentication",
        "rest_framework.authentication.SessionAuthentication",
    ],
    "DEFAULT_PERMISSION_CLASSES": [
        "rest_framework.permissions.IsAuthenticated",
    ],
    "DEFAULT_RENDERER_CLASSES": (
        "djangorestframework_camel_case.render.CamelCaseJSONRenderer",
        "djangorestframework_camel_case.render.CamelCaseBrowsableAPIRenderer",
    ),
    "DEFAULT_SCHEMA_CLASS": "drf_spectacular.openapi.AutoSchema",
    "DEFAULT_PARSER_CLASSES": (
        "djangorestframework_camel_case.parser.CamelCaseJSONParser",
        "djangorestframework_camel_case.parser.CamelCaseFormParser",
        "djangorestframework_camel_case.parser.CamelCaseMultiPartParser",
    ),
    "JSON_UNDERSCOREIZE": {
        "no_underscore_before_number": False,
    },
}

INBOUND_MAIL_DOMAIN = "bot.didero.ai"
EMAIL_HOST = "smtp.postmarkapp.com"
EMAIL_PORT = 2525
EMAIL_HOST_USER = environ.get("SMTP_USER")
EMAIL_HOST_PASSWORD = environ.get("SMTP_PASS")
EMAIL_USE_TLS = True
DEFAULT_FROM_EMAIL = "<EMAIL>"
SERVER_EMAIL = "<EMAIL>"
DEFAULT_EXCEPTION_REPORTER_FILTER = "didero.utils.utils.EmailExceptionFilter"

MEDIA_ROOT = "_media/"

STORAGES = {
    "default": {
        "BACKEND": "storages.backends.s3.S3Storage",
        "OPTIONS": {
            "region_name": "us-east-2",
            "bucket_name": "didero-docs-development",
            "object_parameters": {
                "ContentDisposition": "inline",
            },
            "access_key": environ.get("S3_ACCESS_KEY"),
            "secret_key": environ.get("S3_SECRET_KEY"),
            "default_acl": "private",
            "max_memory_size": 5000000,
            "querystring_auth": True,
        },
    },
    "staticfiles": {
        "BACKEND": "django.contrib.staticfiles.storage.StaticFilesStorage",
    },
}

LOGGING = {
    "version": 1,
    "disable_existing_loggers": False,
    "formatters": {
        "json_formatter": {
            "()": structlog.stdlib.ProcessorFormatter,
            "processor": structlog.processors.JSONRenderer(),
        },
        "json": {
            "()": "didero.logging.django_json_log_formatter.JSONFormatter",
        },
        "plain_console": {
            "()": structlog.stdlib.ProcessorFormatter,
            "processor": structlog.dev.ConsoleRenderer(),
        },
        "key_value": {
            "()": structlog.stdlib.ProcessorFormatter,
            "processor": structlog.processors.KeyValueRenderer(
                key_order=["timestamp", "level", "event", "logger"]
            ),
        },
    },
    "filters": {
        "require_debug_true": {
            "()": "django.utils.log.RequireDebugTrue",
        }
    },
    "handlers": {
        # "file": {
        #     "level": "DEBUG",
        #     "class": "logging.FileHandler",
        #     "filename": "/path/to/django/debug.log",
        # },
        "stderr": {
            "level": "ERROR",
            "class": "logging.StreamHandler",
            "stream": sys.stderr,
            "formatter": "json",
        },
        "stdout": {
            "level": "DEBUG",
            "class": "logging.StreamHandler",
            "stream": sys.stdout,
            "formatter": "json",
        },
        "console": {
            "level": "DEBUG",
            "filters": ["require_debug_true"],
            "class": "logging.StreamHandler",
            "formatter": "plain_console",
        },
        # "json_file": {
        #     "class": "logging.handlers.WatchedFileHandler",
        #     "filename": "logs/json.log",
        #     "formatter": "json_formatter",
        # },
        # "flat_line_file": {
        #     "class": "logging.handlers.WatchedFileHandler",
        #     "filename": "logs/flat_line.log",
        #     "formatter": "key_value",
        # },
    },
    "loggers": {
        "didero": {
            "handlers": ["console"],
            "level": "INFO",
            "propagate": False,
        },
        "django_structlog.celery.receivers": {
            "handlers": ["stdout", "stderr"],
            "level": "INFO",
            "propagate": False,
        },
        "celery": {
            "handlers": ["stdout", "stderr"],
            "level": "INFO",
            "propagate": False,
        },
        "django.request": {
            "handlers": ["stdout", "stderr"],
            "level": "INFO",
            "propagate": True,
        },
        "django.server": {
            "handlers": ["stdout", "stderr"],
            "level": "INFO",
            "propagate": True,
        },
        "django.db.backends": {
            "level": "INFO",
            "handlers": ["console"],
        },
    },
}

DJANGO_STRUCTLOG_CELERY_ENABLED = False
from didero.telemetry.structlog_processors import add_trace_context

structlog.configure(
    processors=[
        structlog.contextvars.merge_contextvars,
        structlog.stdlib.filter_by_level,
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.UnicodeDecoder(),
        add_trace_context,  # Add OpenTelemetry trace context to logs
        structlog.stdlib.ProcessorFormatter.wrap_for_formatter,
    ],
    logger_factory=structlog.stdlib.LoggerFactory(),
    cache_logger_on_first_use=True,
)

CORS_ALLOW_CREDENTIALS = True
CORS_ALLOWED_ORIGINS = [APP_URL, "http://localhost:3000"]
CORS_ALLOWED_ORIGIN_REGEXES = [
    r"^https:\/\/didero-[a-zA-Z0-9-]+-dideroai\.vercel\.app$",
]

CORS_ALLOW_HEADERS = (
    "accept",
    "Authorization",
    "content-type",
    "user-agent",
    "x-csrftoken",
    "x-requested-with",
)

# todo: crypto settings
DIDERO_ENVIRONMENT = environ.get("DIDERO_ENVIRONMENT", "dev")
DIDERO_DEV_NAME = environ.get("DIDERO_DEVELOPER_NAME", "no-developer-name")

# Google Sheets Configuration
GOOGLE_SHEET_FEEDBACK_ID = environ.get("GOOGLE_SHEET_FEEDBACK_ID")
GOOGLE_SHEET_FEEDBACK_TAB = environ.get("GOOGLE_SHEET_FEEDBACK_TAB", "AI Feedback")
GOOGLE_SERVICE_ACCOUNT_FILE = environ.get("GOOGLE_SERVICE_ACCOUNT_FILE")

# Langfuse Configuration
LANGFUSE_SECRET_KEY = environ.get("LANGFUSE_SECRET_KEY")
LANGFUSE_PUBLIC_KEY = environ.get("LANGFUSE_PUBLIC_KEY")
LANGFUSE_HOST = environ.get("LANGFUSE_HOST")

# Browserbase config
BROWSERBASE_API_KEY = environ.get("BROWSERBASE_API_KEY")
BROWSERBASE_PROJECT_ID = environ.get("BROWSERBASE_PROJECT_ID")

# Celery Configuration
CELERY_SINGLETON_BACKEND_URL = environ.get(
    "CELERY_SINGLETON_BACKEND_URL", "redis://localhost:6379/1"
)
CELERY_BROKER_URL = "sqs://"
CELERY_BROKER_TRANSPORT_OPTIONS = {
    "region": "us-east-2",
    "worker_enable_remote_control": False,
    "queue_name_prefix": f"didero-dev-{DIDERO_DEV_NAME}-",  # for prod and staging, the queues are predefined. But for dev envs it's okay to make these queues on the fly as there's no DLQ setup for dev envs.
}
CELERY_TASK_DEFAULT_QUEUE = "default"
# this tells Celery to only acknowledge tasks once the worker has completed it. The default behavior is to ack before worker execution.
CELERY_TASK_ACKS_LATE = True
# this tells Celery to only acknowledge tasks that completed successfully.
CELERY_TASK_ACKS_ON_FAILURE_OR_TIMEOUT = False
# this tells Celery to reject a task (fail) if the worker gets killed. The default behavior is to ack the task.
CELERY_TASK_REJECT_ON_WORKER_LOST = True
CELERY_ALWAYS_EAGER = False
CELERY_TIMEZONE = "UTC"
CELERY_BEAT_SCHEDULER = "django_celery_beat.schedulers:DatabaseScheduler"

# Warning: what you see below, is not necessarily what actually gets run by celery beat
# See our ticket to fix https://linear.app/d4o/issue/EPD-764/sync-celery-beat-schedule
# and others with the issue https://github.com/celery/django-celery-beat/issues/248
# The only source of truth is what's actually in django, e.g. https://api.didero.ai/admin/django_celery_beat/periodictask/
CELERY_BEAT_SCHEDULE = {
    # once a day (0 8 * * 1)
    "po-approval-reminder-email": {
        "task": "didero.orders.tasks.send_po_approval_reminder_email",
        "schedule": crontab(minute=0, hour=8),
    },
    # tasks can be rescheduled for later. Every day (at 9am), check if that reschdule has passed
    # and the task should be reissued
    "reissue-scheduled-tasks": {
        "task": "didero.tasks.tasks.reissue_scheduled_tasks",
        "schedule": crontab(minute=0, hour=9),
    },
    # Every day (at 9am), create time-based tasks
    "create-time-based-tasks": {
        "task": "didero.tasks.tasks.create_time_based_tasks",
        "schedule": crontab(minute=0, hour=9),
    },
    # Every day (at 8am), send the tasks reminder email
    "send-tasks-reminder-email": {
        "task": "didero.tasks.tasks.send_tasks_reminder_email",
        "schedule": crontab(minute=0, hour=8),
    },
    # Start a minute after midnight to avoid any timing issues.
    "supplier-onboarding-document-expiry-task": {
        "task": "didero.suppliers.tasks.check_for_document_sor_expiry",
        "schedule": crontab(minute=1, hour=0),
    },
    # Everyday at 4am, backfill all teams' emails in case the consumer missed any
    "backfill-all-teams-emails": {
        "task": "didero.emails.tasks.nylas_tasks.backfill_all_teams_emails",
        "schedule": crontab(minute=0, hour=4),
    },
    # Every 6 hours, check for human feedback and update Langfuse scores
    "update-langfuse-scores": {
        "task": "didero.ai.feedback_tasks.update_langfuse_scores",
        "schedule": crontab(minute=0, hour="*/6"),  # Run every 6 hours
        "options": {"queue": "ai_workflows"},  # Use the AI workflows queue
    },
    # Update shipments every 15 minutes
    "update-pending-shipments": {
        "task": "didero.shipments.tasks.update_pending_shipments",
        "schedule": crontab(minute="*/15"),  # Run every 15 minutes
    },
    # Check for shipment updates every 30 minutes
    "check-shipment-sync-results": {
        "task": "didero.shipments.tasks.check_stagehand_sync_results",
        "schedule": crontab(minute="*/30"),  # Run every 30 minutes
    },
}

CELERY_RESULT_BACKEND = "django-db"
CELERY_CACHE_BACKEND = "default"
CELERY_RESULT_EXTENDED = True

GOOGLE_AUTH_CREDENTIAL_JSON = json.loads(environ.get("GOOGLE_AUTH_CRED", '{"web": {}}'))
GOOGLE_OAUTH_CLIENT_ID = GOOGLE_AUTH_CREDENTIAL_JSON["web"].get("client_id")
GOOGLE_OAUTH_CLIENT_SECRET = GOOGLE_AUTH_CREDENTIAL_JSON["web"].get("client_secret")

MS_OAUTH_CLIENT_ID = environ.get("MS_OAUTH_CLIENT_ID", "")
MS_OAUTH_CLIENT_CERT_JSON = json.loads(environ.get("MS_OAUTH_CLIENT_CERT_JSON", "{}"))
MS_OAUTH_SCOPES = [
    "User.Read",
    "https://graph.microsoft.com/Mail.ReadWrite",
]
MS_REDIRECT_URI = f"{APP_URL}/auth/oauth/ms_connected"

"""
Some libraries do this validation, but we can just keep as expansive a list
as we can find (chatgpt made this one) and monitor it.
"""
IGNORED_DOMAINS = set(
    [
        "gmail.com",
        "outlook.com",
        "yahoo.com",
        "hotmail.com",
        "aol.com",
        "mail.com",
        "live.com",
        "yandex.com",
        "protonmail.com",
        "gmx.com",
        "icloud.com",
        "zoho.com",
        "qq.com",
        "163.com",
        "msn.com",
        "inbox.com",
        "me.com",
        "mac.com",
        "fastmail.com",
        "hushmail.com",
        "att.net",
        "comcast.net",
        "verizon.net",
        "btinternet.com",
        "sbcglobal.net",
        "bellsouth.net",
        "cox.net",
        "charter.net",
        "frontier.com",
        "rogers.com",
        "shaw.ca",
        "sympatico.ca",
        "telus.net",
        "virgin.net",
        "rediffmail.com",
        "rambler.ru",
        "web.de",
        "mail.ru",
        "libero.it",
        "virginmedia.com",
        "blueyonder.co.uk",
        "ntlworld.com",
        INBOUND_MAIL_DOMAIN,
    ]
)


# Warning: changing encryption keys or salts will make existing values unreadable.
# https://github.com/georgemarshall/django-cryptography/issues/25#issuecomment-552084372
CRYPTOGRAPHY_SALT = "4bbee10dfd047f50104981858bb5bb4e"
ENCRYPTION_KEYS = {
    # keys will be derived from the strings in the env vars
    "imap_passwords": derive_key(
        environ.get("KEY_IMAP_PASSWORDS", SECRET_KEY),
        salt=b"0574f903ff14b0274821b8983ec8bef9",
    ),
    "smtp_passwords": derive_key(
        environ.get("KEY_SMTP_PASSWORDS", SECRET_KEY),
        salt=b"f7175d2d527bf5abce0cb449cd21fbdb",
    ),
    "google_access_tokens": derive_key(
        environ.get("KEY_GOOGLE_ACCESS_TOKENS", SECRET_KEY),
        salt=b"2724e74051068f397488ad45695a8ce4",
    ),
    "google_refresh_tokens": derive_key(
        environ.get("KEY_GOOGLE_REFRESH_TOKENS", SECRET_KEY),
        salt=b"d1d1f43ea99a56319ebb1a07db27c79d",
    ),
    "google_identity_json": derive_key(
        environ.get("KEY_GOOGLE_IDENTITY_JSON", SECRET_KEY),
        salt=b"gwr27P64hsZrE5h7CcTbFNqjImyrRywa",
    ),
    "ms_access_tokens": derive_key(
        environ.get("KEY_MS_ACCESS_TOKENS", SECRET_KEY),
        salt=b"ba5d30e167300a98d0218cff67bb41a8",
    ),
    "ms_refresh_tokens": derive_key(
        environ.get("KEY_MS_REFRESH_TOKENS", SECRET_KEY),
        salt=b"01f7d3218a8392cd8c197e9a4c8f19a0",
    ),
    "email_subjects": derive_key(
        environ.get("KEY_EMAIL_SUBJECT", SECRET_KEY),
        salt=b"4440f1382f06b44c5443597b36527d26",
    ),
    "email_contents": derive_key(
        environ.get("KEY_EMAIL_CONTENT", SECRET_KEY),
        salt=b"59791a3ec1c715299b7f04172ebad32b",
    ),
    "email_to": derive_key(
        environ.get("KEY_EMAIL_TO", SECRET_KEY),
        salt=b"Z6Mi8y3MXXdcQ28ZAUnyXMWbNtVzNmyz",
    ),
    "email_from": derive_key(
        environ.get("KEY_EMAIL_FROM", SECRET_KEY),
        salt=b"ugWZwO2VREqZxVOP76xK1t1BZppsCg",
    ),
    "user_pii": environ["KEY_USER_PII"].encode(),
}

OPENSEARCH_HOST = environ.get("OPENSEARCH_HOST")
OPENSEARCH_PASSWORD = environ.get("OPENSEARCH_PASSWORD")
OPENSEARCH_USE_IAM_AUTH = (
    environ.get("OPENSEARCH_USE_IAM_AUTH", "false").lower() == "true"
)

# OpenSearch connection configuration
OPENSEARCH_DSL = {
    "default": {
        "hosts": OPENSEARCH_HOST,
        "http_auth": None
        if OPENSEARCH_USE_IAM_AUTH
        else ("admin", OPENSEARCH_PASSWORD),
        # Connection pool configuration to handle production load
        "timeout": 30,  # Connection timeout in seconds
        "max_retries": 3,  # Maximum number of retries before failing
        "retry_on_timeout": True,  # Retry on timeout errors
        "http_compress": True,  # Enable HTTP compression
        "maxsize": 25,  # Maximum number of connections in the pool
        "pool_maxsize": 25,  # Maximum connections per node
    }
}

OPENSEARCH_DSL_AUTOSYNC = True if OPENSEARCH_HOST else False
OPENSEARCH_DSL_SIGNAL_PROCESSOR = (
    "django_opensearch_dsl.signals.RealTimeSignalProcessor"
)

# This is Yuming's key from openexchangerates.org
# If you get rate-limited you can sign up and get your own key
OPEN_EXCHANGE_RATES_APP_ID = "e4decc4e2e754077b33677f801556a3b"

TEMPORAL_SERVER_URL = environ.get("TEMPORAL_SERVER_URL", "")
TEMPORAL_NAMESPACE = environ.get("TEMPORAL_NAMESPACE", "default")
TEMPORAL_API_KEY = environ.get("TEMPORAL_API_KEY", "")

FEDEX_API_BASE_URL = environ.get("FEDEX_API_BASE_URL", "https://apis-sandbox.fedex.com")
FEDEX_API_KEY = environ.get("FEDEX_API_KEY", "")
FEDEX_API_SECRET = environ.get("FEDEX_API_SECRET", "")

# ZIM carrier configuration
ZIM_HEADLESS_MODE = environ.get("ZIM_HEADLESS_MODE", "true").lower() == "true"

# Stagehand settings
STAGEHAND_API_URL = environ.get(
    "STAGEHAND_API_URL",
    "https://rpa.didero.ai"
    if DIDERO_ENVIRONMENT in ["prod", "staging"]
    else "http://localhost:8001",
)
STAGEHAND_API_KEY = environ.get("STAGEHAND_API_KEY")
