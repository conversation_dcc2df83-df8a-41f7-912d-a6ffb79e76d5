from collections import defaultdict

from opensearch_dsl import Search
from opensearchpy import OpenSearch
from rest_framework import viewsets
from rest_framework.response import Response

from didero.settings.common import OPENSEARCH_DSL
from didero.views import APIView

settings = OPENSEARCH_DSL["default"]
# Pass all settings to OpenSearch client, including connection_class for IAM auth
client = OpenSearch(**settings) if settings.get("hosts") else None

# TODO: should be in config
doc_return_fields = {
    "suppliers": ["id", "name", "website_url", "archived_at"],
    "supplier_contacts": ["id", "name", "email", "phone", "supplier", "archived_at"],
    "documents": ["uuid", "name", "supplier", "archived_at"],
    "communications": [
        "id",
        "email_thread_id",
        "email_subject",
        "supplier",
        "archived_at",
    ],
    "purchase_orders": ["id", "po_number", "supplier", "archived_at"],
    "items": [
        "uuid",
        "description",
        "item_sku",
        "item_number",
        "supplier",
        "archived_at",
    ],
}

INDEX_NAME_TO_SCORE = {
    "suppliers": 5,
    "supplier_contacts": 4,
    "documents": 3,
    "communications": 2,
    "purchase_orders": 1,
    "items": 0,
}


class SearchViewSet(APIView, viewsets.ViewSet):
    def search_all(self, request):
        if not client:
            return Response(
                status=500, data={"error": "Search client is not configured"}
            )
        return self.get_search_response_v2(request)

    def search_docs(self, request):
        if not client:
            return Response(status=500, data={"error": "Search client not configured"})
        return self.get_search_response(request, "documents", 25)

    # legacy search method; TODO: remove
    # helper method that actually does the search; you just need
    # to give it the request, index and limit (number of results)
    def get_search_response(self, request, index, limit):
        # TODO: manage pagination
        # TODO: log/action skipped or error rows
        search_term = request.query_params.get("term", "")
        response = (
            Search(using=client, index=index)
            .query("multi_match", query=search_term, type="phrase_prefix", fields=["*"])
            .filter("term", team_uuid=request.team.uuid)
            .exclude("exists", field="archived_at")
            .exclude("exists", field="supplier.archived_at")
            .sort(
                {
                    "_script": {
                        "type": "number",
                        "script": {
                            "lang": "painless",
                            "source": "params.scores[doc['_index'].value]",
                            "params": {"scores": INDEX_NAME_TO_SCORE},
                        },
                        "order": "desc",
                    }
                }
            )
        )[:limit]

        idx_map = defaultdict(list)
        for hit in response:
            idx = hit.meta.index
            obj = {
                k: v for k, v in hit.to_dict().items() if k in doc_return_fields[idx]
            }
            idx_map[idx].append(obj)

        res = {"results": []}

        for idx, objs in idx_map.items():
            res["results"].append({"index": idx, "documents": objs})

        return Response(res)

    # do a query per index (supplier, comms, pos, items, contacts, docs) to get at most 5 results per model type
    # returns at most 30 results
    def get_search_response_v2(self, request):
        search_term = request.query_params.get("term", "")

        aggregated_results = []
        for index in INDEX_NAME_TO_SCORE.keys():
            response = (
                Search(using=client, index=index)
                .query(
                    "multi_match", query=search_term, type="phrase_prefix", fields=["*"]
                )
                .filter("term", team_uuid=request.team.uuid)
                .exclude("exists", field="archived_at")
                .exclude("exists", field="supplier.archived_at")
            )[:5]
            aggregated_results.extend(response)

        idx_map = defaultdict(list)
        for hit in aggregated_results:
            idx = hit.meta.index
            obj = {
                k: v for k, v in hit.to_dict().items() if k in doc_return_fields[idx]
            }
            idx_map[idx].append(obj)

        res = {"results": []}

        for idx, objs in idx_map.items():
            res["results"].append({"index": idx, "documents": objs})

        return Response(res)
