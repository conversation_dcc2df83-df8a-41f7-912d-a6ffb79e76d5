import structlog
from django.core.management.base import BaseCommand
from django_opensearch_dsl.registries import registry
from opensearchpy import OpenSearch

from didero.settings.common import OPENSEARCH_DSL

logger = structlog.get_logger(__name__)


class Command(BaseCommand):
    help = "Delete, create and then populate OpenSearch indices"

    def handle(self, *args, **options):
        settings = OPENSEARCH_DSL["default"]

        if not settings.get("hosts"):
            self.stdout.write(
                self.style.ERROR(
                    "OpenSearch is not configured. Skipping index recreation."
                )
            )
            return

        # Pass all settings to OpenSearch client, including connection_class for IAM auth
        client = OpenSearch(**settings)
        # These are the top-level indices, e.g. Supplier, Communication
        indices = registry.get_indices()

        for index in indices:
            index_name = index._name
            # if the index exists, delete it
            if client.indices.exists(index_name):
                index.delete()
                self.stdout.write(self.style.NOTICE(f"Deleted index: {index_name}."))
            # create the index
            index.create()
            self.stdout.write(self.style.NOTICE(f"Created index: {index_name}"))
            document_classes = registry._indices[index]
            # These are the models within the index - for us, that's one per index.
            # E.g. within the didero_suppliers index, there's the SupplierDocument class.
            for doc_class in document_classes:
                self.stdout.write(
                    f"Processing {doc_class.__name__} for index {index._name}."
                )
                model_class = doc_class.django.model
                instances = model_class.objects.all()

                # These are the individual instances, i.e. an actual Supplier 123.
                actions = (
                    doc_class()._prepare_action(instance, "index")
                    for instance in instances
                )
                success, _ = doc_class().bulk(actions, refresh=True)

                self.stdout.write(
                    self.style.NOTICE(
                        f"{success} instances indexed successfully in {index_name}."
                    )
                )
            self.stdout.write(
                self.style.SUCCESS(f"Successfully populated the {index_name} index!")
            )
