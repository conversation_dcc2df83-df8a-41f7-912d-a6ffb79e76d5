"""
Generic document processing tools for AI agents.
These tools can be used across different AI workflows for document analysis.
"""

from typing import Any, Dict, Optional

import structlog
from pydantic_ai import RunContext

from didero.ai.document_matching.schemas import (
    DocumentMatchingDependencies,
    SupportedDocument,
)
from didero.ai.matching.functions import ai_match_addresses, ai_match_items
from didero.ai.matching.types import AddressData, ItemData
from didero.ai.tools.types import AddressMatchParams, ItemMatchParams

logger = structlog.get_logger(__name__)


def _extract_po_number(document: SupportedDocument) -> Optional[str]:
    """Safely extract po_number from document (dict or Pydantic model)"""
    if isinstance(document, dict):
        return document.get("po_number")
    return getattr(document, "po_number", None)


def _get_supplier_id_from_po_number(po_number: str) -> Optional[int]:
    """Get supplier_id by looking up PurchaseOrder by po_number"""
    if not po_number:
        return None

    try:
        from didero.orders.models import PurchaseOrder

        po = PurchaseOrder.objects.select_related("supplier").get(po_number=po_number)
        return po.supplier.id if po.supplier else None
    except Exception:
        # Handle both DoesNotExist and other exceptions
        return None


def _get_nested_value(obj: Any, path: str) -> Any:
    """Extract value from nested object/dict using dot notation path."""
    try:
        parts = path.split(".")
        value = obj
        for part in parts:
            if isinstance(value, dict):
                value = value.get(part)
            elif isinstance(value, list):
                # Handle array indices
                try:
                    index = int(part)
                    value = value[index] if index < len(value) else None
                except (ValueError, IndexError):
                    value = None
            else:
                # Try attribute access
                value = getattr(value, part, None)
            if value is None:
                return None
        return value
    except Exception:
        return None


async def extract_value(
    ctx: RunContext[DocumentMatchingDependencies],
    document: str,
    path: str,
) -> str:
    """
    Extract a specific value from a document using dot notation path.

    This is a simple, reliable tool for getting values from complex document structures.
    Use this tool whenever you need to retrieve a field value from either document.
    The path supports nested object access and array indexing.

    Supported Document Types:
        - PurchaseOrderDetails (PO documents)
        - Invoice documents
        - OrderAcknowledgement documents
        - Generic dictionaries (for other document types like shipments)

    The tool handles both Pydantic models and plain dictionaries seamlessly.

    Args:
        document: Which document to extract from - must be exactly "doc1" or "doc2"
        path: Dot notation path to the value. Supports:
              - Simple fields: "po_number", "total_amount", "invoice_number"
              - Nested objects: "shipping_address.city", "billing_address.zip"
              - Array access: "items.0.quantity", "line_items.2.unit_price"
              - Deep nesting: "items.0.product.specifications.weight"

    Returns:
        str: The extracted value as a string, or empty string if not found

    Limitations:
        - Only works with the document types listed above
        - Array notation only supports numeric indices (not queries)
        - Cannot extract complex objects - only primitive values (strings, numbers, booleans)
        - Returns empty string for null/None/missing values
        - All values are converted to strings (including numbers)

    Examples:
        # Extract header fields
        po_num = extract_value("doc1", "po_number")  # Returns: "PO-2024-001"
        total = extract_value("doc2", "total_amount")  # Returns: "1250.00"

        # Extract from nested objects
        city = extract_value("doc1", "shipping_address.city")  # Returns: "New York"

        # Extract from arrays (items, order_items, line_items are common array fields)
        first_item_qty = extract_value("doc1", "items.0.quantity")  # Returns: "10"
        second_item_price = extract_value("doc2", "order_items.1.unit_price")  # Returns: "25.50"

        # Handle missing fields gracefully
        missing = extract_value("doc1", "non_existent_field")  # Returns: ""

        # Works with different document structures
        # Invoice: extract_value("doc1", "invoice_number")
        # PO: extract_value("doc2", "supplier_name")
        # Shipment (dict): extract_value("doc1", "tracking_number")
    """
    if document == "doc1":
        source_doc = ctx.deps.document1
    elif document == "doc2":
        source_doc = ctx.deps.document2
    else:
        return ""

    value = _get_nested_value(source_doc, path)
    return str(value) if value is not None else ""


async def compare_values(
    ctx: RunContext[DocumentMatchingDependencies],
    value1: str,
    value2: str,
    comparison_type: str = "exact",
) -> Dict[str, Any]:
    """
    Compare two values intelligently and return structured comparison result.

    This tool is designed to work with string outputs from extract_value and handles
    different types of comparisons with automatic tolerance settings for numeric values.
    Use this after extracting values to determine if they match according to business rules.

    Args:
        value1: First value to compare (typically from extract_value on document1)
        value2: Second value to compare (typically from extract_value on document2)
        comparison_type: Type of comparison to perform
            - "exact": Values must match exactly (default)
            - "numeric": Compare as numbers with tolerance settings
            - "case_insensitive": Ignore case differences

    Returns:
        Dict with comparison result:
        {
            "match": bool,              # True if values match according to comparison type
            "status": str,              # "match", "mismatch", "missing", or "extra"
            "reason": str               # Human-readable explanation
        }

    Limitations:
        - Expects string inputs (as returned by extract_value)
        - Numeric comparison only works with parseable numbers (handles $, commas)
        - Cannot compare complex objects or lists directly
        - Date comparison not supported (use "exact" for dates)
        - Boolean values are compared as strings
        - No semantic understanding (e.g., "USA" vs "United States")

    Fallback Strategies:
        - If numeric parsing fails, falls back to string comparison
        - Empty strings are handled as missing values
        - For complex comparisons (addresses, items), use verify_address_match or verify_item_match
        - For dates, extract as strings and use exact comparison
        - For lists/arrays, extract individual elements and compare separately

    Examples:
        # Compare PO numbers (exact match required)
        result = compare_values("PO-2024-001", "PO-2024-001", "exact")
        # Returns: {"match": True, "status": "match", "reason": "Values match exactly"}

        # Compare amounts with tolerance (handles currency symbols)
        result = compare_values("$1,250.00", "1255.00", "numeric")
        # Returns: {"match": True, "status": "match", "reason": "Values match within 5% tolerance"}

        # Compare descriptions (case insensitive)
        result = compare_values("Steel Bolt", "STEEL BOLT", "case_insensitive")
        # Returns: {"match": True, "status": "match", "reason": "Values match (case insensitive)"}

        # Handle missing values
        result = compare_values("INV-123", "", "exact")
        # Returns: {"match": False, "status": "extra", "reason": "Value missing in document2"}

        # Numeric comparison with parsing failure
        result = compare_values("N/A", "1000", "numeric")
        # Returns: {"match": False, "status": "mismatch", "reason": "String comparison (numeric parsing failed): 'N/A' vs '1000'"}
    """
    # Normalize values
    v1 = str(value1).strip() if value1 else ""
    v2 = str(value2).strip() if value2 else ""

    # Handle empty values
    if not v1 and not v2:
        return {"match": True, "status": "match", "reason": "Both values empty"}
    elif not v1:
        return {
            "match": False,
            "status": "missing",
            "reason": "Value missing in document1",
        }
    elif not v2:
        return {
            "match": False,
            "status": "extra",
            "reason": "Value missing in document2",
        }

    # Compare based on type
    if comparison_type == "case_insensitive":
        match = v1.lower() == v2.lower()
        reason = f"Values {'match' if match else 'differ'} (case insensitive): '{v1}' vs '{v2}'"
    elif comparison_type == "numeric":
        try:
            # Handle currency and number formatting
            n1 = float(v1.replace(",", "").replace("$", ""))
            n2 = float(v2.replace(",", "").replace("$", ""))

            # Use configured tolerance if available
            tolerance_config = ctx.deps.tolerance_config
            if tolerance_config:
                tolerance_type = tolerance_config.get("type", "percentage")
                tolerance_value = tolerance_config.get("value", 5.0)

                if tolerance_type == "percentage":
                    # Percentage tolerance
                    if n2 == 0:
                        match = n1 == 0
                        tolerance_used = "exact (reference is zero)"
                    else:
                        percentage_diff = abs(n1 - n2) / abs(n2) * 100
                        match = percentage_diff <= tolerance_value
                        tolerance_used = f"{tolerance_value}% tolerance"
                elif tolerance_type == "fixed_amount":
                    # Fixed amount tolerance
                    absolute_diff = abs(n1 - n2)
                    match = absolute_diff <= tolerance_value
                    tolerance_used = f"${tolerance_value} tolerance"
                else:
                    # Fallback to penny tolerance
                    match = abs(n1 - n2) < 0.01
                    tolerance_used = "penny tolerance (fallback)"
            else:
                # Default penny tolerance if no config
                match = abs(n1 - n2) < 0.01
                tolerance_used = "penny tolerance (default)"

            reason = f"Numeric comparison ({tolerance_used}): {n1} vs {n2} - {'within tolerance' if match else 'exceeds tolerance'}"

        except ValueError:
            # Fallback to string comparison if not numeric
            match = v1 == v2
            reason = f"String comparison (numeric parsing failed): '{v1}' vs '{v2}'"
    else:  # exact
        match = v1 == v2
        reason = f"Exact comparison: '{v1}' vs '{v2}'"

    status = "match" if match else "mismatch"

    return {"match": match, "status": status, "reason": reason}


def verify_item_match(
    ctx: RunContext[DocumentMatchingDependencies],
    params: ItemMatchParams,
) -> Dict[str, Any]:
    """
    Verify if two line items represent the same product using intelligent AI matching.

    This tool uses advanced matching logic to determine if two items are the same,
    even with variations in item numbers, descriptions, or formatting. Use this
    when comparing line items between documents to handle real-world variations.

    Args:
        params: Item matching parameters with all necessary data

    Returns:
        Dict with match result:
        {
            "match": bool,               # True if items represent the same product
            "matching_score": float,         # 0.0-1.0 matching score
            "reason": str               # Explanation of the match decision
        }

    Examples:
        # Items with different formatting but same product
        params = ItemMatchParams(
            item1_number="ABC-123", item1_description="Steel Bolt 1/4 inch",
            item2_number="ABC123", item2_description="Steel Bolt 1/4\\""
        )
        result = verify_item_match(params)
        # Returns: {"match": True, "matching_score": 0.9, "reason": "AI identified as same product"}
    """
    try:
        # Import for cache storage (used after AI matching)
        from didero.documents.models import DocumentItemMatch

        # Build item data from parameters for AI matching
        item1_data = ItemData(
            item_number=params.item1_number,
            item_description=params.item1_description,
            quantity=params.quantity1,
            unit_price=params.unit_price1,
        )

        item2_data = ItemData(
            item_number=params.item2_number,
            item_description=params.item2_description,
            quantity=params.quantity2,
            unit_price=params.unit_price2,
        )

        po_number = _extract_po_number(ctx.deps.document2) or _extract_po_number(
            ctx.deps.document1
        )

        logger.info(
            "No cached result found, calling AI matching",
            item1_number=params.item1_number,
            item2_number=params.item2_number,
            team_id=ctx.deps.team_id,
            doc1_type=ctx.deps.document1_type.value,
            doc2_type=ctx.deps.document2_type.value,
        )

        ai_result = ai_match_items(
            item1=item1_data,
            item2=item2_data,
            team_id=ctx.deps.team_id,
            po_number=po_number,
        )

        # Handle None result gracefully
        if ai_result is None:
            return {
                "match": False,
                "matching_score": 0.0,
                "reason": "AI matching failed - no result returned",
            }

        # Store the match result for future reuse
        try:
            from didero.documents.models import DocumentItemMatch

            supplier_id = (
                _get_supplier_id_from_po_number(po_number) if po_number else None
            )

            DocumentItemMatch.store_match_result(
                doc_type1=ctx.deps.document1_type.value,
                item_number1=params.item1_number,
                item_description1=params.item1_description,
                doc_type2=ctx.deps.document2_type.value,
                item_number2=params.item2_number,
                item_description2=params.item2_description,
                team_id=ctx.deps.team_id,
                is_match=bool(ai_result),
                supplier_id=supplier_id,
            )
        except Exception as storage_error:
            # Don't fail the main function if storage fails
            logger.warning(
                "Failed to store item match result",
                error=str(storage_error),
                item1_number=params.item1_number,
                item2_number=params.item2_number,
                team_id=ctx.deps.team_id,
            )

        return {
            "match": bool(ai_result),
            "matching_score": 0.9 if ai_result else 0.1,
            "reason": "AI-based item matching",
        }

    except Exception as e:
        # Fallback to description comparison
        desc1 = str(params.item1_description).lower().strip()
        desc2 = str(params.item2_description).lower().strip()
        match = desc1 == desc2 and desc1 != ""

        return {
            "match": match,
            "matching_score": 0.7 if match else 0.3,
            "reason": f"Fallback description comparison: {str(e)}",
        }


def verify_address_match(
    ctx: RunContext[DocumentMatchingDependencies],
    params: AddressMatchParams,
) -> Dict[str, Any]:
    """
    Verify if two addresses represent the same physical location using intelligent AI matching.

    This tool handles variations in address formatting, abbreviations, and minor
    differences that often occur between documents. Use this for comparing billing
    or shipping addresses to determine if they refer to the same location.

    Args:
        params: Address matching parameters with all necessary data

    Returns:
        Dict with match result:
        {
            "match": bool,               # True if addresses are the same location
            "matching_score": float,         # 0.0-1.0 matching score
            "reason": str               # Explanation of the match decision
        }

    Examples:
        # Same address with formatting differences
        params = AddressMatchParams(
            addr1_line1="123 Main St", addr1_city="New York", addr1_state="NY",
            addr2_line1="123 Main Street", addr2_city="New York", addr2_state="New York"
        )
        result = verify_address_match(params)
        # Returns: {"match": True, "matching_score": 0.95, "reason": "Same location with minor variations"}
    """
    try:
        # Build address data from parameters
        address1_data = AddressData(
            line1=params.addr1_line_1,
            line2=params.addr1_line_2,
            city=params.addr1_city,
            state=params.addr1_state_or_province,
            zip=params.addr1_postal_code,
            country=params.addr1_country,
        )

        address2_data = AddressData(
            line1=params.addr2_line_1,
            line2=params.addr2_line_2,
            city=params.addr2_city,
            state=params.addr2_state_or_province,
            zip=params.addr2_postal_code,
            country=params.addr2_country,
        )

        po_number = _extract_po_number(ctx.deps.document2) or _extract_po_number(
            ctx.deps.document1
        )

        ai_result = ai_match_addresses(
            address1=address1_data,
            address2=address2_data,
            team_id=ctx.deps.team_id,
            po_number=po_number,
        )

        # Handle None result gracefully
        if ai_result is None:
            return {
                "match": False,
                "matching_score": 0.0,
                "reason": "AI address matching failed - no result returned",
            }

        return {
            "match": bool(ai_result),
            "matching_score": 0.9 if ai_result else 0.1,
            "reason": "AI-based address matching",
        }

    except Exception as e:
        # Fallback to simple comparison
        addr1_str = f"{params.addr1_line_1} {params.addr1_city}".strip()
        addr2_str = f"{params.addr2_line_1} {params.addr2_city}".strip()
        match = addr1_str.lower() == addr2_str.lower() and addr1_str != ""

        return {
            "match": match,
            "matching_score": 0.6 if match else 0.4,
            "reason": f"Fallback string comparison: {str(e)}",
        }
