"""
Database update tools for the Reasoning Agent.

These tools suggest database updates that require human approval.
"""

from typing import Any, Dict, Optional

import structlog
from django.apps import apps
from django.contrib.contenttypes.models import ContentType
from pydantic_ai import RunContext

from didero.tasks.schemas import TaskActionType as TaskActionTypeName
from didero.tasks.schemas import TaskType as TaskTypeName
from didero.tasks.utils import CreateTaskActionParams, create_task_v2
from didero.users.models import Team
from didero.utils.utils import get_didero_ai_user

from ...schemas.dependencies import ReasoningDependencies
from ..constants import DATABASE_TOOLS_CONFIG
from ..utils.field_converters import FieldConversionError, FieldConverter

logger = structlog.get_logger(__name__)


# TODO: Implement find_updatable_records tool
# This higher-level tool should:
# 1. Analyze built_context for all entities with record_ids
# 2. For each entity, determine if it matches the target model type
# 3. Call suggest_database_update with explicit record_identifier for each match
# 4. Return list of suggestion results
#
# Example usage:
# suggestions = await find_updatable_records(
#     ctx=ctx,
#     model="orders.PurchaseOrder",
#     field="vendor_notes",
#     field_type="TextField",
#     suggested_value="Updated notes",
#     reason="Email contains updated supplier information"
# )
#
# This ensures suggest_database_update always receives explicit record_identifier
# and eliminates ambiguity when multiple entities exist in context.


async def suggest_database_update(
    ctx: RunContext[ReasoningDependencies],
    model: str,
    field: str,
    field_type: str,
    current_value: Any,
    suggested_value: Any,
    reason: str,
    record_identifier: Optional[str] = None,
) -> Dict[str, Any]:
    """
    Suggest a database update that requires human approval.

    Creates a human-reviewable task using the existing Task system with
    EXECUTE_DATABASE_UPDATE and REJECT_DATABASE_UPDATE actions.

    Args:
        ctx: The run context containing dependencies and built context
        model: Database model path (e.g., "orders.PurchaseOrder")
        field: Field name to update (e.g., "vendor_notes")
        field_type: Django field type (for validation)
        current_value: Current value in database
        suggested_value: AI suggested new value
        reason: Explanation for why this update is needed
        record_identifier: Optional human-readable record identifier

    Returns:
        Dict containing:
        - suggestion_id: ID of the created task for tracking
        - requires_approval: Always True for database updates
        - confidence: Confidence score for this suggestion (0.0-100.0)
        - field_metadata: Information about the field being updated
        - task_created: Whether the approval task was successfully created
        - error: Error message if task creation failed
    """
    try:
        team_id = ctx.deps.team_id
        built_context = ctx.deps.built_context or {}

        logger.info(
            "Database update suggestion requested",
            model=model,
            field=field,
            reason=reason,
            team_id=team_id,
        )

        # Step 1: Parse and validate model information
        try:
            app_label, model_name = model.split(".", 1)
            model_class = apps.get_model(app_label, model_name)
        except (ValueError, LookupError) as e:
            logger.error(
                "Invalid model path",
                model=model,
                error=str(e),
                team_id=team_id,
            )
            return {
                "suggestion_id": None,
                "requires_approval": True,
                "confidence": 0.0,
                "field_metadata": {},
                "task_created": False,
                "error": f"Invalid model path '{model}': {str(e)}",
            }

        # Step 2: Validate field and convert suggested value using existing FieldConverter
        try:
            converted_value, display_string, field_info = (
                FieldConverter.validate_and_convert(model_class, field, suggested_value)
            )

            # Verify field type matches what was expected (V1: strict safety)
            if field_info["field_type"] != field_type:
                logger.error(
                    "Field type mismatch - rejecting for V1 safety",
                    expected_type=field_type,
                    actual_type=field_info["field_type"],
                    field=field,
                    model=model,
                    team_id=team_id,
                )
                return {
                    "suggestion_id": None,
                    "requires_approval": True,
                    "confidence": 0.0,
                    "field_metadata": {
                        "error": f"Field type mismatch: expected {field_type}, got {field_info['field_type']}"
                    },
                    "task_created": False,
                    "error": f"Field type mismatch: expected {field_type}, got {field_info['field_type']}",
                }
        except FieldConversionError as e:
            logger.error(
                "Field conversion failed",
                model=model,
                field=field,
                suggested_value=suggested_value,
                error=str(e),
                team_id=team_id,
            )
            return {
                "suggestion_id": None,
                "requires_approval": True,
                "confidence": 0.0,
                "field_metadata": {"error": str(e)},
                "task_created": False,
                "error": f"Cannot convert suggested value: {str(e)}",
            }

        # Step 3: Calculate confidence score based on context and field information
        confidence = _calculate_confidence_score(
            reason=reason,
            field_info=field_info,
            converted_value=converted_value,
            built_context=built_context,
            current_value=current_value,
        )

        # Step 4: Extract record information - require explicit identifier for V1 safety
        record_id, record_display = _extract_record_info(
            built_context, record_identifier, model_name
        )

        if not record_id:
            logger.error(
                "Could not determine record ID",
                model=model,
                record_identifier=record_identifier,
                built_context_keys=list(built_context.keys()),
                team_id=team_id,
            )
            return {
                "suggestion_id": None,
                "requires_approval": True,
                "confidence": confidence,
                "field_metadata": field_info,
                "task_created": False,
                "error": "Could not determine which record to update from context",
            }

        # Step 5: Format current value for display
        current_display = _format_value_for_display(
            current_value, field_info["field_type"]
        )

        # Step 6: Create the approval task
        task_result = await _create_database_update_approval_task(
            team_id=team_id,
            model=model,
            record_id=record_id,
            record_display=record_display,
            field_name=field,
            field_type=field_info["field_type"],
            current_value=current_value,
            current_display=current_display,
            suggested_value=converted_value,
            suggested_display=display_string,
            ai_reasoning=reason,
            confidence=confidence,
            source_context=built_context,
            model_class=model_class,
        )

        if not task_result["success"]:
            logger.error(
                "Failed to create database update approval task",
                error=task_result.get("error"),
                model=model,
                field=field,
                team_id=team_id,
            )
            return {
                "suggestion_id": None,
                "requires_approval": True,
                "confidence": confidence,
                "field_metadata": field_info,
                "task_created": False,
                "error": task_result.get("error", "Unknown error creating task"),
            }

        logger.info(
            "Database update suggestion created successfully",
            model=model,
            field=field,
            record_id=record_id,
            record_display=record_display,
            task_id=task_result["task_id"],
            confidence=confidence,
            team_id=team_id,
        )

        return {
            "suggestion_id": str(task_result["task_id"]),
            "requires_approval": True,
            "confidence": confidence,
            "field_metadata": field_info,
            "task_created": True,
            "error": None,
        }

    except Exception as e:
        logger.error(
            "Database update suggestion failed with unexpected error",
            model=model,
            field=field,
            reason=reason,
            error=str(e),
            exc_info=True,
            team_id=ctx.deps.team_id,
        )
        return {
            "suggestion_id": None,
            "requires_approval": True,
            "confidence": 0.0,
            "field_metadata": {},
            "task_created": False,
            "error": f"Internal error: {str(e)}",
        }


def _calculate_confidence_score(
    reason: str,
    field_info: Dict[str, Any],
    converted_value: Any,
    built_context: Dict[str, Any],
    current_value: Any,
) -> float:
    """
    Calculate confidence score for database update suggestion.

    Based on patterns from email categorization system. Score is 0-100.
    More conservative scoring to ensure human review for uncertain cases.
    """
    config = DATABASE_TOOLS_CONFIG
    confidence = config["CONFIDENCE_BASE_SCORE"]

    # Reasoning quality assessment (more stringent)
    reasoning_words = len(reason.strip().split())
    if reasoning_words >= config["DETAILED_REASONING_WORDS"]:
        confidence += config["DETAILED_REASONING_BONUS"]
    elif reasoning_words >= config["MODERATE_REASONING_WORDS"]:
        confidence += config["MODERATE_REASONING_BONUS"]
    elif reasoning_words >= config["BASIC_REASONING_WORDS"]:
        confidence += config["BASIC_REASONING_BONUS"]
    else:
        confidence += config["BRIEF_REASONING_BONUS"]

    # Context-based reasoning patterns (more selective)
    context_keywords = config["CONTEXT_KEYWORDS"]
    matching_keywords = sum(
        1 for keyword in context_keywords if keyword in reason.lower()
    )
    if matching_keywords >= 2:
        confidence += config["STRONG_CONTEXT_BONUS"]
    elif matching_keywords == 1:
        confidence += config["WEAK_CONTEXT_BONUS"]

    # Field type support assessment (reduced bonus for supported types)
    if field_info.get("field_type") in config["SUPPORTED_FIELD_TYPES"]:
        confidence += config["SUPPORTED_FIELD_BONUS"]
    else:
        confidence += config["UNSUPPORTED_FIELD_PENALTY"]

    # Field metadata assessment (more selective)
    if field_info.get("choices") and any(
        str(converted_value) == str(choice[0]) for choice in field_info["choices"]
    ):
        confidence += config["FIELD_CHOICES_MATCH_BONUS"]

    if field_info.get("max_length") and isinstance(converted_value, str):
        length_ratio = len(str(converted_value)) / field_info["max_length"]
        if length_ratio <= config["LENGTH_SAFE_RATIO"]:
            confidence += config["LENGTH_SAFE_BONUS"]
        elif length_ratio > config["LENGTH_RISKY_RATIO"]:
            confidence += config["LENGTH_RISKY_PENALTY"]

    # Context quality assessment (more demanding)
    context_score = 0
    if built_context.get("entities") and len(built_context["entities"]) > 0:
        # Check if entities actually exist and have data
        existing_entities = [
            e for e in built_context["entities"].values() if e.get("exists")
        ]
        if existing_entities:
            context_score += config["EXISTING_ENTITIES_BONUS"]

    if built_context.get("references") and len(built_context["references"]) > 0:
        context_score += config["REFERENCES_BONUS"]

    confidence += context_score

    # Value comparison assessment (stricter penalties)
    if current_value is not None and converted_value is None:
        confidence += config["REMOVING_DATA_PENALTY"]
    elif current_value is None and converted_value is not None:
        confidence += config["ADDING_DATA_BONUS"]
    elif current_value == converted_value:
        confidence += config["NO_CHANGE_PENALTY"]

    # Apply more conservative thresholds
    if confidence >= config["CONFIDENCE_THRESHOLD"]:
        confidence = min(config["CONFIDENCE_HIGH_CAP"], confidence)
    else:
        confidence = max(config["CONFIDENCE_LOW_FLOOR"], confidence)

    return round(confidence, 1)


def _extract_record_info(
    built_context: Dict[str, Any],
    record_identifier: Optional[str],
    model_name: str,
) -> tuple[Optional[int], str]:
    """
    Extract record ID and display name from built context using explicit identifier.

    V1 Safety: Always requires explicit record_identifier - no fallback to first entity.
    This prevents ambiguous updates when multiple entities exist in context.
    """
    record_id = None
    record_display = record_identifier or f"{model_name} Record"

    # V1: Only use explicit record_identifier - no fallback to prevent ambiguity
    if not record_identifier:
        entities = built_context.get("entities", {})
        entity_count = len(
            [e for e in entities.values() if e.get("exists") and e.get("record_id")]
        )

        if entity_count > 1:
            logger.error(
                "Multiple entities available but no explicit record_identifier provided",
                model_name=model_name,
                entity_count=entity_count,
                available_entities=list(entities.keys()),
                recommendation="Use find_updatable_records tool or provide explicit record_identifier",
            )
        elif entity_count == 0:
            logger.error(
                "No entities available in context",
                model_name=model_name,
                built_context_keys=list(built_context.keys()),
            )
        else:
            logger.warning(
                "Single entity available but no record_identifier provided",
                model_name=model_name,
                available_entity=list(entities.keys())[0] if entities else None,
                recommendation="Provide explicit record_identifier for clarity",
            )

        return None, record_display

    # Find explicit record_identifier in entities
    entities = built_context.get("entities", {})
    for entity_key, entity_data in entities.items():
        if (
            entity_key == record_identifier
            and entity_data.get("exists")
            and entity_data.get("record_id")
        ):
            record_id = entity_data["record_id"]
            record_display = entity_key
            logger.debug(
                "Found explicit record in context",
                record_identifier=record_identifier,
                record_id=record_id,
            )
            break

    if not record_id:
        logger.error(
            "Explicit record_identifier not found in context",
            record_identifier=record_identifier,
            available_entities=list(entities.keys()) if entities else [],
        )

    return record_id, record_display


def _format_value_for_display(value: Any, field_type: str) -> str:
    """Format current value for human-readable display."""
    if value is None:
        return "None"

    if field_type == "MoneyField" and hasattr(value, "amount"):
        return f"${value.amount:,.2f} {value.currency}"
    elif field_type == "DateField" and hasattr(value, "strftime"):
        return value.strftime("%Y-%m-%d")
    elif field_type == "BooleanField":
        return "Yes" if value else "No"
    else:
        return str(value)


async def _create_database_update_approval_task(
    team_id: int,
    model: str,
    record_id: int,
    record_display: str,
    field_name: str,
    field_type: str,
    current_value: Any,
    current_display: str,
    suggested_value: Any,
    suggested_display: str,
    ai_reasoning: str,
    confidence: float,
    source_context: Dict[str, Any],
    model_class: type,
) -> Dict[str, Any]:
    """
    Create the human approval task for the database update.

    Returns dict with success status and task_id if successful, or error message.
    """
    try:
        # Get team and AI user
        team = Team.objects.get(id=team_id)
        ai_user = get_didero_ai_user(team)

        if not ai_user:
            return {
                "success": False,
                "error": f"No AI user found for team {team_id}",
            }

        # Get ContentType for the model
        content_type = ContentType.objects.get_for_model(model_class)

        # Prepare task parameters following DatabaseUpdateApprovalParams structure
        task_type_params = {
            "model": model,
            "record_id": record_id,
            "record_display": record_display,
            "field_name": field_name,
            "field_type": field_type,
            "current_value": current_value,
            "current_display": current_display,
            "suggested_value": suggested_value,
            "suggested_display": suggested_display,
            "ai_reasoning": ai_reasoning,
            "confidence": confidence,
            "source_context": source_context,
        }

        # Define actions for the task
        actions = [
            CreateTaskActionParams(
                action_type=TaskActionTypeName.EXECUTE_DATABASE_UPDATE,
                action_params={},
                action_execution_params={
                    "model": model,
                    "record_id": record_id,
                    "field_name": field_name,
                    "field_type": field_type,
                    "suggested_value": str(suggested_value),
                },
            ),
            CreateTaskActionParams(
                action_type=TaskActionTypeName.REJECT_DATABASE_UPDATE,
                action_params={},
                action_execution_params={
                    "rejection_reason": "",  # Will be filled by user
                },
            ),
        ]

        # Create the task using V2 task system
        task = create_task_v2(
            task_type=TaskTypeName.DATABASE_UPDATE_APPROVAL,
            user=ai_user,
            model_type=content_type,
            model_id=str(record_id),
            task_type_params=task_type_params,
            actions=actions,
        )

        return {
            "success": True,
            "task_id": task.id,  # type: ignore[attr-defined]
        }

    except Exception as e:
        logger.error(
            "Failed to create approval task",
            model=model,
            record_id=record_id,
            field_name=field_name,
            error=str(e),
            exc_info=True,
        )
        return {
            "success": False,
            "error": f"Task creation failed: {str(e)}",
        }
