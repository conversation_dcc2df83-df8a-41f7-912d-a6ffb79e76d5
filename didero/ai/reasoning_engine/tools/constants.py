"""
Constants for AI reasoning engine tools.

This module contains configuration constants used across the reasoning engine
to maintain consistency and make tuning easier.
"""

# Database Tools V1 Configuration
DATABASE_TOOLS_CONFIG = {
    # Confidence Scoring
    "CONFIDENCE_BASE_SCORE": 40.0,
    "CONFIDENCE_THRESHOLD": 70.0,
    "CONFIDENCE_HIGH_CAP": 90.0,
    "CONFIDENCE_LOW_FLOOR": 20.0,
    # Reasoning Quality Thresholds
    "DETAILED_REASONING_WORDS": 15,
    "MODERATE_REASONING_WORDS": 10,
    "BASIC_REASONING_WORDS": 5,
    # Confidence Score Adjustments
    "DETAILED_REASONING_BONUS": 20.0,
    "MODERATE_REASONING_BONUS": 15.0,
    "BASIC_REASONING_BONUS": 8.0,
    "BRIEF_REASONING_BONUS": 2.0,
    # Context-based Scoring
    "STRONG_CONTEXT_BONUS": 12.0,
    "WEAK_CONTEXT_BONUS": 6.0,
    "SUPPORTED_FIELD_BONUS": 10.0,
    "UNSUPPORTED_FIELD_PENALTY": -20.0,
    # Field Metadata Assessment
    "FIELD_CHOICES_MATCH_BONUS": 8.0,
    "LENGTH_SAFE_BONUS": 5.0,
    "LENGTH_RISKY_PENALTY": -3.0,
    "LENGTH_SAFE_RATIO": 0.5,
    "LENGTH_RISKY_RATIO": 0.9,
    # Context Quality Scoring
    "EXISTING_ENTITIES_BONUS": 6.0,
    "REFERENCES_BONUS": 4.0,
    # Value Change Assessment
    "REMOVING_DATA_PENALTY": -20.0,
    "ADDING_DATA_BONUS": 3.0,
    "NO_CHANGE_PENALTY": -10.0,
    # Supported Field Types (V1)
    "SUPPORTED_FIELD_TYPES": [
        "MoneyField",
        "CharField",
        "TextField",
        "DateField",
        "BooleanField",
    ],
    # Context Keywords for Reasoning Analysis
    "CONTEXT_KEYWORDS": [
        "email",
        "document",
        "invoice",
        "shipment",
        "supplier",
        "vendor",
    ],
}

# Field Converter Configuration
FIELD_CONVERTER_CONFIG = {
    # Cache settings
    "ENABLE_FIELD_METADATA_CACHE": True,
    "CACHE_TIMEOUT_SECONDS": 300,  # 5 minutes
    # Performance settings
    "MAX_RELATED_OBJECTS": 100,
    "QUERY_TIMEOUT_SECONDS": 30,
}


# Lookup Configuration
#
# DESIGN DECISION: Comprehensive Preloading Strategy
# ==================================================
#
# PROBLEM: The Object Retrieval Tool (ERD lines 235-258) needs to provide comprehensive
# context to the Reasoning Agent for AI decision-making. The original implementation
# caused async context warnings when accessing ForeignKey relationships during serialization.
#
# ANALYSIS OF ALTERNATIVES:
#
# Option 1: Smart Serialization (Field-Type Aware)
# - Pros: Efficient, only accesses loaded fields, respects Django's lazy loading
# - Cons: Complex logic, requires passing configuration through serialization
# - Pattern: Used in some codebase areas for specific use cases
#
# Option 2: Comprehensive Preloading (CHOSEN)
# - Pros: Simple, follows ERD requirements, eliminates async warnings, provides full AI context
# - Cons: Slightly higher memory usage, loads data that might not always be used
# - Pattern: Matches established codebase patterns (ERP sync, follow-up activities)
#
# WHY OPTION 2 IS BETTER FOR THIS USE CASE:
#
# 1. ERD COMPLIANCE: The ERD explicitly requires "related_objects" (lines 252-257)
#    for AI decision-making. The AI needs comprehensive context to make intelligent
#    routing decisions for procurement emails.
#
# 2. ESTABLISHED PATTERNS: The codebase consistently uses preloading for async contexts:
#    - ERP sync: preloads "purchase_order__team", "purchase_order__supplier"
#    - Follow-up activities: preloads "supplier", "team"
#    - Serialization utils: assumes relationships are preloaded
#
# 3. PERFORMANCE ACCEPTABLE: This is for AI decision-making (not high-frequency operations).
#    Single query with JOINs is more efficient than multiple lazy-loaded queries.
#
# 4. MAINTAINABILITY: Simple configuration-driven approach. No complex serialization logic.
#    Easy to extend for new models and relationships.
#
# 5. ASYNC SAFETY: Eliminates all async context warnings by preloading relationships
#    that the AI needs for context building.
#
# FIELD SELECTION RATIONALE:
# - Based on Django model introspection of all ForeignKey relationships
# - Prioritized fields likely needed for AI context (team, supplier, user, addresses)
# - Includes optional fields that may be needed depending on email content
#
LOOKUP_CONFIG = {
    "po_number": {
        "app": "orders",
        "model": "PurchaseOrder",
        "field": "po_number",
        "team_field": "team_id",
        # Comprehensive preloading for AI context (eliminates async warnings)
        "select_related": [
            "supplier",  # Original - supplier information for AI decisions
            "team",  # Team context for security and user identification
            "placed_by",  # User who placed the order (for context)
            "sender_address",  # Sender address information
            "shipping_address",  # Shipping details for logistics decisions
            "document",  # Associated document reference
        ],
        "prefetch_related": [
            "items",
            "shipments",
            "invoices",
            "order_acknowledgements",
        ],
        "updatable_fields": [
            "order_status",
            "requested_date",
            "total_cost",
            "vendor_notes",
            "internal_notes",
            "payment_terms",
        ],
        "related_lookups": {
            "shipments": "shipments",
            "invoices": "invoices",
            "items": "items",
        },
    },
    "invoice_number": {
        "app": "documents",
        "model": "Invoice",
        "field": "number",
        "team_field": "team_id",
        # Comprehensive preloading for AI context (eliminates async warnings)
        "select_related": [
            "supplier",  # Original - supplier information for AI decisions
            "team",  # Team context for security and user identification
            "billing_address",  # Billing address for invoice processing
            "purchase_order",  # Related PO for context and validation
        ],
        "prefetch_related": ["items", "line_items"],
        "updatable_fields": [
            "total_amount",
            "due_date",
            "payment_status",
            "notes",
            "freight_cost",
        ],
        "related_lookups": {"purchase_order": "purchase_order", "items": "items"},
    },
    "shipment_tracking": {
        "app": "orders",
        "model": "Shipment",
        "field": "tracking_number",
        "team_field": "purchase_order__team_id",
        # Comprehensive preloading for AI context (eliminates async warnings)
        "select_related": [
            "team",  # Team context for security and user identification
            "purchase_order",  # Original - related PO for context
            # Note: purchase_order__supplier removed to avoid deep joins
            # AI can access supplier through purchase_order if needed
        ],
        "prefetch_related": ["line_items", "shipping_documents"],
        "updatable_fields": [
            "carrier",
            "tracking_number",
            "ship_date",
            "estimated_delivery_date",
            "delivery_date",
            "status",
        ],
        "related_lookups": {"purchase_order": "purchase_order"},
    },
    "supplier_name": {
        "app": "suppliers",
        "model": "Supplier",
        "field": "name__icontains",
        "team_field": "team_id",
        "select_related": [],
        "prefetch_related": ["purchase_orders"],
        "updatable_fields": [
            "name",
            "email",
            "phone",
            "address",
            "contact_person",
            "payment_terms",
            "notes",
        ],
        "related_lookups": {"purchase_orders": "purchase_orders"},
    },
}
