"""
Constants for AI reasoning engine tools.

This module contains configuration constants used across the reasoning engine
to maintain consistency and make tuning easier.
"""

# Database Tools V1 Configuration
DATABASE_TOOLS_CONFIG = {
    # Confidence Scoring
    "CONFIDENCE_BASE_SCORE": 40.0,
    "CONFIDENCE_THRESHOLD": 70.0,
    "CONFIDENCE_HIGH_CAP": 90.0,
    "CONFIDENCE_LOW_FLOOR": 20.0,
    # Reasoning Quality Thresholds
    "DETAILED_REASONING_WORDS": 15,
    "MODERATE_REASONING_WORDS": 10,
    "BASIC_REASONING_WORDS": 5,
    # Confidence Score Adjustments
    "DETAILED_REASONING_BONUS": 20.0,
    "MODERATE_REASONING_BONUS": 15.0,
    "BASIC_REASONING_BONUS": 8.0,
    "BRIEF_REASONING_BONUS": 2.0,
    # Context-based Scoring
    "STRONG_CONTEXT_BONUS": 12.0,
    "WEAK_CONTEXT_BONUS": 6.0,
    "SUPPORTED_FIELD_BONUS": 10.0,
    "UNSUPPORTED_FIELD_PENALTY": -20.0,
    # Field Metadata Assessment
    "FIELD_CHOICES_MATCH_BONUS": 8.0,
    "LENGTH_SAFE_BONUS": 5.0,
    "LENGTH_RISKY_PENALTY": -3.0,
    "LENGTH_SAFE_RATIO": 0.5,
    "LENGTH_RISKY_RATIO": 0.9,
    # Context Quality Scoring
    "EXISTING_ENTITIES_BONUS": 6.0,
    "REFERENCES_BONUS": 4.0,
    # Value Change Assessment
    "REMOVING_DATA_PENALTY": -20.0,
    "ADDING_DATA_BONUS": 3.0,
    "NO_CHANGE_PENALTY": -10.0,
    # Supported Field Types (V1)
    "SUPPORTED_FIELD_TYPES": [
        "MoneyField",
        "CharField",
        "TextField",
        "DateField",
        "BooleanField",
    ],
    # Context Keywords for Reasoning Analysis
    "CONTEXT_KEYWORDS": [
        "email",
        "document",
        "invoice",
        "shipment",
        "supplier",
        "vendor",
    ],
}

# Field Converter Configuration
FIELD_CONVERTER_CONFIG = {
    # Cache settings
    "ENABLE_FIELD_METADATA_CACHE": True,
    "CACHE_TIMEOUT_SECONDS": 300,  # 5 minutes
    # Performance settings
    "MAX_RELATED_OBJECTS": 100,
    "QUERY_TIMEOUT_SECONDS": 30,
}


# Lookup Configuration
LOOKUP_CONFIG = {
    "po_number": {
        "app": "orders",
        "model": "PurchaseOrder",
        "field": "po_number",
        "team_field": "team_id",
        "select_related": ["supplier"],
        "prefetch_related": ["items", "shipments", "invoices"],
        "updatable_fields": [
            "order_status",
            "requested_date",
            "total_cost",
            "vendor_notes",
            "internal_notes",
            "payment_terms",
        ],
        "related_lookups": {
            "shipments": "shipments",
            "invoices": "invoices",
            "items": "items",
        },
    },
    "invoice_number": {
        "app": "documents",
        "model": "Invoice",
        "field": "number",
        "team_field": "team_id",
        "select_related": ["supplier", "purchase_order"],
        "prefetch_related": ["items"],
        "updatable_fields": [
            "total_amount",
            "due_date",
            "payment_status",
            "notes",
            "freight_cost",
        ],
        "related_lookups": {"purchase_order": "purchase_order", "items": "items"},
    },
    "shipment_tracking": {
        "app": "orders",
        "model": "Shipment",
        "field": "tracking_number",
        "team_field": "purchase_order__team_id",
        "select_related": ["purchase_order", "purchase_order__supplier"],
        "prefetch_related": [],
        "updatable_fields": [
            "carrier",
            "tracking_number",
            "ship_date",
            "estimated_delivery_date",
            "delivery_date",
            "status",
        ],
        "related_lookups": {"purchase_order": "purchase_order"},
    },
    "supplier_name": {
        "app": "suppliers",
        "model": "Supplier",
        "field": "name__icontains",
        "team_field": "team_id",
        "select_related": [],
        "prefetch_related": ["purchase_orders"],
        "updatable_fields": [
            "name",
            "email",
            "phone",
            "address",
            "contact_person",
            "payment_terms",
            "notes",
        ],
        "related_lookups": {"purchase_orders": "purchase_orders"},
    },
}
