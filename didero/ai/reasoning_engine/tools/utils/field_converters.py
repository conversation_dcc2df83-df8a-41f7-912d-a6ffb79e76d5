"""
Field converters for database update tools.

This module provides type-safe conversion utilities for converting AI-suggested
values to Django field types, handling validation and formatting.

V1 Scope: MoneyField, CharField, DateField, BooleanField
"""

import time
from datetime import date, datetime
from decimal import Decimal, InvalidOperation
from typing import Any, Dict, <PERSON><PERSON>, Tu<PERSON>, Union

import structlog
from django.db import models
from djmoney import money

from ..constants import FIELD_CONVERTER_CONFIG

logger = structlog.get_logger(__name__)

# Field metadata cache to improve performance
_field_metadata_cache: Dict[str, Dict[str, Any]] = {}
_cache_timestamps: Dict[str, float] = {}


class FieldConversionError(Exception):
    """Raised when field conversion fails."""

    def __init__(self, field_type: str, value: Any, reason: str):
        self.field_type = field_type
        self.value = value
        self.reason = reason
        super().__init__(f"Failed to convert '{value}' to {field_type}: {reason}")


class FieldConverter:
    """
    Provides type-safe conversion of AI-suggested values to Django field types.

    Each converter method returns a tuple of (converted_value, display_string)
    where converted_value can be stored in the database and display_string
    is human-readable for task display.
    """

    @staticmethod
    def get_field_info(model_class: type, field_name: str) -> Dict[str, Any]:
        """
        Get metadata about a Django model field with caching for performance.

        Args:
            model_class: Django model class
            field_name: Name of the field

        Returns:
            Dictionary containing field metadata

        Raises:
            FieldConversionError: If field doesn't exist
        """
        # Create cache key
        cache_key = (
            f"{model_class._meta.app_label}.{model_class._meta.model_name}.{field_name}"
        )

        # Check cache if enabled
        if FIELD_CONVERTER_CONFIG["ENABLE_FIELD_METADATA_CACHE"]:
            current_time = time.time()

            # Check if we have valid cached data
            if (
                cache_key in _field_metadata_cache
                and cache_key in _cache_timestamps
                and current_time - _cache_timestamps[cache_key]
                < FIELD_CONVERTER_CONFIG["CACHE_TIMEOUT_SECONDS"]
            ):
                logger.debug(f"Using cached field metadata for {cache_key}")
                return _field_metadata_cache[cache_key]

        try:
            field = model_class._meta.get_field(field_name)

            info = {
                "field_type": field.__class__.__name__,
                "field_class": field.__class__,
                "max_length": getattr(field, "max_length", None),
                "null": field.null,
                "blank": field.blank,
                "choices": getattr(field, "choices", None),
                "help_text": field.help_text,
                "verbose_name": field.verbose_name,
            }

            # Add MoneyField specific info
            if hasattr(field, "currency_field_name"):
                info["currency_field_name"] = field.currency_field_name
                info["default_currency"] = getattr(field, "default_currency", "USD")

            # Cache the result if caching is enabled
            if FIELD_CONVERTER_CONFIG["ENABLE_FIELD_METADATA_CACHE"]:
                _field_metadata_cache[cache_key] = info
                _cache_timestamps[cache_key] = time.time()
                logger.debug(f"Cached field metadata for {cache_key}")

            return info

        except models.FieldDoesNotExist:
            raise FieldConversionError(
                "FieldDoesNotExist",
                field_name,
                f"Field '{field_name}' not found on model {model_class.__name__}",
            )

    @staticmethod
    def convert_money_field(
        value: Union[str, int, float, Decimal], currency: str = "USD"
    ) -> Tuple[money.Money, str]:
        """
        Convert value to MoneyField (djmoney.Money).

        Args:
            value: Input value (string, number)
            currency: Currency code (default: USD)

        Returns:
            Tuple of (Money object, display string)

        Raises:
            FieldConversionError: If conversion fails
        """
        try:
            # Handle string inputs (common from AI)
            if isinstance(value, str):
                # Remove common currency symbols and whitespace
                cleaned_value = (
                    value.strip().replace("$", "").replace(",", "").replace(" ", "")
                )

                # Try to extract currency from string if present
                if any(
                    curr in cleaned_value.upper()
                    for curr in ["USD", "EUR", "GBP", "CAD"]
                ):
                    for curr in ["USD", "EUR", "GBP", "CAD"]:
                        if curr in cleaned_value.upper():
                            currency = curr
                            cleaned_value = (
                                cleaned_value.upper().replace(curr, "").strip()
                            )
                            break

                try:
                    decimal_value = Decimal(cleaned_value)
                except (ValueError, InvalidOperation):
                    raise FieldConversionError(
                        "MoneyField",
                        value,
                        f"Cannot parse '{cleaned_value}' as a decimal number",
                    )
            else:
                decimal_value = Decimal(str(value))

            money_obj = money.Money(decimal_value, currency)
            display_string = f"${decimal_value:,.2f} {currency}"

            return money_obj, display_string

        except Exception as e:
            if isinstance(e, FieldConversionError):
                raise
            raise FieldConversionError("MoneyField", value, str(e))

    @staticmethod
    def convert_char_field(
        value: Union[str, int, float], max_length: Optional[int] = None
    ) -> Tuple[str, str]:
        """
        Convert value to CharField.

        Args:
            value: Input value
            max_length: Maximum allowed length

        Returns:
            Tuple of (string value, display string)

        Raises:
            FieldConversionError: If conversion fails
        """
        try:
            # Convert to string
            str_value = str(value).strip()

            # Check max_length constraint
            if max_length and len(str_value) > max_length:
                raise FieldConversionError(
                    "CharField",
                    value,
                    f"Value length ({len(str_value)}) exceeds max_length ({max_length})",
                )

            # Empty string validation
            if not str_value:
                logger.warning(
                    "Converting empty value to CharField", original_value=value
                )

            return str_value, str_value

        except Exception as e:
            if isinstance(e, FieldConversionError):
                raise
            raise FieldConversionError("CharField", value, str(e))

    @staticmethod
    def convert_date_field(value: Union[str, date, datetime]) -> Tuple[date, str]:
        """
        Convert value to DateField.

        Args:
            value: Input value (string, date, datetime)

        Returns:
            Tuple of (date object, display string)

        Raises:
            FieldConversionError: If conversion fails
        """
        try:
            if isinstance(value, date):
                return value, value.strftime("%Y-%m-%d")

            if isinstance(value, datetime):
                return value.date(), value.date().strftime("%Y-%m-%d")

            if isinstance(value, str):
                # Try common date formats
                date_formats = [
                    "%Y-%m-%d",  # 2024-01-15
                    "%m/%d/%Y",  # 01/15/2024
                    "%d/%m/%Y",  # 15/01/2024
                    "%Y/%m/%d",  # 2024/01/15
                    "%B %d, %Y",  # January 15, 2024
                    "%b %d, %Y",  # Jan 15, 2024
                    "%d %B %Y",  # 15 January 2024
                    "%d %b %Y",  # 15 Jan 2024
                ]

                for date_format in date_formats:
                    try:
                        parsed_date = datetime.strptime(
                            value.strip(), date_format
                        ).date()
                        return parsed_date, parsed_date.strftime("%Y-%m-%d")
                    except ValueError:
                        continue

                raise FieldConversionError(
                    "DateField",
                    value,
                    f"Cannot parse date string '{value}'. Supported formats: {date_formats}",
                )

            raise FieldConversionError(
                "DateField",
                value,
                f"Unsupported type {type(value)} for date conversion",
            )

        except Exception as e:
            if isinstance(e, FieldConversionError):
                raise
            raise FieldConversionError("DateField", value, str(e))

    @staticmethod
    def convert_boolean_field(value: Union[str, bool, int]) -> Tuple[bool, str]:
        """
        Convert value to BooleanField.

        Args:
            value: Input value

        Returns:
            Tuple of (boolean value, display string)

        Raises:
            FieldConversionError: If conversion fails
        """
        try:
            if isinstance(value, bool):
                return value, "Yes" if value else "No"

            if isinstance(value, int):
                bool_value = bool(value)
                return bool_value, "Yes" if bool_value else "No"

            if isinstance(value, str):
                value_lower = value.strip().lower()

                # True values
                if value_lower in ["true", "yes", "y", "1", "on", "enabled", "active"]:
                    return True, "Yes"

                # False values
                if value_lower in [
                    "false",
                    "no",
                    "n",
                    "0",
                    "off",
                    "disabled",
                    "inactive",
                ]:
                    return False, "No"

                raise FieldConversionError(
                    "BooleanField",
                    value,
                    f"Cannot parse '{value}' as boolean. Use: true/false, yes/no, 1/0, etc.",
                )

            raise FieldConversionError(
                "BooleanField",
                value,
                f"Unsupported type {type(value)} for boolean conversion",
            )

        except Exception as e:
            if isinstance(e, FieldConversionError):
                raise
            raise FieldConversionError("BooleanField", value, str(e))

    @classmethod
    def convert_field_value(
        cls, field_type: str, value: Any, field_info: Optional[Dict[str, Any]] = None
    ) -> Tuple[Any, str]:
        """
        Convert a value to the appropriate Django field type.

        Args:
            field_type: Django field type name
            value: Value to convert
            field_info: Optional field metadata from get_field_info()

        Returns:
            Tuple of (converted_value, display_string)

        Raises:
            FieldConversionError: If conversion fails or field type unsupported
        """
        field_info = field_info or {}

        # V1 Supported field types
        if field_type == "MoneyField":
            currency = field_info.get("default_currency", "USD")
            return cls.convert_money_field(value, currency)

        elif field_type in ["CharField", "TextField"]:
            max_length = field_info.get("max_length")
            return cls.convert_char_field(value, max_length)

        elif field_type == "DateField":
            return cls.convert_date_field(value)

        elif field_type == "BooleanField":
            return cls.convert_boolean_field(value)

        else:
            raise FieldConversionError(
                field_type,
                value,
                f"Field type '{field_type}' not supported in V1. Supported: MoneyField, CharField, TextField, DateField, BooleanField",
            )

    @classmethod
    def validate_and_convert(
        cls, model_class: type, field_name: str, value: Any
    ) -> Tuple[Any, str, Dict[str, Any]]:
        """
        Complete validation and conversion workflow.

        Args:
            model_class: Django model class
            field_name: Field name to update
            value: AI suggested value

        Returns:
            Tuple of (converted_value, display_string, field_info)

        Raises:
            FieldConversionError: If validation or conversion fails
        """
        # Get field metadata
        field_info = cls.get_field_info(model_class, field_name)
        field_type = field_info["field_type"]

        # Validate field is not read-only
        if field_name in ["id", "created_at", "modified_at"]:
            raise FieldConversionError(
                field_type,
                value,
                f"Field '{field_name}' is read-only and cannot be updated",
            )

        # Convert value
        converted_value, display_string = cls.convert_field_value(
            field_type, value, field_info
        )

        # Log successful conversion
        logger.info(
            "Field conversion successful",
            model=model_class.__name__,
            field_name=field_name,
            field_type=field_type,
            original_value=value,
            converted_value=converted_value,
            display_string=display_string,
        )

        return converted_value, display_string, field_info
