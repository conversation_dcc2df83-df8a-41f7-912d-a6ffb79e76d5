"""
Database lookup tools for the Context Builder Agent.

These tools query the database to find existing entities based on references
and provide field metadata for AI decision-making.
"""

from typing import Any, Dict, List

import structlog
from django.apps import apps
from django.db import models
from pydantic_ai import RunContext

from ...schemas.dependencies import ContextBuilderDependencies
from ..constants import FIELD_CONVERTER_CONFIG, LOOKUP_CONFIG
from ..utils.field_converters import FieldConverter

logger = structlog.get_logger(__name__)


async def lookup_entity_in_database(
    ctx: RunContext[ContextBuilderDependencies],
    reference_type: str,
    reference_value: str,
    include_field_metadata: bool = True,
) -> Dict[str, Any]:
    """
    Look up an entity in the database by reference and provide field metadata.

    This tool finds entities and returns comprehensive information including
    field metadata for AI database update decision-making.

    Args:
        ctx: The run context containing dependencies
        reference_type: Type of reference (e.g., "po_number", "invoice_number", "shipment_tracking")
        reference_value: The reference value to search for
        include_field_metadata: Whether to include field metadata for updates

    Returns:
        Dict containing:
        - exists: bool indicating if entity was found
        - entity: The entity data if found
        - record_id: Primary key of the found entity
        - model_info: Model class information
        - field_metadata: Field metadata for AI updates (if include_field_metadata=True)
        - related_entities: Related entities
        - metadata: Additional context
    """
    try:
        team_id = ctx.deps.team_id

        # Map reference types to model lookups
        lookup_config = _get_lookup_config()

        if reference_type not in lookup_config:
            logger.warning(
                "Unsupported reference type",
                reference_type=reference_type,
                supported_types=list(lookup_config.keys()),
            )
            return {
                "exists": False,
                "entity": None,
                "record_id": None,
                "model_info": None,
                "field_metadata": {},
                "related_entities": {},
                "metadata": {"error": f"Unsupported reference type: {reference_type}"},
            }

        config = lookup_config[reference_type]
        model_class = apps.get_model(config["app"], config["model"])

        # Build query
        query_params = {config["field"]: reference_value}
        if config.get("team_field"):
            query_params[config["team_field"]] = str(team_id)

        # Execute query with related data
        queryset = model_class.objects.filter(**query_params)
        if config.get("select_related"):
            queryset = queryset.select_related(*config["select_related"])
        if config.get("prefetch_related"):
            queryset = queryset.prefetch_related(*config["prefetch_related"])

        entity = queryset.first()

        if not entity:
            return {
                "exists": False,
                "entity": None,
                "record_id": None,
                "model_info": None,
                "field_metadata": {},
                "related_entities": {},
                "metadata": {},
            }

        # Convert entity to dict (safe serialization)
        entity_data = _serialize_model_instance(entity)

        # Get model info
        model_info = {
            "app_label": model_class._meta.app_label,
            "model_name": model_class._meta.model_name,
            "model_verbose_name": model_class._meta.verbose_name,
            "full_model_path": f"{model_class._meta.app_label}.{model_class._meta.model_name}",
        }

        # Get field metadata for AI updates
        field_metadata = {}
        if include_field_metadata:
            field_metadata = _get_field_metadata(
                model_class, config.get("updatable_fields", [])
            )

        # Get related entities
        related_entities = _get_related_entities(
            entity, config.get("related_lookups", {})
        )

        # Build metadata
        metadata = _build_entity_metadata(entity, config)

        logger.info(
            "Entity lookup successful",
            reference_type=reference_type,
            reference_value=reference_value,
            model=model_class.__name__,
            record_id=entity.pk,
            has_field_metadata=bool(field_metadata),
        )

        return {
            "exists": True,
            "entity": entity_data,
            "record_id": entity.pk,
            "model_info": model_info,
            "field_metadata": field_metadata,
            "related_entities": related_entities,
            "metadata": metadata,
        }

    except Exception as e:
        logger.error(
            "Entity lookup failed",
            reference_type=reference_type,
            reference_value=reference_value,
            error=str(e),
            exc_info=True,
        )
        return {
            "exists": False,
            "entity": None,
            "record_id": None,
            "model_info": None,
            "field_metadata": {},
            "related_entities": {},
            "metadata": {"error": str(e)},
        }


def _get_lookup_config() -> Dict[str, Dict[str, Any]]:
    """
    Configuration for different entity lookup types.

    Each config defines how to find and process different entity types.
    """
    return LOOKUP_CONFIG


def _serialize_model_instance(instance: models.Model) -> Dict[str, Any]:
    """
    Safely serialize a Django model instance to a dictionary.

    Handles common field types and avoids serialization issues.
    """
    data = {}

    for field in instance._meta.fields:
        field_name = field.name
        try:
            value = getattr(instance, field_name)

            # Handle special field types
            if hasattr(value, "isoformat"):  # datetime/date
                data[field_name] = value.isoformat()
            elif hasattr(value, "amount"):  # Money field
                data[field_name] = {
                    "amount": str(value.amount),
                    "currency": str(value.currency),
                }
            elif value is None:
                data[field_name] = None
            else:
                data[field_name] = str(value)

        except Exception as e:
            logger.warning(f"Failed to serialize field {field_name}: {e}")
            data[field_name] = None

    return data


def _get_field_metadata(
    model_class: type, updatable_fields: List[str]
) -> Dict[str, Dict[str, Any]]:
    """
    Get field metadata for AI database updates.

    Only includes fields that are updatable and supported by the field converter.
    """
    field_metadata = {}
    supported_field_types = [
        "MoneyField",
        "CharField",
        "TextField",
        "DateField",
        "BooleanField",
    ]

    for field_name in updatable_fields:
        try:
            field_info = FieldConverter.get_field_info(model_class, field_name)
            field_type = field_info["field_type"]

            # Only include supported field types for V1
            if field_type in supported_field_types:
                field_metadata[field_name] = {
                    "field_type": field_type,
                    "verbose_name": field_info["verbose_name"],
                    "help_text": field_info["help_text"],
                    "null": field_info["null"],
                    "blank": field_info["blank"],
                    "max_length": field_info.get("max_length"),
                    "choices": field_info.get("choices"),
                    "updatable": True,
                    "supported_in_v1": True,
                }

                # Add MoneyField specific metadata
                if field_type == "MoneyField":
                    field_metadata[field_name].update(
                        {
                            "currency_field_name": field_info.get(
                                "currency_field_name"
                            ),
                            "default_currency": field_info.get(
                                "default_currency", "USD"
                            ),
                        }
                    )
            else:
                # Include metadata but mark as unsupported
                field_metadata[field_name] = {
                    "field_type": field_type,
                    "verbose_name": field_info["verbose_name"],
                    "updatable": False,
                    "supported_in_v1": False,
                    "reason": f"Field type '{field_type}' not supported in V1",
                }

        except Exception as e:
            logger.warning(f"Failed to get metadata for field {field_name}: {e}")
            field_metadata[field_name] = {
                "updatable": False,
                "supported_in_v1": False,
                "error": str(e),
            }

    return field_metadata


def _get_related_entities(
    instance: models.Model, related_lookups: Dict[str, str]
) -> Dict[str, Any]:
    """
    Get related entities for the found instance.
    """
    related_data = {}

    for relation_name, lookup_path in related_lookups.items():
        try:
            related_obj = getattr(instance, lookup_path)

            if hasattr(related_obj, "all"):  # Many-to-many or reverse FK
                max_objects = FIELD_CONVERTER_CONFIG["MAX_RELATED_OBJECTS"]
                related_data[relation_name] = [
                    _serialize_model_instance(obj)
                    for obj in related_obj.all()[:max_objects]
                ]
            elif related_obj:  # Single related object
                related_data[relation_name] = _serialize_model_instance(related_obj)
            else:
                related_data[relation_name] = None

        except Exception as e:
            logger.warning(f"Failed to get related data for {relation_name}: {e}")
            related_data[relation_name] = None

    return related_data


def _build_entity_metadata(
    instance: models.Model, config: Dict[str, Any]
) -> Dict[str, Any]:
    """
    Build additional metadata about the entity.
    """
    metadata = {
        "record_type": config["model"],
        "found_via": config["field"],
    }

    # Add commonly useful fields
    if hasattr(instance, "status"):
        metadata["status"] = str(instance.status)
    if hasattr(instance, "created_at"):
        metadata["created_at"] = instance.created_at.isoformat()
    if hasattr(instance, "modified_at"):
        metadata["modified_at"] = instance.modified_at.isoformat()

    # Add model-specific metadata
    if config["model"] == "PurchaseOrder":
        if hasattr(instance, "supplier") and instance.supplier:
            metadata["supplier_name"] = instance.supplier.name
        if hasattr(instance, "total_cost"):
            metadata["total_cost"] = (
                str(instance.total_cost) if instance.total_cost else None
            )

    elif config["model"] == "Invoice":
        if hasattr(instance, "purchase_order") and instance.purchase_order:
            metadata["po_number"] = instance.purchase_order.number
        if hasattr(instance, "total_amount"):
            metadata["total_amount"] = (
                str(instance.total_amount) if instance.total_amount else None
            )

    elif config["model"] == "Shipment":
        if hasattr(instance, "purchase_order") and instance.purchase_order:
            metadata["po_number"] = instance.purchase_order.number
        if hasattr(instance, "carrier"):
            metadata["carrier"] = str(instance.carrier) if instance.carrier else None

    return metadata
