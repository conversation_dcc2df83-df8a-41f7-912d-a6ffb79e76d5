"""
Unit tests for Database Tools V1.

Tests the core functionality of database tools without requiring
full Django/database setup.
"""

import unittest
from unittest.mock import Mock, patch

from ..constants import DATABASE_TOOLS_CONFIG
from ..utils.field_converters import <PERSON><PERSON>onverter, FieldConversionError


class TestDatabaseToolsConstants(unittest.TestCase):
    """Test that constants are properly configured."""

    def test_database_tools_config_exists(self):
        """Test that DATABASE_TOOLS_CONFIG contains expected keys."""
        required_keys = [
            "CONFIDENCE_BASE_SCORE",
            "CONFIDENCE_THRESHOLD",
            "SUPPORTED_FIELD_TYPES",
            "CONTEXT_KEYWORDS",
        ]

        for key in required_keys:
            self.assertIn(key, DATABASE_TOOLS_CONFIG, f"Missing config key: {key}")

    def test_confidence_scoring_values(self):
        """Test that confidence scoring values are reasonable."""
        config = DATABASE_TOOLS_CONFIG

        # Base score should be reasonable (0-100)
        self.assertGreaterEqual(config["CONFIDENCE_BASE_SCORE"], 0)
        self.assertLessEqual(config["CONFIDENCE_BASE_SCORE"], 100)

        # Threshold should be between base and cap
        self.assertGreater(
            config["CONFIDENCE_THRESHOLD"], config["CONFIDENCE_BASE_SCORE"]
        )
        self.assertLess(config["CONFIDENCE_THRESHOLD"], config["CONFIDENCE_HIGH_CAP"])

        # Supported field types should include key types
        supported_types = config["SUPPORTED_FIELD_TYPES"]
        expected_types = [
            "CharField",
            "TextField",
            "BooleanField",
            "DateField",
            "MoneyField",
        ]
        for field_type in expected_types:
            self.assertIn(field_type, supported_types)


class TestFieldConverter(unittest.TestCase):
    """Test field converter functionality."""

    def test_field_conversion_error(self):
        """Test FieldConversionError creation."""
        error = FieldConversionError("CharField", "invalid_value", "Test reason")

        self.assertEqual(error.field_type, "CharField")
        self.assertEqual(error.value, "invalid_value")
        self.assertEqual(error.reason, "Test reason")
        self.assertIn("CharField", str(error))
        self.assertIn("invalid_value", str(error))

    @patch(
        "didero.ai.reasoning_engine.tools.utils.field_converters.FIELD_CONVERTER_CONFIG"
    )
    def test_get_field_info_caching_disabled(self, mock_config):
        """Test get_field_info with caching disabled."""
        mock_config.__getitem__.return_value = False  # Disable caching

        # Mock Django model field
        mock_field = Mock()
        mock_field.__class__.__name__ = "CharField"
        mock_field.max_length = 100
        mock_field.null = True
        mock_field.blank = True
        mock_field.choices = None
        mock_field.help_text = "Test help"
        mock_field.verbose_name = "Test field"

        mock_model = Mock()
        mock_model._meta.get_field.return_value = mock_field
        mock_model._meta.app_label = "test_app"
        mock_model._meta.model_name = "test_model"

        info = FieldConverter.get_field_info(mock_model, "test_field")

        self.assertEqual(info["field_type"], "CharField")
        self.assertEqual(info["max_length"], 100)
        self.assertEqual(info["verbose_name"], "Test field")

    def test_convert_money_field_valid(self):
        """Test money field conversion with valid input."""
        from decimal import Decimal

        with patch(
            "didero.ai.reasoning_engine.tools.utils.field_converters.money.Money"
        ) as mock_money:
            mock_money_instance = Mock()
            mock_money.return_value = mock_money_instance

            result_value, result_display = FieldConverter.convert_money_field(
                "100.50", "USD"
            )

            # Money converter calls with Decimal, not string
            mock_money.assert_called_once_with(Decimal("100.50"), "USD")
            self.assertEqual(result_value, mock_money_instance)

    def test_convert_boolean_field_variations(self):
        """Test boolean field conversion with various inputs."""
        true_values = ["true", "True", "TRUE", "yes", "1", 1, True]
        false_values = ["false", "False", "FALSE", "no", "0", 0, False]

        for value in true_values:
            result_value, result_display = FieldConverter.convert_boolean_field(value)
            self.assertTrue(result_value, f"Value {value} should convert to True")
            self.assertEqual(result_display, "Yes")

        for value in false_values:
            result_value, result_display = FieldConverter.convert_boolean_field(value)
            self.assertFalse(result_value, f"Value {value} should convert to False")
            self.assertEqual(result_display, "No")

    def test_convert_char_field_length_validation(self):
        """Test character field length validation."""
        # Test within limits
        result_value, result_display = FieldConverter.convert_char_field(
            "test", max_length=10
        )
        self.assertEqual(result_value, "test")
        self.assertEqual(result_display, "test")

        # Test exceeding limits
        with self.assertRaises(FieldConversionError) as context:
            FieldConverter.convert_char_field("very long text", max_length=5)

        # Current message format: "Value length (14) exceeds max_length (5)"
        self.assertIn("exceeds max_length", str(context.exception))


class TestConfidenceScoring(unittest.TestCase):
    """Test confidence scoring algorithm components."""

    def test_reasoning_quality_scoring(self):
        """Test that longer reasoning gets higher scores."""
        # This would be a unit test for the confidence scoring function
        # when extracted to a standalone function
        pass

    def test_context_keyword_matching(self):
        """Test context keyword detection."""
        config = DATABASE_TOOLS_CONFIG
        keywords = config["CONTEXT_KEYWORDS"]

        # Test that expected keywords are present
        expected_keywords = ["email", "document", "invoice", "supplier"]
        for keyword in expected_keywords:
            self.assertIn(keyword, keywords)

    def test_field_type_support_scoring(self):
        """Test that supported field types are properly configured."""
        config = DATABASE_TOOLS_CONFIG

        # Verify bonus/penalty values are reasonable
        self.assertGreater(config["SUPPORTED_FIELD_BONUS"], 0)
        self.assertLess(config["UNSUPPORTED_FIELD_PENALTY"], 0)


if __name__ == "__main__":
    unittest.main()
