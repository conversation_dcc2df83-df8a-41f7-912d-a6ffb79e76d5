#!/usr/bin/env python3
"""
Basic test script for database tools V1 implementation.

This script tests the field conversion functionality without requiring 
a full Django environment setup.
"""

import os
import sys
from datetime import date, datetime
from decimal import Decimal

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Setup Django
import django
from django.conf import settings
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'didero.settings.common')
django.setup()

def test_field_converters():
    """Test the field converter functionality."""
    print("Testing field converters...")
    
    try:
        from didero.ai.reasoning_engine.tools.utils.field_converters import (
            FieldConverter, 
            FieldConversionError
        )
        
        # Test MoneyField conversion
        print("\n1. Testing MoneyField conversion:")
        test_cases = [
            ("$1234.56", "USD"),
            ("1000", "USD"),
            ("€500.25", "EUR"),
            ("1,234.56 USD", "USD"),
        ]
        
        for value, currency in test_cases:
            try:
                money_obj, display = FieldConverter.convert_money_field(value, currency)
                print(f"  ✓ '{value}' -> {money_obj} (display: {display})")
            except FieldConversionError as e:
                print(f"  ✗ '{value}' -> Error: {e}")
        
        # Test CharField conversion
        print("\n2. Testing CharField conversion:")
        test_cases = [
            ("Hello World", None),
            ("   Trimmed   ", None),
            ("Long text that exceeds limit", 10),
            (123, None),
        ]
        
        for value, max_length in test_cases:
            try:
                str_value, display = FieldConverter.convert_char_field(value, max_length)
                print(f"  ✓ '{value}' (max_length={max_length}) -> '{str_value}' (display: {display})")
            except FieldConversionError as e:
                print(f"  ✗ '{value}' (max_length={max_length}) -> Error: {e}")
        
        # Test DateField conversion
        print("\n3. Testing DateField conversion:")
        test_cases = [
            "2024-01-15",
            "01/15/2024",
            "January 15, 2024",
            "Jan 15, 2024",
            date(2024, 1, 15),
            datetime(2024, 1, 15, 10, 30),
            "invalid date",
        ]
        
        for value in test_cases:
            try:
                date_obj, display = FieldConverter.convert_date_field(value)
                print(f"  ✓ '{value}' -> {date_obj} (display: {display})")
            except FieldConversionError as e:
                print(f"  ✗ '{value}' -> Error: {e}")
        
        # Test BooleanField conversion
        print("\n4. Testing BooleanField conversion:")
        test_cases = [
            "true", "false", "yes", "no", "1", "0",
            "on", "off", "enabled", "disabled",
            True, False, 1, 0, "invalid"
        ]
        
        for value in test_cases:
            try:
                bool_value, display = FieldConverter.convert_boolean_field(value)
                print(f"  ✓ '{value}' -> {bool_value} (display: {display})")
            except FieldConversionError as e:
                print(f"  ✗ '{value}' -> Error: {e}")
        
        print("\n✅ Field converter tests completed!")
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("Make sure you're running this from the Django project root.")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False


def test_task_configurations():
    """Test that task configurations are properly defined."""
    print("\nTesting task configurations...")
    
    try:
        # Test TaskType enum includes our new type
        from didero.tasks.schemas import TaskType
        if hasattr(TaskType, 'DATABASE_UPDATE_APPROVAL'):
            print("  ✓ DATABASE_UPDATE_APPROVAL found in TaskType enum")
        else:
            print("  ✗ DATABASE_UPDATE_APPROVAL missing from TaskType enum")
            return False
        
        # Test TaskActionType enum includes our new actions
        from didero.tasks.schemas import TaskActionType
        required_actions = ['EXECUTE_DATABASE_UPDATE', 'REJECT_DATABASE_UPDATE']
        for action in required_actions:
            if hasattr(TaskActionType, action):
                print(f"  ✓ {action} found in TaskActionType enum")
            else:
                print(f"  ✗ {action} missing from TaskActionType enum")
                return False
        
        print("  ✅ Task configuration tests passed!")
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False


def main():
    """Main test function."""
    print("🧪 Testing Database Tools V1 Implementation")
    print("=" * 50)
    
    success = True
    
    # Test field converters
    if not test_field_converters():
        success = False
    
    # Test task configurations
    if not test_task_configurations():
        success = False
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 All tests passed! Database Tools V1 implementation is ready.")
    else:
        print("❌ Some tests failed. Please check the implementation.")
    
    return success


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)