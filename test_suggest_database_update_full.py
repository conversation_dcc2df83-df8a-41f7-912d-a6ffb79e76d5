#!/usr/bin/env python3
"""
Comprehensive test for the completed suggest_database_update implementation.
"""

import asyncio
import os
import sys
from dataclasses import dataclass, field
from typing import Dict, Any

# Set up Django environment
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "didero.settings.common")

import django
django.setup()

from pydantic_ai import RunContext

@dataclass
class MockReasoningDependencies:
    """Mock dependencies for testing."""
    team_id: int
    built_context: Dict[str, Any] = field(default_factory=dict)

async def test_suggest_database_update():
    """Test the suggest_database_update implementation comprehensively."""
    print("🧪 COMPREHENSIVE TEST: suggest_database_update")
    print("=" * 60)

    # Import the implementation
    try:
        from didero.ai.reasoning_engine.tools.actions.database_tools import suggest_database_update
        print("✅ Successfully imported suggest_database_update")
    except ImportError as e:
        print(f"❌ Import failed: {e}")
        return False

    # Test 1: Basic Function Call Test
    print("\n🧪 Test 1: Basic Function Call")
    print("-" * 30)
    
    mock_context = {
        "entities": {
            "PO-2025-1000": {
                "exists": True,
                "record_id": 1,
                "entity": {
                    "id": 1,
                    "po_number": "PO-2025-1000",
                    "vendor_notes": "Original notes"
                }
            }
        }
    }

    deps = MockReasoningDependencies(team_id=1, built_context=mock_context)
    ctx = RunContext[MockReasoningDependencies](deps)

    try:
        result = await suggest_database_update(
            ctx=ctx,
            model="orders.PurchaseOrder",
            field="vendor_notes", 
            type="TextField",
            current_value="Original notes",
            suggested_value="Updated by AI test",
            reason="Testing the implementation with detailed reasoning to validate confidence scoring",
            record_identifier="PO-2025-1000"
        )
        
        print("✅ Function executed successfully")
        print(f"📊 Result keys: {list(result.keys())}")
        print(f"📈 Confidence score: {result.get('confidence')}")
        print(f"❓ Requires approval: {result.get('requires_approval')}")
        print(f"📋 Task created: {result.get('task_created')}")
        
        if result.get('error'):
            print(f"⚠️  Error returned: {result['error']}")
        
    except Exception as e:
        print(f"❌ Function call failed: {e}")
        import traceback
        traceback.print_exc()
        return False

    # Test 2: Confidence Scoring Test
    print("\n🧪 Test 2: Confidence Scoring Validation")
    print("-" * 30)
    
    confidence_tests = [
        {
            "name": "High confidence (detailed reasoning)",
            "reason": "Email received from supplier containing updated vendor information with specific details about order changes and new contact information",
            "expected_range": (75, 95)
        },
        {
            "name": "Medium confidence (moderate reasoning)",
            "reason": "Document shows updated vendor notes information",
            "expected_range": (60, 80)
        },
        {
            "name": "Low confidence (brief reasoning)",
            "reason": "Update needed",
            "expected_range": (25, 65)
        }
    ]
    
    confidence_results = []
    
    for test_case in confidence_tests:
        try:
            result = await suggest_database_update(
                ctx=ctx,
                model="orders.PurchaseOrder",
                field="vendor_notes",
                type="TextField", 
                current_value="Original",
                suggested_value="Updated",
                reason=test_case["reason"],
                record_identifier="PO-2025-1000"
            )
            
            confidence = result.get('confidence', 0)
            min_expected, max_expected = test_case["expected_range"]
            in_range = min_expected <= confidence <= max_expected
            
            status = "✅ PASS" if in_range else "❌ FAIL"
            print(f"{status} {test_case['name']}: {confidence:.1f} (expected {min_expected}-{max_expected})")
            
            confidence_results.append(in_range)
            
        except Exception as e:
            print(f"❌ {test_case['name']}: Exception - {e}")
            confidence_results.append(False)

    # Test 3: Error Handling Test
    print("\n🧪 Test 3: Error Handling Validation")  
    print("-" * 30)

    error_tests = [
        {
            "name": "Invalid model path",
            "params": {
                "model": "invalid.InvalidModel",
                "field": "test_field",
                "type": "CharField",
                "current_value": "test",
                "suggested_value": "new_test",
                "reason": "Test error handling"
            },
            "should_error": True
        },
        {
            "name": "Invalid field name",
            "params": {
                "model": "orders.PurchaseOrder", 
                "field": "nonexistent_field",
                "type": "CharField",
                "current_value": "test",
                "suggested_value": "new_test", 
                "reason": "Test error handling"
            },
            "should_error": True
        },
        {
            "name": "No context (missing record_id)",
            "params": {
                "model": "orders.PurchaseOrder",
                "field": "vendor_notes",
                "type": "TextField",
                "current_value": "test",
                "suggested_value": "new_test",
                "reason": "Test error handling"
            },
            "should_error": True,
            "use_empty_context": True
        }
    ]
    
    error_results = []
    
    for test_case in error_tests:
        try:
            # Use empty context if specified
            if test_case.get("use_empty_context"):
                empty_deps = MockReasoningDependencies(team_id=1, built_context={})
                test_ctx = RunContext[MockReasoningDependencies](empty_deps)
            else:
                test_ctx = ctx
                
            result = await suggest_database_update(ctx=test_ctx, **test_case["params"])
            
            has_error = result.get('error') is not None
            task_created = result.get('task_created', False)
            
            if test_case["should_error"]:
                success = has_error and not task_created
                status = "✅ PASS" if success else "❌ FAIL"
                print(f"{status} {test_case['name']}: Error properly handled")
            else:
                success = not has_error and task_created
                status = "✅ PASS" if success else "❌ FAIL" 
                print(f"{status} {test_case['name']}: Success case worked")
                
            error_results.append(success)
            
        except Exception as e:
            # Unexpected exceptions are failures
            print(f"❌ {test_case['name']}: Unexpected exception - {e}")
            error_results.append(False)

    # Test 4: Field Type Validation
    print("\n🧪 Test 4: Field Type Support Validation")
    print("-" * 30)
    
    field_tests = [
        ("TextField", "text value", True),
        ("CharField", "short text", True), 
        ("BooleanField", "true", True),
        ("DateField", "2024-12-25", True),
        ("MoneyField", "100.50", True),
        # Note: Unsupported types should be caught by FieldConverter
    ]
    
    field_results = []
    
    for field_type, test_value, should_work in field_tests:
        try:
            result = await suggest_database_update(
                ctx=ctx,
                model="orders.PurchaseOrder",
                field="vendor_notes",  # This is actually TextField in the model
                type=field_type,
                current_value="original",
                suggested_value=test_value,
                reason="Testing field type support",
                record_identifier="PO-2025-1000"
            )
            
            has_error = result.get('error') is not None
            success = (not has_error) == should_work
            
            status = "✅ PASS" if success else "❌ FAIL"
            print(f"{status} {field_type}: {'Supported' if not has_error else 'Error caught'}")
            
            field_results.append(success)
            
        except Exception as e:
            print(f"❌ {field_type}: Unexpected exception - {e}")
            field_results.append(False)

    # Final Assessment
    print("\n" + "=" * 60)
    print("📊 COMPREHENSIVE TEST RESULTS")
    print("=" * 60)
    
    confidence_score = sum(confidence_results) / len(confidence_results) * 100 if confidence_results else 0
    error_score = sum(error_results) / len(error_results) * 100 if error_results else 0  
    field_score = sum(field_results) / len(field_results) * 100 if field_results else 0
    
    print(f"🧠 Confidence Scoring: {confidence_score:.1f}% ({sum(confidence_results)}/{len(confidence_results)})")
    print(f"🛡️ Error Handling: {error_score:.1f}% ({sum(error_results)}/{len(error_results)})")
    print(f"🔧 Field Type Support: {field_score:.1f}% ({sum(field_results)}/{len(field_results)})")
    
    overall_score = (confidence_score + error_score + field_score) / 3
    print(f"🎯 Overall Test Score: {overall_score:.1f}%")
    
    if overall_score >= 80:
        print("\n🎉 EXCELLENT! Implementation passes comprehensive testing")
        return True
    elif overall_score >= 60:
        print("\n✅ GOOD! Implementation is solid with minor issues") 
        return True
    else:
        print("\n⚠️ NEEDS WORK! Implementation has significant issues")
        return False

if __name__ == "__main__":
    print("Database Tools V1 - Comprehensive Implementation Test")
    print("Testing all aspects of the suggest_database_update implementation")
    print()
    
    try:
        success = asyncio.run(test_suggest_database_update())
        sys.exit(0 if success else 1)
    except Exception as e:
        print(f"\n❌ Test execution failed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)