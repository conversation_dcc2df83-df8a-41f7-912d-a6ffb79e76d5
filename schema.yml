openapi: 3.0.3
info:
  title: ''
  version: 0.0.0
paths:
  /api/_teamkeys/Jt5eNer2AUCbf0PN:
    post:
      operationId: api__teamkeys_Jt5eNer2AUCbf0PN_create
      description: |-
        WARNING: this API could be dangerous.
        We're returning API keys for users other than the user that is authenticated.
        This allows complete account impersonation and takeover if it falls into
        the wrong hands. You have been warned.
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          description: No response body
  /api/activity_log/{id}:
    get:
      operationId: api_activity_log_retrieve
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PurchaseOrderStatusUpdate'
          description: ''
  /api/activity_log/po/{po_pk}:
    get:
      operationId: api_activity_log_po_list
      parameters:
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      - in: path
        name: po_pk
        schema:
          type: integer
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedPurchaseOrderStatusUpdateList'
          description: ''
  /api/addresses:
    get:
      operationId: api_addresses_list
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Address'
          description: ''
    post:
      operationId: api_addresses_create
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Address'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/Address'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/Address'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Address'
          description: ''
  /api/addresses/{id}:
    patch:
      operationId: api_addresses_partial_update
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedAddress'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedAddress'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedAddress'
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Address'
          description: ''
    delete:
      operationId: api_addresses_destroy
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '204':
          description: No response body
  /api/auditlog:
    get:
      operationId: api_auditlog_list
      parameters:
      - name: ordering
        required: false
        in: query
        description: Which field to use when ordering the results.
        schema:
          type: string
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedAuditLogList'
          description: ''
  /api/auditlog/item/{item_uuid}:
    get:
      operationId: api_auditlog_item_retrieve
      parameters:
      - in: path
        name: item_uuid
        schema:
          type: string
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuditLog'
          description: ''
  /api/auditlog/purchaseorder/{purchase_order_pk}:
    get:
      operationId: api_auditlog_purchaseorder_retrieve
      parameters:
      - in: path
        name: purchase_order_pk
        schema:
          type: integer
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuditLog'
          description: ''
  /api/auditlog/v1/{object_content_type_string}/{object_id}:
    get:
      operationId: api_auditlog_v1_retrieve
      parameters:
      - in: path
        name: object_content_type_string
        schema:
          type: string
        required: true
      - in: path
        name: object_id
        schema:
          type: string
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuditLog'
          description: ''
  /api/bounce:
    post:
      operationId: api_bounce_create
      description: |-
        Exchange a bounce token, a short-lived temporary
        token sent in emails for a longer lived authentication
        token
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      - {}
      responses:
        '200':
          description: No response body
  /api/bulk-upload-jobs:
    get:
      operationId: api_bulk_upload_jobs_list
      description: Get all bulk upload jobs for the team
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/BulkUploadJob'
          description: ''
    post:
      operationId: api_bulk_upload_jobs_create
      description: Create a new bulk upload job
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/BulkUploadJob'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/BulkUploadJob'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/BulkUploadJob'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BulkUploadJob'
          description: ''
  /api/bulk-upload-jobs/{id}:
    get:
      operationId: api_bulk_upload_jobs_retrieve
      description: Get job status and details
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BulkUploadJob'
          description: ''
    put:
      operationId: api_bulk_upload_jobs_update
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/BulkUploadJob'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/BulkUploadJob'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/BulkUploadJob'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BulkUploadJob'
          description: ''
    patch:
      operationId: api_bulk_upload_jobs_partial_update
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedBulkUploadJob'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedBulkUploadJob'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedBulkUploadJob'
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BulkUploadJob'
          description: ''
    delete:
      operationId: api_bulk_upload_jobs_destroy
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '204':
          description: No response body
  /api/contacts:
    get:
      operationId: api_contacts_list
      parameters:
      - name: ordering
        required: false
        in: query
        description: Which field to use when ordering the results.
        schema:
          type: string
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedContactWithSupplierList'
          description: ''
    post:
      operationId: api_contacts_create
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ContactWithSupplier'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/ContactWithSupplier'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/ContactWithSupplier'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ContactWithSupplier'
          description: ''
  /api/docs:
    get:
      operationId: api_docs_list
      parameters:
      - name: ordering
        required: false
        in: query
        description: Which field to use when ordering the results.
        schema:
          type: string
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedDocumentList'
          description: ''
    post:
      operationId: api_docs_create
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Document'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/Document'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/Document'
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Document'
          description: ''
  /api/docs/{id}:
    get:
      operationId: api_docs_retrieve
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Document'
          description: ''
  /api/docs/{uuid}:
    get:
      operationId: api_docs_retrieve_2
      description: |-
        Retrieve a document by UUID, including archived documents.

        Overrides default behavior to allow accessing archived documents,
        consistent with other model implementations (suppliers, items, contacts).
      parameters:
      - in: path
        name: uuid
        schema:
          type: string
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Document'
          description: ''
    put:
      operationId: api_docs_update
      description: |-
        Update a document, including archived documents (for unarchiving).

        Overrides default behavior to allow updating archived documents,
        enabling the unarchive workflow via PATCH requests with archived_at: null.
      parameters:
      - in: path
        name: uuid
        schema:
          type: string
        required: true
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Document'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/Document'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/Document'
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Document'
          description: ''
    patch:
      operationId: api_docs_partial_update
      parameters:
      - in: path
        name: uuid
        schema:
          type: string
        required: true
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedDocument'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedDocument'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedDocument'
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Document'
          description: ''
    delete:
      operationId: api_docs_destroy
      description: |-
        Delete a document with support for both soft delete (archive) and hard delete.

        By default, performs soft delete (archiving). Use ?force=true for hard delete.
        Implementation matches patterns used in suppliers and items models.

        Args:
            request: HTTP request object
            *args: Additional positional arguments
            **kwargs: Additional keyword arguments, expects 'uuid'

        Returns:
            Response: 200 for archive operations, 204 for hard delete
      parameters:
      - in: path
        name: uuid
        schema:
          type: string
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '204':
          description: No response body
  /api/docs/{uuid}/bulk_create_items:
    post:
      operationId: api_docs_bulk_create_items_create
      parameters:
      - in: path
        name: uuid
        schema:
          type: string
        required: true
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Document'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/Document'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/Document'
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Document'
          description: ''
  /api/docs/{uuid}/comments:
    get:
      operationId: api_docs_comments_list
      parameters:
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      - in: path
        name: uuid
        schema:
          type: string
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedDocumentCommentList'
          description: ''
    post:
      operationId: api_docs_comments_create
      parameters:
      - in: path
        name: uuid
        schema:
          type: string
        required: true
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DocumentComment'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/DocumentComment'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/DocumentComment'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DocumentComment'
          description: ''
  /api/docs/{uuid}/comments/{id}:
    get:
      operationId: api_docs_comments_retrieve
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      - in: path
        name: uuid
        schema:
          type: string
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DocumentComment'
          description: ''
    put:
      operationId: api_docs_comments_update
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      - in: path
        name: uuid
        schema:
          type: string
        required: true
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DocumentComment'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/DocumentComment'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/DocumentComment'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DocumentComment'
          description: ''
    patch:
      operationId: api_docs_comments_partial_update
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      - in: path
        name: uuid
        schema:
          type: string
        required: true
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedDocumentComment'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedDocumentComment'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedDocumentComment'
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DocumentComment'
          description: ''
    delete:
      operationId: api_docs_comments_destroy
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      - in: path
        name: uuid
        schema:
          type: string
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '204':
          description: No response body
  /api/docs/{uuid}/links:
    get:
      operationId: api_docs_links_list
      parameters:
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      - in: path
        name: uuid
        schema:
          type: string
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedDocumentLinkList'
          description: ''
    post:
      operationId: api_docs_links_create
      parameters:
      - in: path
        name: uuid
        schema:
          type: string
        required: true
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DocumentLink'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/DocumentLink'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/DocumentLink'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DocumentLink'
          description: ''
    delete:
      operationId: api_docs_links_destroy
      parameters:
      - in: path
        name: uuid
        schema:
          type: string
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '204':
          description: No response body
  /api/documents:
    get:
      operationId: api_documents_list
      parameters:
      - name: ordering
        required: false
        in: query
        description: Which field to use when ordering the results.
        schema:
          type: string
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedDocumentList'
          description: ''
    post:
      operationId: api_documents_create
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Document'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/Document'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/Document'
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Document'
          description: ''
  /api/email-threads/{thread_id}:
    get:
      operationId: api_email_threads_retrieve
      parameters:
      - in: path
        name: thread_id
        schema:
          type: string
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/EmailThreadDetail'
          description: ''
    post:
      operationId: api_email_threads_create
      parameters:
      - in: path
        name: thread_id
        schema:
          type: string
        required: true
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/EmailThread'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/EmailThread'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/EmailThread'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/EmailThread'
          description: ''
  /api/emails/connections/:
    get:
      operationId: api_emails_connections_list
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/EmailCredentialNylas'
          description: ''
    post:
      operationId: api_emails_connections_create
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/EmailCredentialNylas'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/EmailCredentialNylas'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/EmailCredentialNylas'
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/EmailCredentialNylas'
          description: ''
  /api/emails/connections/{id}/:
    get:
      operationId: api_emails_connections_retrieve
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/EmailCredentialNylas'
          description: ''
    put:
      operationId: api_emails_connections_update
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/EmailCredentialNylas'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/EmailCredentialNylas'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/EmailCredentialNylas'
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/EmailCredentialNylas'
          description: ''
    patch:
      operationId: api_emails_connections_partial_update
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedEmailCredentialNylas'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedEmailCredentialNylas'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedEmailCredentialNylas'
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/EmailCredentialNylas'
          description: ''
    delete:
      operationId: api_emails_connections_destroy
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '204':
          description: No response body
  /api/emails/drafts/:
    get:
      operationId: api_emails_drafts_list
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Communication'
          description: ''
    post:
      operationId: api_emails_drafts_create
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Communication'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/Communication'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/Communication'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Communication'
          description: ''
  /api/emails/drafts/{id}/:
    get:
      operationId: api_emails_drafts_retrieve
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Communication'
          description: ''
    put:
      operationId: api_emails_drafts_update
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Communication'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/Communication'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/Communication'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Communication'
          description: ''
    patch:
      operationId: api_emails_drafts_partial_update
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedCommunication'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedCommunication'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedCommunication'
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Communication'
          description: ''
    delete:
      operationId: api_emails_drafts_destroy
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '204':
          description: No response body
  /api/emails/drafts/from-template/:
    post:
      operationId: api_emails_drafts_from_template_create
      description: Create an email draft from a template, including all attachments.
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Communication'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/Communication'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/Communication'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Communication'
          description: ''
  /api/emails/nylas/oauth:
    post:
      operationId: api_emails_nylas_oauth_create
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          description: No response body
  /api/emails/send:
    post:
      operationId: api_emails_send_create
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          description: No response body
  /api/emails/suggestions:
    get:
      operationId: api_emails_suggestions_retrieve
      description: |-
        Returns hints for email addresses the user may
        want to send to or include in cc/bcc headers.
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          description: No response body
  /api/emails/template-variables:
    get:
      operationId: api_emails_template_variables_retrieve
      description: Get all available template variables for drag-and-drop template
        builder.
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          description: No response body
  /api/emails/templates/:
    get:
      operationId: api_emails_templates_retrieve
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          description: No response body
  /api/emails/templates/{template_id}:
    get:
      operationId: api_emails_templates_retrieve_2
      description: |-
        Get a specific email template by ID for editing.

        Returns:
        {
            "id": 123,
            "template_name": "PO Confirmation",
            "email_type": "po_confirmation",
            "subject": "PO {{ purchase_order.po_number }} Confirmation",
            "body": "Dear {{ supplier.name }}, your PO is confirmed...",
            "email_to": ["{{ supplier.default_email }}"],
            "email_cc": [],
            "email_bcc": [],
            "team_id": 1,
            "supplier_id": null,
            "created_at": "2024-01-01T00:00:00Z",
            "modified_at": "2024-01-01T00:00:00Z"
        }
      parameters:
      - in: path
        name: template_id
        schema:
          type: integer
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          description: No response body
  /api/emails/templates/{template_id}/delete:
    delete:
      operationId: api_emails_templates_delete_destroy
      description: |-
        Delete an email template.

        Returns:
        {
            "message": "Template deleted successfully"
        }
      parameters:
      - in: path
        name: template_id
        schema:
          type: integer
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '204':
          description: No response body
  /api/emails/templates/{template_id}/update:
    put:
      operationId: api_emails_templates_update_update
      description: |-
        Update an existing email template.

        Expected request body:
        {
            "template_name": "Updated PO Confirmation",
            "email_type": "po_confirmation",
            "subject": "Updated PO {{ purchase_order.po_number }} Confirmation",
            "body": "<p>Dear {{ supplier.name }}, your updated PO...</p>",
            "email_to": ["{{ supplier.default_email }}"],
            "email_cc": [],
            "email_bcc": [],
            "supplier_id": null
        }

        Returns: Same format as CreateEmailTemplateView
      parameters:
      - in: path
        name: template_id
        schema:
          type: integer
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          description: No response body
  /api/emails/templates/create:
    post:
      operationId: api_emails_templates_create_create
      description: |-
        Create a new email template.

        Expected request body:
        {
            "template_name": "PO Confirmation",
            "email_type": "po_confirmation",
            "subject": "PO {{ purchase_order.po_number }} Confirmation",
            "body": "<p>Dear {{ supplier.name }}, your PO is confirmed...</p>",
            "email_to": ["{{ supplier.default_email }}"],
            "email_cc": [],
            "email_bcc": [],
            "supplier_id": null  // optional
        }

        Returns:
        {
            "id": 123,
            "template_name": "PO Confirmation",
            "email_type": "po_confirmation",
            "subject": "PO {{ purchase_order.po_number }} Confirmation",
            "body": "<p>Dear {{ supplier.name }}, your PO is confirmed...</p>",
            "email_to": ["{{ supplier.default_email }}"],
            "email_cc": [],
            "email_bcc": [],
            "team_id": 1,
            "supplier_id": null,
            "created_at": "2024-01-01T00:00:00Z",
            "modified_at": "2024-01-01T00:00:00Z"
        }
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          description: No response body
  /api/emails/templates/render:
    post:
      operationId: api_emails_templates_render_create
      description: |-
        Render an email template with entity data.

        Expected request body:
        {
            "template_id": 123,
            "entities": {"purchase_order": 456, "supplier": 789},
            "additional_context": {"custom_variable": "custom_value"}
        }
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          description: No response body
  /api/emailthreads:
    get:
      operationId: api_emailthreads_list
      parameters:
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedEmailThreadList'
          description: ''
    post:
      operationId: api_emailthreads_create
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/EmailThread'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/EmailThread'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/EmailThread'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/EmailThread'
          description: ''
  /api/emailthreads/{id}:
    get:
      operationId: api_emailthreads_retrieve
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/EmailThreadDetail'
          description: ''
    put:
      operationId: api_emailthreads_update
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/EmailThread'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/EmailThread'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/EmailThread'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/EmailThread'
          description: ''
    patch:
      operationId: api_emailthreads_partial_update
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedEmailThread'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedEmailThread'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedEmailThread'
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/EmailThread'
          description: ''
    delete:
      operationId: api_emailthreads_destroy
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '204':
          description: No response body
  /api/emailthreads/link_po:
    post:
      operationId: api_emailthreads_link_po_create
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/EmailThread'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/EmailThread'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/EmailThread'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/EmailThread'
          description: ''
  /api/erpitems:
    get:
      operationId: api_erpitems_retrieve
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ERPItem'
          description: ''
  /api/erpitems/bulk-import:
    post:
      operationId: api_erpitems_bulk_import_create
      description: |-
        Kick off bulk import for ERP Items.
        We first get the app from the request, then we get the relevant TeamIntegratedAppCredential
        for that app. We then get the relevant TeamIntegratedAppCredential.credential_id. Using that,
        we get all Alloy items for that credential_id. For each of those, we create a Didero Item.
        For each of those, we link / create a corresponding ERPItem.
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ERPItem'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/ERPItem'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/ERPItem'
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ERPItem'
          description: ''
  /api/erpitems/count:
    get:
      operationId: api_erpitems_count_retrieve
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          description: No response body
  /api/imap/:
    get:
      operationId: api_imap_list
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/EmailCredentialNylas'
          description: ''
    post:
      operationId: api_imap_create
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/EmailCredentialNylas'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/EmailCredentialNylas'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/EmailCredentialNylas'
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/EmailCredentialNylas'
          description: ''
  /api/imap/{id}/:
    get:
      operationId: api_imap_retrieve
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/EmailCredentialNylas'
          description: ''
    put:
      operationId: api_imap_update
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/EmailCredentialNylas'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/EmailCredentialNylas'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/EmailCredentialNylas'
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/EmailCredentialNylas'
          description: ''
    patch:
      operationId: api_imap_partial_update
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedEmailCredentialNylas'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedEmailCredentialNylas'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedEmailCredentialNylas'
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/EmailCredentialNylas'
          description: ''
    delete:
      operationId: api_imap_destroy
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '204':
          description: No response body
  /api/insights/item_geo:
    get:
      operationId: api_insights_item_geo_list
      parameters:
      - name: ordering
        required: false
        in: query
        description: Which field to use when ordering the results.
        schema:
          type: string
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedItemGeoInsightList'
          description: ''
  /api/insights/item_geo/{id}:
    get:
      operationId: api_insights_item_geo_retrieve
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ItemGeoInsight'
          description: ''
  /api/insights/item_geo/nearby:
    get:
      operationId: api_insights_item_geo_nearby_retrieve
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ItemGeoInsight'
          description: ''
  /api/integrations/alloy/credentials:
    get:
      operationId: api_integrations_alloy_credentials_retrieve
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          description: No response body
    post:
      operationId: api_integrations_alloy_credentials_create
      description: |-
        Hacky roundabout way of adding an alloy credentialId
        the FE modal only returns a connectionId so we need to map:
        connectionId -> app_name -> credentialId
        TODO: There might be a better way to do this with the embedded iPaaS
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          description: No response body
  /api/integrations/alloy/token:
    get:
      operationId: api_integrations_alloy_token_retrieve
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          description: No response body
  /api/invoices:
    get:
      operationId: api_invoices_list
      description: List invoices with filtering and pagination.
      parameters:
      - name: ordering
        required: false
        in: query
        description: Which field to use when ordering the results.
        schema:
          type: string
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedInvoiceListList'
          description: ''
  /api/invoices/{id}:
    get:
      operationId: api_invoices_retrieve
      description: Get a specific invoice with all details.
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/InvoiceDetail'
          description: ''
  /api/item-fields-display-configs:
    get:
      operationId: api_item_fields_display_configs_retrieve
      description: Get the ItemFieldsDisplayConfigView for the requestor's team
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ItemFieldsDisplayConfig'
          description: ''
    put:
      operationId: api_item_fields_display_configs_update
      description: Update the ItemFieldsDisplayConfigView for the requestor's team
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ItemFieldsDisplayConfig'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/ItemFieldsDisplayConfig'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/ItemFieldsDisplayConfig'
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ItemFieldsDisplayConfig'
          description: ''
  /api/items:
    get:
      operationId: api_items_list
      parameters:
      - name: ordering
        required: false
        in: query
        description: Which field to use when ordering the results.
        schema:
          type: string
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedItemList'
          description: ''
    post:
      operationId: api_items_create
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Item'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/Item'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/Item'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Item'
          description: ''
  /api/items/{item_uuid}/docs:
    get:
      operationId: api_items_docs_list
      parameters:
      - in: path
        name: item_uuid
        schema:
          type: string
        required: true
      - name: ordering
        required: false
        in: query
        description: Which field to use when ordering the results.
        schema:
          type: string
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedDocumentList'
          description: ''
    post:
      operationId: api_items_docs_create
      parameters:
      - in: path
        name: item_uuid
        schema:
          type: string
        required: true
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Document'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/Document'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/Document'
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Document'
          description: ''
  /api/items/{uuid}:
    get:
      operationId: api_items_retrieve
      description: Get a specific item.
      parameters:
      - in: path
        name: uuid
        schema:
          type: string
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Item'
          description: ''
    put:
      operationId: api_items_update
      description: Update an item.
      parameters:
      - in: path
        name: uuid
        schema:
          type: string
        required: true
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Item'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/Item'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/Item'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Item'
          description: ''
    patch:
      operationId: api_items_partial_update
      parameters:
      - in: path
        name: uuid
        schema:
          type: string
        required: true
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedItem'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedItem'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedItem'
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Item'
          description: ''
    delete:
      operationId: api_items_destroy
      parameters:
      - in: path
        name: uuid
        schema:
          type: string
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '204':
          description: No response body
  /api/items/ai-import:
    post:
      operationId: api_items_ai_import_create
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AIItem'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/AIItem'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/AIItem'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AIItem'
          description: ''
  /api/items/count:
    get:
      operationId: api_items_count_retrieve
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          description: No response body
  /api/notifications:
    get:
      operationId: api_notifications_list
      parameters:
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedNotificationList'
          description: ''
  /api/notifications/status:
    post:
      operationId: api_notifications_status_create
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Notification'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/Notification'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/Notification'
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Notification'
          description: ''
  /api/orders:
    get:
      operationId: api_orders_list
      description: Handles the creation and viewing of Purchase orders, including
        PDF generation of the invoice.
      parameters:
      - name: ordering
        required: false
        in: query
        description: Which field to use when ordering the results.
        schema:
          type: string
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedPurchaseOrderList'
          description: ''
    post:
      operationId: api_orders_create
      description: |-
        Create a PurchaseOrder with optional fields.
        This endpoint is hit when a user is creating a new PO from the website.
        The source is always Didero unless specified otherwise in the request.
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PurchaseOrder'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PurchaseOrder'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PurchaseOrder'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PurchaseOrder'
          description: ''
  /api/orders/{id}:
    get:
      operationId: api_orders_retrieve
      description: Get a specific PO.
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PurchaseOrder'
          description: ''
    put:
      operationId: api_orders_update
      description: |-
        Update a PO. Only some fields are allowed to be edited - depending on the
        status of the PO itself.
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PurchaseOrder'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PurchaseOrder'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PurchaseOrder'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PurchaseOrder'
          description: ''
    delete:
      operationId: api_orders_destroy
      description: Handles the creation and viewing of Purchase orders, including
        PDF generation of the invoice.
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '204':
          description: No response body
  /api/orders/{id}/reference_count:
    get:
      operationId: api_orders_reference_count_retrieve
      description: Get the count of Comms, Docs, Contacts, and Comments for the Purchase
        Order.
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PurchaseOrder'
          description: ''
  /api/orders/{po_pk}/items:
    get:
      operationId: api_orders_items_list
      description: ViewSet for creating, retrieving, updating, and deleting OrderItems.
      parameters:
      - in: path
        name: po_pk
        schema:
          type: integer
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/OrderItem'
          description: ''
    post:
      operationId: api_orders_items_create
      description: |-
        Create an OrderItem for a specific PurchaseOrder. The order must include an item_id, and can also include
        quantity and price.
      parameters:
      - in: path
        name: po_pk
        schema:
          type: integer
        required: true
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/OrderItem'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/OrderItem'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/OrderItem'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OrderItem'
          description: ''
  /api/orders/{po_pk}/items/{order_item_pk}:
    get:
      operationId: api_orders_items_retrieve
      description: ViewSet for creating, retrieving, updating, and deleting OrderItems.
      parameters:
      - in: path
        name: order_item_pk
        schema:
          type: integer
        required: true
      - in: path
        name: po_pk
        schema:
          type: integer
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OrderItem'
          description: ''
    put:
      operationId: api_orders_items_update
      description: Update an existing OrderItem with partial data.
      parameters:
      - in: path
        name: order_item_pk
        schema:
          type: integer
        required: true
      - in: path
        name: po_pk
        schema:
          type: integer
        required: true
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/OrderItem'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/OrderItem'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/OrderItem'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OrderItem'
          description: ''
    patch:
      operationId: api_orders_items_partial_update
      description: ViewSet for creating, retrieving, updating, and deleting OrderItems.
      parameters:
      - in: path
        name: order_item_pk
        schema:
          type: integer
        required: true
      - in: path
        name: po_pk
        schema:
          type: integer
        required: true
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedOrderItem'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedOrderItem'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedOrderItem'
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OrderItem'
          description: ''
    delete:
      operationId: api_orders_items_destroy
      description: Delete an OrderItem.
      parameters:
      - in: path
        name: order_item_pk
        schema:
          type: integer
        required: true
      - in: path
        name: po_pk
        schema:
          type: integer
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '204':
          description: No response body
  /api/orders/{po_pk}/items/bulk_delete:
    delete:
      operationId: api_orders_items_bulk_delete_destroy
      description: ViewSet for creating, retrieving, updating, and deleting OrderItems.
      parameters:
      - in: path
        name: po_pk
        schema:
          type: integer
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '204':
          description: No response body
  /api/orders/{purchase_order_id}/action:
    post:
      operationId: api_orders_action_create
      description: |-
        str(object='') -> str
        str(bytes_or_buffer[, encoding[, errors]]) -> str

        Create a new string object from the given object. If encoding or
        errors is specified, then the object must expose a data buffer
        that will be decoded using the given encoding and error handler.
        Otherwise, returns the result of object.__str__() (if defined)
        or repr(object).
        encoding defaults to sys.getdefaultencoding().
        errors defaults to 'strict'.
      parameters:
      - in: path
        name: purchase_order_id
        schema:
          type: integer
        required: true
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PurchaseOrder'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PurchaseOrder'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PurchaseOrder'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PurchaseOrder'
          description: ''
  /api/orders/{purchase_order_id}/comments:
    get:
      operationId: api_orders_comments_list
      parameters:
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      - in: path
        name: purchase_order_id
        schema:
          type: integer
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedPurchaseOrderCommentList'
          description: ''
    post:
      operationId: api_orders_comments_create
      parameters:
      - in: path
        name: purchase_order_id
        schema:
          type: integer
        required: true
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PurchaseOrderComment'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PurchaseOrderComment'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PurchaseOrderComment'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PurchaseOrderComment'
          description: ''
  /api/orders/{purchase_order_id}/comments/{id}:
    get:
      operationId: api_orders_comments_retrieve
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      - in: path
        name: purchase_order_id
        schema:
          type: integer
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PurchaseOrderComment'
          description: ''
    put:
      operationId: api_orders_comments_update
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      - in: path
        name: purchase_order_id
        schema:
          type: integer
        required: true
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PurchaseOrderComment'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PurchaseOrderComment'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PurchaseOrderComment'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PurchaseOrderComment'
          description: ''
    patch:
      operationId: api_orders_comments_partial_update
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      - in: path
        name: purchase_order_id
        schema:
          type: integer
        required: true
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedPurchaseOrderComment'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedPurchaseOrderComment'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedPurchaseOrderComment'
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PurchaseOrderComment'
          description: ''
    delete:
      operationId: api_orders_comments_destroy
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      - in: path
        name: purchase_order_id
        schema:
          type: integer
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '204':
          description: No response body
  /api/orders/{purchase_order_id}/docs:
    get:
      operationId: api_orders_docs_list
      parameters:
      - name: ordering
        required: false
        in: query
        description: Which field to use when ordering the results.
        schema:
          type: string
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      - in: path
        name: purchase_order_id
        schema:
          type: integer
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedDocumentList'
          description: ''
    post:
      operationId: api_orders_docs_create
      parameters:
      - in: path
        name: purchase_order_id
        schema:
          type: integer
        required: true
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Document'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/Document'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/Document'
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Document'
          description: ''
  /api/orders/{purchase_order_id}/docs/{uuid}:
    get:
      operationId: api_orders_docs_retrieve
      description: |-
        Retrieve a document by UUID, including archived documents.

        Overrides default behavior to allow accessing archived documents,
        consistent with other model implementations (suppliers, items, contacts).
      parameters:
      - in: path
        name: purchase_order_id
        schema:
          type: integer
        required: true
      - in: path
        name: uuid
        schema:
          type: string
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Document'
          description: ''
    put:
      operationId: api_orders_docs_update
      description: |-
        Update a document, including archived documents (for unarchiving).

        Overrides default behavior to allow updating archived documents,
        enabling the unarchive workflow via PATCH requests with archived_at: null.
      parameters:
      - in: path
        name: purchase_order_id
        schema:
          type: integer
        required: true
      - in: path
        name: uuid
        schema:
          type: string
        required: true
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Document'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/Document'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/Document'
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Document'
          description: ''
    patch:
      operationId: api_orders_docs_partial_update
      parameters:
      - in: path
        name: purchase_order_id
        schema:
          type: integer
        required: true
      - in: path
        name: uuid
        schema:
          type: string
        required: true
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedDocument'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedDocument'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedDocument'
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Document'
          description: ''
    delete:
      operationId: api_orders_docs_destroy
      description: |-
        Delete a document with support for both soft delete (archive) and hard delete.

        By default, performs soft delete (archiving). Use ?force=true for hard delete.
        Implementation matches patterns used in suppliers and items models.

        Args:
            request: HTTP request object
            *args: Additional positional arguments
            **kwargs: Additional keyword arguments, expects 'uuid'

        Returns:
            Response: 200 for archive operations, 204 for hard delete
      parameters:
      - in: path
        name: purchase_order_id
        schema:
          type: integer
        required: true
      - in: path
        name: uuid
        schema:
          type: string
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '204':
          description: No response body
  /api/orders/{purchase_order_id}/shipments:
    post:
      operationId: api_orders_shipments_create
      description: |-
        Create or replace shipments for a purchase order.

        POST: Takes a list of shipments for the purchase order.
        - Deletes all existing shipments
        - Creates new shipments from the provided data
        - Updates the PO status
      parameters:
      - in: path
        name: purchase_order_id
        schema:
          type: integer
        required: true
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Shipment'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/Shipment'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/Shipment'
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Shipment'
          description: ''
  /api/orders/approval-assignees:
    get:
      operationId: api_orders_approval_assignees_retrieve
      description: Returns available users and groups that can be assigned as approvers
        for purchase orders.
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          description: No response body
  /api/orders/count:
    get:
      operationId: api_orders_count_retrieve
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          description: No response body
  /api/orders/pdf:
    post:
      operationId: api_orders_pdf_create
      description: Handles the creation and viewing of Purchase orders, including
        PDF generation of the invoice.
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PurchaseOrder'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PurchaseOrder'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PurchaseOrder'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PurchaseOrder'
          description: ''
  /api/profile:
    get:
      operationId: api_profile_retrieve
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/User'
          description: ''
    put:
      operationId: api_profile_update
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/User'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/User'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/User'
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/User'
          description: ''
    patch:
      operationId: api_profile_partial_update
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedUser'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedUser'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedUser'
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/User'
          description: ''
  /api/schema/:
    get:
      operationId: api_schema_retrieve
      description: |-
        OpenApi3 schema for this API. Format can be selected via content negotiation.

        - YAML: application/vnd.oai.openapi
        - JSON: application/vnd.oai.openapi+json
      parameters:
      - in: query
        name: format
        schema:
          type: string
          enum:
          - json
          - yaml
      - in: query
        name: lang
        schema:
          type: string
          enum:
          - af
          - ar
          - ar-dz
          - ast
          - az
          - be
          - bg
          - bn
          - br
          - bs
          - ca
          - ckb
          - cs
          - cy
          - da
          - de
          - dsb
          - el
          - en
          - en-au
          - en-gb
          - eo
          - es
          - es-ar
          - es-co
          - es-mx
          - es-ni
          - es-ve
          - et
          - eu
          - fa
          - fi
          - fr
          - fy
          - ga
          - gd
          - gl
          - he
          - hi
          - hr
          - hsb
          - hu
          - hy
          - ia
          - id
          - ig
          - io
          - is
          - it
          - ja
          - ka
          - kab
          - kk
          - km
          - kn
          - ko
          - ky
          - lb
          - lt
          - lv
          - mk
          - ml
          - mn
          - mr
          - ms
          - my
          - nb
          - ne
          - nl
          - nn
          - os
          - pa
          - pl
          - pt
          - pt-br
          - ro
          - ru
          - sk
          - sl
          - sq
          - sr
          - sr-latn
          - sv
          - sw
          - ta
          - te
          - tg
          - th
          - tk
          - tr
          - tt
          - udm
          - uk
          - ur
          - uz
          - vi
          - zh-hans
          - zh-hant
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      - {}
      responses:
        '200':
          content:
            application/vnd.oai.openapi:
              schema:
                type: object
                additionalProperties: {}
            application/yaml:
              schema:
                type: object
                additionalProperties: {}
            application/vnd.oai.openapi+json:
              schema:
                type: object
                additionalProperties: {}
            application/json:
              schema:
                type: object
                additionalProperties: {}
          description: ''
  /api/search:
    get:
      operationId: api_search_retrieve
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          description: No response body
  /api/search/docs:
    get:
      operationId: api_search_docs_retrieve
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          description: No response body
  /api/session:
    get:
      operationId: api_session_retrieve
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          description: No response body
  /api/shipments/{id}:
    patch:
      operationId: api_shipments_partial_update
      description: |-
        Update a single shipment.

        PATCH: Updates specific fields of an existing shipment
        - Updates the specified shipment
        - Updates the PO status
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedShipment'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedShipment'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedShipment'
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Shipment'
          description: ''
  /api/shipping-documents:
    get:
      operationId: api_shipping_documents_list
      description: List shipping documents.
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ShippingDocument'
          description: ''
  /api/shipping-documents/{id}:
    get:
      operationId: api_shipping_documents_retrieve
      description: |-
        Retrieve a single shipping document with document type-specific information.

        Returns the shipping document data along with any document type-specific
        information stored in the extracted_data field.
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ShippingDocument'
          description: ''
  /api/signin:
    post:
      operationId: api_signin_create
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      - {}
      responses:
        '200':
          description: No response body
  /api/sors:
    get:
      operationId: api_sors_list
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/SupplierOnboardingRequirement'
          description: ''
    post:
      operationId: api_sors_create
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SupplierOnboardingRequirement'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/SupplierOnboardingRequirement'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/SupplierOnboardingRequirement'
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SupplierOnboardingRequirement'
          description: ''
  /api/sors/{id}:
    get:
      operationId: api_sors_retrieve
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SupplierOnboardingRequirement'
          description: ''
    put:
      operationId: api_sors_update
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SupplierOnboardingRequirement'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/SupplierOnboardingRequirement'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/SupplierOnboardingRequirement'
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SupplierOnboardingRequirement'
          description: ''
    patch:
      operationId: api_sors_partial_update
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedSupplierOnboardingRequirement'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedSupplierOnboardingRequirement'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedSupplierOnboardingRequirement'
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SupplierOnboardingRequirement'
          description: ''
    delete:
      operationId: api_sors_destroy
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '204':
          description: No response body
  /api/suppliers:
    get:
      operationId: api_suppliers_list
      parameters:
      - name: ordering
        required: false
        in: query
        description: Which field to use when ordering the results.
        schema:
          type: string
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedSupplierList'
          description: ''
    post:
      operationId: api_suppliers_create
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Modify'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/Modify'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/Modify'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Modify'
          description: ''
  /api/suppliers/{id}:
    get:
      operationId: api_suppliers_retrieve
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Supplier'
          description: ''
    put:
      operationId: api_suppliers_update
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Modify'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/Modify'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/Modify'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Modify'
          description: ''
    patch:
      operationId: api_suppliers_partial_update
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedModify'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedModify'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedModify'
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Modify'
          description: ''
    delete:
      operationId: api_suppliers_destroy
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '204':
          description: No response body
  /api/suppliers/{id}/domains:
    post:
      operationId: api_suppliers_domains_create
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Supplier'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/Supplier'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/Supplier'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Supplier'
          description: ''
  /api/suppliers/{id}/email_blocklist:
    post:
      operationId: api_suppliers_email_blocklist_create
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Supplier'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/Supplier'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/Supplier'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Supplier'
          description: ''
  /api/suppliers/{supplier_id}/comments:
    get:
      operationId: api_suppliers_comments_list
      parameters:
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      - in: path
        name: supplier_id
        schema:
          type: integer
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedSupplierCommentList'
          description: ''
    post:
      operationId: api_suppliers_comments_create
      parameters:
      - in: path
        name: supplier_id
        schema:
          type: integer
        required: true
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SupplierComment'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/SupplierComment'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/SupplierComment'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SupplierComment'
          description: ''
  /api/suppliers/{supplier_id}/comments/{id}:
    get:
      operationId: api_suppliers_comments_retrieve
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      - in: path
        name: supplier_id
        schema:
          type: integer
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SupplierComment'
          description: ''
    put:
      operationId: api_suppliers_comments_update
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      - in: path
        name: supplier_id
        schema:
          type: integer
        required: true
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SupplierComment'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/SupplierComment'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/SupplierComment'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SupplierComment'
          description: ''
    patch:
      operationId: api_suppliers_comments_partial_update
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      - in: path
        name: supplier_id
        schema:
          type: integer
        required: true
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedSupplierComment'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedSupplierComment'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedSupplierComment'
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SupplierComment'
          description: ''
    delete:
      operationId: api_suppliers_comments_destroy
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      - in: path
        name: supplier_id
        schema:
          type: integer
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '204':
          description: No response body
  /api/suppliers/{supplier_id}/contacts:
    get:
      operationId: api_suppliers_contacts_list
      parameters:
      - name: ordering
        required: false
        in: query
        description: Which field to use when ordering the results.
        schema:
          type: string
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      - in: path
        name: supplier_id
        schema:
          type: integer
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedContactWithSupplierList'
          description: ''
    post:
      operationId: api_suppliers_contacts_create
      parameters:
      - in: path
        name: supplier_id
        schema:
          type: integer
        required: true
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ContactWithSupplier'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/ContactWithSupplier'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/ContactWithSupplier'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ContactWithSupplier'
          description: ''
  /api/suppliers/{supplier_id}/contacts/{id}:
    get:
      operationId: api_suppliers_contacts_retrieve
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      - in: path
        name: supplier_id
        schema:
          type: integer
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ContactWithSupplier'
          description: ''
    put:
      operationId: api_suppliers_contacts_update
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      - in: path
        name: supplier_id
        schema:
          type: integer
        required: true
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ContactWithSupplier'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/ContactWithSupplier'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/ContactWithSupplier'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ContactWithSupplier'
          description: ''
    patch:
      operationId: api_suppliers_contacts_partial_update
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      - in: path
        name: supplier_id
        schema:
          type: integer
        required: true
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedContactWithSupplier'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedContactWithSupplier'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedContactWithSupplier'
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ContactWithSupplier'
          description: ''
    delete:
      operationId: api_suppliers_contacts_destroy
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      - in: path
        name: supplier_id
        schema:
          type: integer
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '204':
          description: No response body
  /api/suppliers/{supplier_id}/docs:
    get:
      operationId: api_suppliers_docs_list
      parameters:
      - name: ordering
        required: false
        in: query
        description: Which field to use when ordering the results.
        schema:
          type: string
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      - in: path
        name: supplier_id
        schema:
          type: integer
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedDocumentList'
          description: ''
    post:
      operationId: api_suppliers_docs_create
      parameters:
      - in: path
        name: supplier_id
        schema:
          type: integer
        required: true
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Document'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/Document'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/Document'
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Document'
          description: ''
  /api/suppliers/{supplier_id}/docs/{uuid}:
    get:
      operationId: api_suppliers_docs_retrieve
      description: |-
        Retrieve a document by UUID, including archived documents.

        Overrides default behavior to allow accessing archived documents,
        consistent with other model implementations (suppliers, items, contacts).
      parameters:
      - in: path
        name: supplier_id
        schema:
          type: integer
        required: true
      - in: path
        name: uuid
        schema:
          type: string
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Document'
          description: ''
    put:
      operationId: api_suppliers_docs_update
      description: |-
        Update a document, including archived documents (for unarchiving).

        Overrides default behavior to allow updating archived documents,
        enabling the unarchive workflow via PATCH requests with archived_at: null.
      parameters:
      - in: path
        name: supplier_id
        schema:
          type: integer
        required: true
      - in: path
        name: uuid
        schema:
          type: string
        required: true
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Document'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/Document'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/Document'
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Document'
          description: ''
    patch:
      operationId: api_suppliers_docs_partial_update
      parameters:
      - in: path
        name: supplier_id
        schema:
          type: integer
        required: true
      - in: path
        name: uuid
        schema:
          type: string
        required: true
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedDocument'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedDocument'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedDocument'
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Document'
          description: ''
    delete:
      operationId: api_suppliers_docs_destroy
      description: |-
        Delete a document with support for both soft delete (archive) and hard delete.

        By default, performs soft delete (archiving). Use ?force=true for hard delete.
        Implementation matches patterns used in suppliers and items models.

        Args:
            request: HTTP request object
            *args: Additional positional arguments
            **kwargs: Additional keyword arguments, expects 'uuid'

        Returns:
            Response: 200 for archive operations, 204 for hard delete
      parameters:
      - in: path
        name: supplier_id
        schema:
          type: integer
        required: true
      - in: path
        name: uuid
        schema:
          type: string
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '204':
          description: No response body
  /api/suppliers/{supplier_id}/domains:
    post:
      operationId: api_suppliers_domains_create_2
      parameters:
      - in: path
        name: supplier_id
        schema:
          type: integer
        required: true
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Supplier'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/Supplier'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/Supplier'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Supplier'
          description: ''
    put:
      operationId: api_suppliers_domains_update
      parameters:
      - in: path
        name: supplier_id
        schema:
          type: integer
        required: true
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Supplier'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/Supplier'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/Supplier'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Supplier'
          description: ''
    patch:
      operationId: api_suppliers_domains_partial_update
      parameters:
      - in: path
        name: supplier_id
        schema:
          type: integer
        required: true
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedSupplier'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedSupplier'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedSupplier'
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Supplier'
          description: ''
  /api/suppliers/{supplier_id}/email_blocklist:
    post:
      operationId: api_suppliers_email_blocklist_create_2
      parameters:
      - in: path
        name: supplier_id
        schema:
          type: integer
        required: true
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Supplier'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/Supplier'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/Supplier'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Supplier'
          description: ''
  /api/suppliers/erp-suppliers:
    get:
      operationId: api_suppliers_erp_suppliers_retrieve
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          description: No response body
  /api/suppliers/erp-suppliers/count:
    get:
      operationId: api_suppliers_erp_suppliers_count_retrieve
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          description: No response body
  /api/suppliers/export_data:
    get:
      operationId: api_suppliers_export_data_retrieve
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Supplier'
          description: ''
  /api/suppliers/moduleactivate:
    post:
      operationId: api_suppliers_moduleactivate_create
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          description: No response body
  /api/tags:
    get:
      operationId: api_tags_list
      parameters:
      - name: ordering
        required: false
        in: query
        description: Which field to use when ordering the results.
        schema:
          type: string
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedTagList'
          description: ''
    post:
      operationId: api_tags_create
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Tag'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/Tag'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/Tag'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Tag'
          description: ''
  /api/tags/{name}:
    put:
      operationId: api_tags_update
      parameters:
      - in: path
        name: name
        schema:
          type: string
        required: true
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Tag'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/Tag'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/Tag'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Tag'
          description: ''
    delete:
      operationId: api_tags_destroy
      parameters:
      - in: path
        name: name
        schema:
          type: string
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '204':
          description: No response body
  /api/tags/{name}/mentions:
    get:
      operationId: api_tags_mentions_retrieve
      parameters:
      - in: path
        name: name
        schema:
          type: string
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          description: No response body
  /api/tasks:
    get:
      operationId: api_tasks_list
      parameters:
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedTaskList'
          description: ''
    post:
      operationId: api_tasks_create
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Task'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/Task'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/Task'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Task'
          description: ''
  /api/tasks/{id}:
    get:
      operationId: api_tasks_retrieve
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Task'
          description: ''
    put:
      operationId: api_tasks_update
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Task'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/Task'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/Task'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Task'
          description: ''
    patch:
      operationId: api_tasks_partial_update
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedTask'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedTask'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedTask'
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Task'
          description: ''
    delete:
      operationId: api_tasks_destroy
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '204':
          description: No response body
  /api/tasks/action:
    post:
      operationId: api_tasks_action_create
      description: |-
        str(object='') -> str
        str(bytes_or_buffer[, encoding[, errors]]) -> str

        Create a new string object from the given object. If encoding or
        errors is specified, then the object must expose a data buffer
        that will be decoded using the given encoding and error handler.
        Otherwise, returns the result of object.__str__() (if defined)
        or repr(object).
        encoding defaults to sys.getdefaultencoding().
        errors defaults to 'strict'.
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Task'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/Task'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/Task'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Task'
          description: ''
  /api/team:
    put:
      operationId: api_team_update
      description: View for updating team information.
      tags:
      - api
      requestBody:
        content:
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/Team'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/Team'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Team'
          description: ''
    patch:
      operationId: api_team_partial_update
      description: View for updating team information.
      tags:
      - api
      requestBody:
        content:
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedTeam'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedTeam'
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Team'
          description: ''
  /api/team-setting:
    get:
      operationId: api_team_setting_list
      parameters:
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedTeamSettingList'
          description: ''
    post:
      operationId: api_team_setting_create
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TeamSetting'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/TeamSetting'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/TeamSetting'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TeamSetting'
          description: ''
  /api/team-setting/{id}:
    put:
      operationId: api_team_setting_update
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TeamSetting'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/TeamSetting'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/TeamSetting'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TeamSetting'
          description: ''
  /api/user-groups:
    get:
      operationId: api_user_groups_list
      description: A viewset for viewing user groups.
      parameters:
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedUserGroupList'
          description: ''
  /api/user-groups/{id}:
    get:
      operationId: api_user_groups_retrieve
      description: A viewset for viewing user groups.
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserGroup'
          description: ''
  /api/v2/tasks:
    get:
      operationId: api_v2_tasks_list
      parameters:
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedTaskV2List'
          description: ''
    post:
      operationId: api_v2_tasks_create
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TaskV2'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/TaskV2'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/TaskV2'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TaskV2'
          description: ''
  /api/v2/tasks/{id}:
    get:
      operationId: api_v2_tasks_retrieve
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TaskV2'
          description: ''
    put:
      operationId: api_v2_tasks_update
      description: |-
        Update a Task V2 with standard fields or special operations like snoozing.

        For snoozing a task:
        - Set status to "ON_HOLD"
        - Optionally include 'remind_again_in' (hours as integer) to specify snooze duration
        - Or include 'next_reminder_at' (ISO timestamp) for a specific reminder time
        - If neither is provided, defaults to 24 hours

        Example for snoozing a task for 48 hours:
        PATCH /api/v2/tasks/{id}
        {
            "status": "ON_HOLD",
            "remind_again_in": 48
        }
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TaskV2'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/TaskV2'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/TaskV2'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TaskV2'
          description: ''
    patch:
      operationId: api_v2_tasks_partial_update
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedTaskV2'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedTaskV2'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedTaskV2'
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TaskV2'
          description: ''
    delete:
      operationId: api_v2_tasks_destroy
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '204':
          description: No response body
  /api/v2/tasks/{id}/available-assignees:
    get:
      operationId: api_v2_tasks_available_assignees_retrieve
      description: |-
        Get a list of users that this task can be reassigned to.

        Returns all users that belong to the same team as the task.

        Example:
        GET /api/v2/tasks/{id}/available-assignees/
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TaskV2'
          description: ''
  /api/v2/tasks/{id}/reassign:
    post:
      operationId: api_v2_tasks_reassign_create
      description: |-
        Reassign a task to another user or user group.

        Either user_id or group_id must be provided in the request body:
        - If user_id is provided, the task is assigned to that user
        - If group_id is provided, the task is assigned to that group
        - If both are provided, the task is assigned to the user, ignoring the group

        The user/group must belong to the same team as the task.

        Example:
        POST /api/v2/tasks/{id}/reassign/
        {
            "user_id": 123  // ID of the new assignee
        }

        OR

        POST /api/v2/tasks/{id}/reassign/
        {
            "group_id": 456  // ID of the new assignee group
        }
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TaskV2'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/TaskV2'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/TaskV2'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TaskV2'
          description: ''
  /api/v2/tasks/execute-action/:
    post:
      operationId: api_v2_tasks_execute_action_create
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TaskAction'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/TaskAction'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/TaskAction'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TaskAction'
          description: ''
  /api/workflows/userworkflows/:
    get:
      operationId: api_workflows_userworkflows_list
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/UserWorkflow'
          description: ''
    post:
      operationId: api_workflows_userworkflows_create
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UserWorkflow'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/UserWorkflow'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/UserWorkflow'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserWorkflow'
          description: ''
  /api/workflows/userworkflows/{id}/:
    get:
      operationId: api_workflows_userworkflows_retrieve
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this user workflow.
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserWorkflow'
          description: ''
    put:
      operationId: api_workflows_userworkflows_update
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this user workflow.
        required: true
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UserWorkflow'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/UserWorkflow'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/UserWorkflow'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserWorkflow'
          description: ''
    patch:
      operationId: api_workflows_userworkflows_partial_update
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this user workflow.
        required: true
      tags:
      - api
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedUserWorkflow'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedUserWorkflow'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedUserWorkflow'
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserWorkflow'
          description: ''
    delete:
      operationId: api_workflows_userworkflows_destroy
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this user workflow.
        required: true
      tags:
      - api
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '204':
          description: No response body
  /tasks/action/:
    post:
      operationId: tasks_action_create
      description: |-
        str(object='') -> str
        str(bytes_or_buffer[, encoding[, errors]]) -> str

        Create a new string object from the given object. If encoding or
        errors is specified, then the object must expose a data buffer
        that will be decoded using the given encoding and error handler.
        Otherwise, returns the result of object.__str__() (if defined)
        or repr(object).
        encoding defaults to sys.getdefaultencoding().
        errors defaults to 'strict'.
      tags:
      - tasks
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Task'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/Task'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/Task'
        required: true
      security:
      - tokenAuth: []
      - cookieAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Task'
          description: ''
components:
  schemas:
    AIItem:
      type: object
      properties:
        supplier:
          $ref: '#/components/schemas/BasicSupplier'
        item_description:
          type: string
        price:
          type: integer
      required:
      - item_description
      - price
      - supplier
    APICall:
      type: object
      properties:
        url:
          type: string
        type:
          $ref: '#/components/schemas/TypeEnum'
        payload: {}
      required:
      - payload
      - type
      - url
    ActiveModalEnum:
      enum:
      - EMAIL_DIALOG_MODAL
      - ADD_COMMENT_MODAL
      type: string
      description: |-
        * `EMAIL_DIALOG_MODAL` - EMAIL_DIALOG_MODAL
        * `ADD_COMMENT_MODAL` - ADD_COMMENT_MODAL
    Address:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        line_1:
          type: string
          maxLength: 128
        line_2:
          type: string
          nullable: true
          title: Address Line 2
          maxLength: 128
        city:
          type: string
          nullable: true
          maxLength: 64
        state_or_province:
          type: string
          nullable: true
          title: State/Province
          maxLength: 64
        postal_code:
          type: string
          maxLength: 64
        country:
          $ref: '#/components/schemas/ShippingAddressCountryEnum'
        phone_number:
          type: string
          pattern: ^\+?1?\d{7,15}$
          maxLength: 17
        is_default:
          type: boolean
        team:
          type: integer
          nullable: true
        supplier:
          type: integer
          nullable: true
        name:
          type: string
          nullable: true
        latitude:
          type: string
          format: decimal
          pattern: ^-?\d{0,6}(?:\.\d{0,16})?$
          nullable: true
        longitude:
          type: string
          format: decimal
          pattern: ^-?\d{0,6}(?:\.\d{0,16})?$
          nullable: true
        google_place_id:
          type: string
          nullable: true
          description: GoogleMaps place_id for the geocode result
        delivery_instructions:
          type: string
          nullable: true
          description: Special delivery instructions for this address
      required:
      - country
      - id
      - line_1
      - postal_code
    AuditLog:
      type: object
      properties:
        action:
          type: string
          readOnly: true
        actor:
          allOf:
          - $ref: '#/components/schemas/User'
          readOnly: true
        content_type:
          type: string
          readOnly: true
        changes:
          readOnly: true
          nullable: true
          title: Change message
        timestamp:
          type: string
          format: date-time
          readOnly: true
        object_pk:
          type: string
          readOnly: true
        object_id:
          type: integer
          readOnly: true
          nullable: true
      required:
      - action
      - actor
      - changes
      - content_type
      - object_id
      - object_pk
      - timestamp
    BasicDocument:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        name:
          type: string
          maxLength: 256
      required:
      - id
      - name
    BasicPurchaseOrder:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        po_number:
          type: string
          nullable: true
          maxLength: 255
        order_status:
          $ref: '#/components/schemas/OrderStatusEnum'
        supplier_id:
          type: string
          readOnly: true
      required:
      - id
      - supplier_id
    BasicSupplier:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        name:
          type: string
          readOnly: true
        website_url:
          type: string
          format: uri
          readOnly: true
          nullable: true
        archived_at:
          type: string
          format: date-time
          readOnly: true
          nullable: true
        onboarding_status:
          allOf:
          - $ref: '#/components/schemas/OnboardingStatusEnum'
          readOnly: true
      required:
      - archived_at
      - id
      - name
      - onboarding_status
      - website_url
    BasicTaskType:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        name:
          $ref: '#/components/schemas/Name247Enum'
      required:
      - id
      - name
    BlankEnum:
      enum:
      - ''
    BulkUploadJob:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        upload_type:
          $ref: '#/components/schemas/UploadTypeEnum'
        status:
          $ref: '#/components/schemas/BulkUploadJobStatusEnum'
        filename:
          type: string
          maxLength: 255
        total_rows:
          type: integer
          maximum: 2147483647
          minimum: -2147483648
        processed_rows:
          type: integer
          maximum: 2147483647
          minimum: -2147483648
        successful_rows:
          type: integer
          maximum: 2147483647
          minimum: -2147483648
        errors: {}
        progress_percentage:
          type: string
          readOnly: true
        created_at:
          type: string
          format: date-time
        modified_at:
          type: string
          format: date-time
          readOnly: true
      required:
      - filename
      - id
      - modified_at
      - progress_percentage
      - upload_type
    BulkUploadJobStatusEnum:
      enum:
      - pending
      - processing
      - completed
      - failed
      type: string
      description: |-
        * `pending` - Pending
        * `processing` - Processing
        * `completed` - Completed
        * `failed` - Failed
    Button:
      type: object
      properties:
        label:
          type: string
        style:
          $ref: '#/components/schemas/StyleEnum'
        api_calls:
          type: array
          items:
            $ref: '#/components/schemas/APICall'
        active_modal:
          $ref: '#/components/schemas/ActiveModalEnum'
        context:
          type: string
          readOnly: true
      required:
      - api_calls
      - context
      - label
      - style
    CarrierTypeEnum:
      enum:
      - fedex
      - zim
      - one
      - msc
      - cosco
      - evergreen
      - unknown
      type: string
      description: |-
        * `fedex` - FEDEX
        * `zim` - ZIM
        * `one` - ONE
        * `msc` - MSC
        * `cosco` - COSCO
        * `evergreen` - EVERGREEN
        * `unknown` - UNKNOWN
    CategoryEnum:
      enum:
      - tax
      - discount
      - shipping
      - handling
      - fee
      - rebate
      - service
      - material
      - promotion
      - surcharge
      - refund
      - custom
      type: string
      description: |-
        * `tax` - TAX
        * `discount` - DISCOUNT
        * `shipping` - SHIPPING
        * `handling` - HANDLING
        * `fee` - FEE
        * `rebate` - REBATE
        * `service` - SERVICE
        * `material` - MATERIAL
        * `promotion` - PROMOTION
        * `surcharge` - SURCHARGE
        * `refund` - REFUND
        * `custom` - CUSTOM
    CommTypeEnum:
      enum:
      - email
      - phone
      type: string
      description: |-
        * `email` - Email
        * `phone` - Phone
    Communication:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        team:
          type: integer
        contacts:
          type: array
          items:
            $ref: '#/components/schemas/SupplierContact'
          readOnly: true
        comm_type:
          $ref: '#/components/schemas/CommTypeEnum'
        comm_time:
          type: string
          format: date-time
        direction:
          $ref: '#/components/schemas/DirectionEnum'
        documents:
          type: string
          readOnly: true
        email_credential:
          allOf:
          - $ref: '#/components/schemas/EmailCredentialNylas'
          readOnly: true
        email_message_id:
          type: string
          maxLength: 1024
        email_subject:
          type: string
          maxLength: 1024
        email_to:
          type: array
          items:
            $ref: '#/components/schemas/CommunicationEmailRecipient'
        email_from:
          type: string
          format: email
          maxLength: 255
        email_cc:
          type: array
          items:
            $ref: '#/components/schemas/CommunicationEmailRecipient'
        email_bcc:
          type: array
          items:
            $ref: '#/components/schemas/CommunicationEmailRecipient'
        email_thread:
          type: integer
          nullable: true
        response_to:
          type: integer
          nullable: true
        email_excerpt:
          type: string
          readOnly: true
        is_draft:
          type: boolean
        user:
          type: integer
          nullable: true
        supplier:
          type: integer
        supplier_id:
          type: integer
          readOnly: true
        email_content:
          type: string
        email_thread_thread_id:
          type: string
          readOnly: true
      required:
      - comm_type
      - contacts
      - direction
      - documents
      - email_credential
      - email_excerpt
      - email_from
      - email_thread_thread_id
      - id
      - supplier
      - supplier_id
      - team
    CommunicationEmailRecipient:
      type: object
      properties:
        email_address:
          type: string
          format: email
          maxLength: 255
      required:
      - email_address
    ConditionEnum:
      enum:
      - good
      - damaged
      - partial_damage
      type: string
      description: |-
        * `good` - Good Condition
        * `damaged` - Damaged
        * `partial_damage` - Partially Damaged
    ContactWithSupplier:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        last_contact:
          type: string
          format: date-time
          readOnly: true
        created_at:
          type: string
          format: date-time
          readOnly: true
        archived_at:
          type: string
          format: date-time
          readOnly: true
          nullable: true
        supplier:
          allOf:
          - $ref: '#/components/schemas/BasicSupplier'
          readOnly: true
        name:
          type: string
          maxLength: 256
        phone:
          type: string
          nullable: true
          maxLength: 15
        email:
          type: string
          format: email
          nullable: true
          maxLength: 256
        additional_info:
          type: string
      required:
      - archived_at
      - created_at
      - email
      - id
      - last_contact
      - supplier
    DirectionEnum:
      enum:
      - incoming
      - outgoing
      type: string
      description: |-
        * `incoming` - Incoming
        * `outgoing` - Outgoing
    DocTypeEnum:
      enum:
      - comply
      - contract
      - delivery_conf
      - external_po_import
      - goodsrct
      - invoice
      - nda
      - order_ack
      - pickup_sheet
      - shipping_order
      - shipping_document
      - delivery_note
      - despatch_list
      - other
      - payment_conf
      - po
      - price_list
      - processing
      - quote_import
      - unknown
      type: string
      description: |-
        * `comply` - COMPLIANCE
        * `contract` - CONTRACT
        * `delivery_conf` - DELIVERY_CONFIRMATION
        * `external_po_import` - EXTERNAL_PO_IMPORT
        * `goodsrct` - GOODS_RECEIPT
        * `invoice` - INVOICE
        * `nda` - NDA
        * `order_ack` - ORDER_ACKNOWLEDGEMENT
        * `pickup_sheet` - PICKUP_SHEET
        * `shipping_order` - SHIPPING_ORDER
        * `shipping_document` - SHIPPING_DOCUMENT
        * `delivery_note` - DELIVERY_NOTE
        * `despatch_list` - DESPATCH_LIST
        * `other` - OTHER
        * `payment_conf` - PAYMENT_CONFIRMATION
        * `po` - PO
        * `price_list` - PRICE_LIST
        * `processing` - PROCESSING
        * `quote_import` - QUOTE_IMPORT
        * `unknown` - UNKNOWN
    Document:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        uuid:
          type: string
          format: uuid
          readOnly: true
        content_hash:
          type: string
          readOnly: true
          nullable: true
        filesize_bytes:
          type: integer
          readOnly: true
        created_at:
          type: string
          format: date-time
          readOnly: true
        links:
          type: array
          items:
            $ref: '#/components/schemas/DocumentLinkExpanded'
          readOnly: true
        upload_date:
          type: string
          format: date-time
          readOnly: true
        parsed_document_details:
          readOnly: true
        tags:
          type: array
          items:
            type: string
        name:
          type: string
        description:
          type: string
        effective_date:
          type: string
          format: date-time
          nullable: true
        expiration_date:
          type: string
          format: date-time
          nullable: true
        csv_data:
          type: string
        doc_type:
          $ref: '#/components/schemas/DocTypeEnum'
        document:
          type: string
          format: uri
        content_type:
          type: string
          maxLength: 255
        csv_meta:
          type: string
        archived_at:
          type: string
          format: date-time
          nullable: true
      required:
      - content_hash
      - created_at
      - filesize_bytes
      - id
      - links
      - parsed_document_details
      - upload_date
      - uuid
    DocumentComment:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        created_by:
          allOf:
          - $ref: '#/components/schemas/User'
          readOnly: true
        created_at:
          type: string
          format: date-time
          readOnly: true
        comment:
          type: string
      required:
      - comment
      - created_at
      - created_by
      - id
    DocumentLink:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        parent_object_type:
          type: string
        parent_object_id:
          type: string
        document_id:
          type: integer
      required:
      - id
      - parent_object_id
      - parent_object_type
    DocumentLinkExpanded:
      type: object
      properties:
        parent_object_type:
          type: string
        parent_object_id:
          type: string
        parent_object:
          type: string
          readOnly: true
      required:
      - parent_object
      - parent_object_id
      - parent_object_type
    DocumentTypeEnum:
      enum:
      - bill_of_lading
      - packing_list
      - advance_shipping_notice
      - goods_receipt_note
      - delivery_note
      type: string
      description: |-
        * `bill_of_lading` - Bill of Lading
        * `packing_list` - Packing List
        * `advance_shipping_notice` - Advance Shipping Notice
        * `goods_receipt_note` - Goods Receipt Note
        * `delivery_note` - Delivery Note
    ERPItem:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        item:
          type: string
          format: uuid
          readOnly: true
        erp_provider:
          allOf:
          - $ref: '#/components/schemas/ErpProviderEnum'
          readOnly: true
        erp_app:
          allOf:
          - $ref: '#/components/schemas/ErpAppEnum'
          readOnly: true
        ipaas_item_id:
          type: string
          readOnly: true
        remote_item_id:
          type: string
          readOnly: true
      required:
      - erp_app
      - erp_provider
      - id
      - ipaas_item_id
      - item
      - remote_item_id
    ERPSupplier:
      type: object
      properties:
        remote_supplier_id:
          type: string
          maxLength: 64
        ipaas_supplier_id:
          type: string
          maxLength: 64
        erp_provider:
          $ref: '#/components/schemas/ErpProviderEnum'
        erp_app:
          $ref: '#/components/schemas/ErpAppEnum'
      required:
      - erp_app
      - erp_provider
      - ipaas_supplier_id
      - remote_supplier_id
    EmailCredentialNylas:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        user_id:
          type: integer
          nullable: true
          readOnly: true
        email_address:
          type: string
          format: email
          readOnly: true
        provider:
          type: string
          readOnly: true
        status:
          allOf:
          - $ref: '#/components/schemas/EmailCredentialNylasStatusEnum'
          readOnly: true
        expires_at:
          type: string
          format: date-time
          readOnly: true
      required:
      - email_address
      - expires_at
      - id
      - provider
      - status
      - user_id
    EmailCredentialNylasStatusEnum:
      enum:
      - valid
      - invalid
      type: string
      description: |-
        * `valid` - VALID
        * `invalid` - INVALID
    EmailThread:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        thread_id:
          type: string
          maxLength: 255
        most_recent_email:
          type: string
          readOnly: true
        email_count:
          type: string
          readOnly: true
        has_attachment:
          type: string
          readOnly: true
      required:
      - email_count
      - has_attachment
      - id
      - most_recent_email
      - thread_id
    EmailThreadDetail:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        thread_id:
          type: string
          maxLength: 255
        emails:
          type: array
          items:
            $ref: '#/components/schemas/Communication'
      required:
      - emails
      - id
      - thread_id
    ErpAppEnum:
      enum:
      - netsuite
      - business_central
      type: string
      description: |-
        * `netsuite` - NETSUITE
        * `business_central` - BUSINESS_CENTRAL
    ErpProviderEnum:
      enum:
      - alloy
      type: string
      description: '* `alloy` - ALLOY'
    ExecutionStatusEnum:
      enum:
      - PENDING
      - IN_PROGRESS
      - COMPLETED
      - FAILED
      type: string
      description: |-
        * `PENDING` - PENDING
        * `IN_PROGRESS` - IN_PROGRESS
        * `COMPLETED` - COMPLETED
        * `FAILED` - FAILED
    ExecutionTypeEnum:
      enum:
      - server
      - server_async
      - client
      type: string
      description: |-
        * `server` - SERVER
        * `server_async` - SERVER_ASYNC
        * `client` - CLIENT
    InvoiceDetail:
      type: object
      description: Detailed serializer for single invoice view - includes all fields
        and nested data.
      properties:
        id:
          type: integer
          readOnly: true
        invoice_number:
          type: string
          maxLength: 255
        invoice_date:
          type: string
          format: date
        due_date:
          type: string
          format: date
          nullable: true
        payment_terms:
          type: string
          nullable: true
          maxLength: 100
        purchase_order:
          allOf:
          - $ref: '#/components/schemas/BasicPurchaseOrder'
          readOnly: true
        billing_address:
          allOf:
          - $ref: '#/components/schemas/Address'
          readOnly: true
        notes:
          type: string
          nullable: true
        special_instructions:
          type: string
          nullable: true
        items:
          type: array
          items:
            $ref: '#/components/schemas/InvoiceItem'
          readOnly: true
        line_items:
          type: array
          items:
            $ref: '#/components/schemas/InvoiceLineItem'
          readOnly: true
        created_at:
          type: string
          format: date-time
          readOnly: true
        modified_at:
          type: string
          format: date-time
          readOnly: true
        total_amount:
          type: object
          additionalProperties:
            type: string
          description: Calculate total amount including items and line items.
          readOnly: true
        supplier:
          type: object
          additionalProperties:
            oneOf:
            - type: integer
            - type: string
            nullable: true
          nullable: true
          description: Get supplier details from the invoice.
          readOnly: true
        document:
          type: object
          additionalProperties:
            oneOf:
            - type: integer
            - type: string
            nullable: true
          nullable: true
          description: Get invoice document from the model method.
          readOnly: true
      required:
      - billing_address
      - created_at
      - document
      - id
      - invoice_date
      - invoice_number
      - items
      - line_items
      - modified_at
      - purchase_order
      - supplier
      - total_amount
    InvoiceItem:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        item:
          type: string
          format: uuid
          nullable: true
        item_number:
          type: string
          maxLength: 100
        item_description:
          type: string
        quantity:
          type: number
          format: double
        unit_price:
          type: string
          format: decimal
          pattern: ^-?\d{0,6}(?:\.\d{0,8})?$
        unit_price_currency:
          type: string
          readOnly: true
        total_price:
          type: string
          format: decimal
          pattern: ^-?\d{0,6}(?:\.\d{0,8})?$
        total_price_currency:
          type: string
          readOnly: true
      required:
      - id
      - item_description
      - item_number
      - quantity
      - total_price_currency
      - unit_price_currency
    InvoiceLineItem:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        category:
          $ref: '#/components/schemas/CategoryEnum'
        description:
          type: string
          nullable: true
          maxLength: 255
        amount:
          type: string
          format: decimal
          pattern: ^-?\d{0,6}(?:\.\d{0,8})?$
        amount_currency:
          type: string
          readOnly: true
      required:
      - amount_currency
      - id
    InvoiceList:
      type: object
      description: Serializer for invoice list view - includes key fields for display.
      properties:
        id:
          type: integer
          readOnly: true
        invoice_number:
          type: string
          maxLength: 255
        invoice_date:
          type: string
          format: date
        due_date:
          type: string
          format: date
          nullable: true
        payment_terms:
          type: string
          nullable: true
          maxLength: 100
        purchase_order:
          allOf:
          - $ref: '#/components/schemas/BasicPurchaseOrder'
          readOnly: true
        notes:
          type: string
          nullable: true
        special_instructions:
          type: string
          nullable: true
        created_at:
          type: string
          format: date-time
          readOnly: true
        modified_at:
          type: string
          format: date-time
          readOnly: true
        supplier:
          type: object
          additionalProperties:
            oneOf:
            - type: integer
            - type: string
            nullable: true
          nullable: true
          description: Get supplier details from the invoice.
          readOnly: true
        document:
          type: object
          additionalProperties:
            oneOf:
            - type: integer
            - type: string
            nullable: true
          nullable: true
          description: Get invoice document from the model method.
          readOnly: true
      required:
      - created_at
      - document
      - id
      - invoice_date
      - invoice_number
      - modified_at
      - purchase_order
      - supplier
    Item:
      type: object
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        team:
          type: integer
          readOnly: true
        supplier:
          allOf:
          - $ref: '#/components/schemas/BasicSupplier'
          readOnly: true
        tags:
          type: array
          items:
            type: string
        description:
          type: string
        item_number:
          type: string
          maxLength: 30
        price:
          type: string
          format: decimal
          pattern: ^-?\d{0,8}(?:\.\d{0,8})?$
        price_currency:
          type: string
          readOnly: true
        notes:
          type: string
          nullable: true
        item_sku:
          type: string
          nullable: true
          maxLength: 30
        manufacturer_part_number:
          type: string
          nullable: true
          maxLength: 30
        supplier_part_number:
          type: string
          nullable: true
          maxLength: 30
        upc:
          type: string
          nullable: true
          maxLength: 25
        unspsc:
          type: string
          nullable: true
          maxLength: 16
        hs_code:
          type: string
          nullable: true
          maxLength: 20
        color:
          type: string
          nullable: true
          maxLength: 30
        weight_value:
          type: number
          format: double
          nullable: true
        weight_unit:
          nullable: true
          oneOf:
          - $ref: '#/components/schemas/WeightUnitEnum'
          - $ref: '#/components/schemas/BlankEnum'
          - $ref: '#/components/schemas/NullEnum'
        dimensions:
          type: string
          nullable: true
          maxLength: 50
        production_lead_time:
          type: number
          format: double
          nullable: true
        transit_lead_time:
          type: number
          format: double
          nullable: true
        total_lead_time:
          type: number
          format: double
          nullable: true
        unit_of_measure:
          type: string
          maxLength: 30
        units_per_measure:
          type: number
          format: double
        thumbnail:
          type: string
          format: uri
        archived_at:
          type: string
          format: date-time
          nullable: true
      required:
      - description
      - id
      - item_number
      - price_currency
      - supplier
      - team
    ItemFieldsDisplayConfig:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        team:
          type: integer
          readOnly: true
        notes:
          type: boolean
        item_sku:
          type: boolean
        manufacturer_part_number:
          type: boolean
        supplier_part_number:
          type: boolean
        upc:
          type: boolean
        unspsc:
          type: boolean
        hs_code:
          type: boolean
        color:
          type: boolean
        weight_value:
          type: boolean
        weight_unit:
          type: boolean
        dimensions:
          type: boolean
        production_lead_time:
          type: boolean
        transit_lead_time:
          type: boolean
        total_lead_time:
          type: boolean
        unit_of_measure:
          type: boolean
        units_per_measure:
          type: boolean
        thumbnail:
          type: boolean
      required:
      - id
      - team
    ItemGeoInsight:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        team:
          type: integer
          readOnly: true
        item:
          type: string
          format: uuid
          readOnly: true
        supplier:
          type: integer
          readOnly: true
        sender_address:
          type: integer
          readOnly: true
          nullable: true
        shipping_address:
          type: integer
          readOnly: true
          nullable: true
        item_description:
          type: string
          readOnly: true
        item_number:
          type: string
          readOnly: true
        item_hs_code:
          type: string
          readOnly: true
          nullable: true
        supplier_name:
          type: string
          readOnly: true
        sender_address_country:
          allOf:
          - $ref: '#/components/schemas/ShippingAddressCountryEnum'
          readOnly: true
        sender_address_latitude:
          type: string
          format: decimal
          pattern: ^-?\d{0,3}(?:\.\d{0,6})?$
          readOnly: true
          nullable: true
        sender_address_longitude:
          type: string
          format: decimal
          pattern: ^-?\d{0,3}(?:\.\d{0,6})?$
          readOnly: true
          nullable: true
        shipping_address_country:
          allOf:
          - $ref: '#/components/schemas/ShippingAddressCountryEnum'
          readOnly: true
        shipping_address_latitude:
          type: string
          format: decimal
          pattern: ^-?\d{0,3}(?:\.\d{0,6})?$
          readOnly: true
          nullable: true
        shipping_address_longitude:
          type: string
          format: decimal
          pattern: ^-?\d{0,3}(?:\.\d{0,6})?$
          readOnly: true
          nullable: true
        quantity:
          type: number
          format: double
          readOnly: true
        total_cost:
          type: string
          format: decimal
          pattern: ^-?\d{0,12}(?:\.\d{0,2})?$
          readOnly: true
        insight_date:
          type: string
          format: date
          readOnly: true
          description: Date for which the insight was calculated for
        created_at:
          type: string
          format: date-time
          readOnly: true
      required:
      - created_at
      - id
      - insight_date
      - item
      - item_description
      - item_hs_code
      - item_number
      - quantity
      - sender_address
      - sender_address_country
      - sender_address_latitude
      - sender_address_longitude
      - shipping_address
      - shipping_address_country
      - shipping_address_latitude
      - shipping_address_longitude
      - supplier
      - supplier_name
      - team
      - total_cost
    LineItem:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        category:
          $ref: '#/components/schemas/CategoryEnum'
        description:
          type: string
          nullable: true
          maxLength: 255
        amount:
          type: string
          format: decimal
          pattern: ^-?\d{0,12}(?:\.\d{0,2})?$
        amount_currency:
          type: string
          readOnly: true
      required:
      - amount_currency
      - id
    Modify:
      type: object
      properties:
        name:
          type: string
          maxLength: 256
        description:
          type: string
          nullable: true
        website_url:
          type: string
          format: uri
        notes:
          type: string
        archived_at:
          type: string
          format: date-time
          readOnly: true
          nullable: true
        payment_terms:
          type: string
          readOnly: true
        shipping_method:
          type: string
          readOnly: true
        default_contact:
          type: integer
          readOnly: true
          nullable: true
          description: Default contact for this supplier used for general communications
        default_shipping_terms:
          type: string
          readOnly: true
          nullable: true
          description: Default shipping terms for purchase orders with this supplier
        available_shipping_terms:
          type: array
          items:
            type: string
            maxLength: 50
          readOnly: true
          nullable: true
          description: List of shipping terms this supplier supports (including custom
            terms)
        id:
          type: integer
          readOnly: true
        onboarding_status:
          allOf:
          - $ref: '#/components/schemas/OnboardingStatusEnum'
          readOnly: true
        blocked_email_addresses:
          type: array
          items:
            type: string
            format: email
          readOnly: true
        domains:
          type: array
          items:
            type: string
          readOnly: true
        team:
          type: integer
          readOnly: true
        created_at:
          type: string
          format: date-time
          readOnly: true
        modified_at:
          type: string
          format: date-time
          readOnly: true
        contacts:
          type: string
          readOnly: true
        last_contact:
          type: string
          readOnly: true
        erp_suppliers:
          type: array
          items:
            $ref: '#/components/schemas/ERPSupplier'
        supplier_address:
          type: string
          readOnly: true
        tags:
          type: array
          items:
            type: string
        default_contact_detail:
          type: string
          readOnly: true
        default_email:
          type: string
          nullable: true
          description: Get the email address of the default contact, if available.
          readOnly: true
      required:
      - archived_at
      - available_shipping_terms
      - blocked_email_addresses
      - contacts
      - created_at
      - default_contact
      - default_contact_detail
      - default_email
      - default_shipping_terms
      - domains
      - id
      - last_contact
      - modified_at
      - name
      - onboarding_status
      - payment_terms
      - shipping_method
      - supplier_address
      - team
    Name247Enum:
      enum:
      - PO_APPROVAL_REQUEST
      - PO_RECEIVED_CHECK
      - PO_CREATED_FROM_QUOTATION
      - PO_CREATION_CONFIRMATION
      - USER_MENTIONED_IN_COMMENT
      - PO_STATUS_UPDATE_APPROVAL_DENIED
      - PO_STATUS_UPDATE_APPROVED
      - PO_STATUS_UPDATE_SUPPLIER_REJECTED
      - PO_STATUS_UPDATE_SHIPPED
      - PO_STATUS_UPDATE_RECEIVED
      - PO_STATUS_UPDATE_CANCELED
      - PO_SUPPLIER_UNRESPONSIVE
      - SHIP_DATE_CHANGED
      - SUPPLIER_UNRESPONSIVE_TO_DIDERO_CHECKIN
      - IMPORT_ITEMS_FROM_PRICE_LIST
      - SUPPLIER_ONBOARDING_DOCUMENT_EXPIRING
      - SUPPLIER_ONBOARDING_DOCUMENT_EXPIRED
      - DRAFT_EMAIL_SIMPLE_DISMISS
      - DRAFT_EMAIL_OR_COMMENT
      - PO_SHOULD_HAVE_SHIPPED
      - MANUAL_NOTIFICATION_SIMPLE_DISMISS
      - SHIPMENT_WORKFLOW_TRACKING_INFO_NOT_FOUND
      - SHIPMENT_WORKFLOW_NO_TRACKING_NUMBER_IN_SHIPMENT_UPDATE
      - OPS_TASK_SHIPMENT_WORKFLOW_COMPLETED
      - ORDER_ACKNOWLEDGEMENT_WORKFLOW_COMPLETED
      - ORDER_ACKNOWLEDGEMENT_WORKFLOW_SUCCESS_NOTIFICATION
      - PO_CREATION_WORKFLOW_SUCCESS_NOTIFICATION
      - SHIPMENT_WORKFLOW_SUCCESS_NOTIFICATION
      - PICKUP_WORKFLOW_SUCCESS_NOTIFICATION
      - ADDRESS_MISMATCH_CONFIRMATION
      - FREIGHT_CHARGE_CONFIRMATION
      - PRICE_CHANGE_CONFIRMATION
      - PO_CONFIRMATION_FOLLOWUP
      - SHIPPING_DETAILS_FOLLOWUP
      - DELIVERY_DATE_CONFIRMATION
      - PART_NUMBER_ETA_FOLLOWUP
      - ORDER_ACKNOWLEDGEMENT_VALIDATION_ERROR
      - SHIPMENT_WORKFLOW_VALIDATION_ERROR
      - FOLLOW_UP
      - ERP_SYNC_ERROR
      - ERP_SYNC_SUCCESS
      - OA_ERP_SYNC_SUCCESS
      - OA_ERP_SYNC_ERROR
      - DOCUMENT_MATCH_REVIEW
      - DOCUMENT_UPLOAD_REQUEST
      - DATABASE_UPDATE_APPROVAL
      type: string
      description: |-
        * `PO_APPROVAL_REQUEST` - PO_APPROVAL_REQUEST
        * `PO_RECEIVED_CHECK` - PO_RECEIVED_CHECK
        * `PO_CREATED_FROM_QUOTATION` - PO_CREATED_FROM_QUOTATION
        * `PO_CREATION_CONFIRMATION` - PO_CREATION_CONFIRMATION
        * `USER_MENTIONED_IN_COMMENT` - USER_MENTIONED_IN_COMMENT
        * `PO_STATUS_UPDATE_APPROVAL_DENIED` - PO_STATUS_UPDATE_APPROVAL_DENIED
        * `PO_STATUS_UPDATE_APPROVED` - PO_STATUS_UPDATE_APPROVED
        * `PO_STATUS_UPDATE_SUPPLIER_REJECTED` - PO_STATUS_UPDATE_SUPPLIER_REJECTED
        * `PO_STATUS_UPDATE_SHIPPED` - PO_STATUS_UPDATE_SHIPPED
        * `PO_STATUS_UPDATE_RECEIVED` - PO_STATUS_UPDATE_RECEIVED
        * `PO_STATUS_UPDATE_CANCELED` - PO_STATUS_UPDATE_CANCELED
        * `PO_SUPPLIER_UNRESPONSIVE` - PO_SUPPLIER_UNRESPONSIVE
        * `SHIP_DATE_CHANGED` - SHIP_DATE_CHANGED
        * `SUPPLIER_UNRESPONSIVE_TO_DIDERO_CHECKIN` - SUPPLIER_UNRESPONSIVE_TO_DIDERO_CHECKIN
        * `IMPORT_ITEMS_FROM_PRICE_LIST` - IMPORT_ITEMS_FROM_PRICE_LIST
        * `SUPPLIER_ONBOARDING_DOCUMENT_EXPIRING` - SUPPLIER_ONBOARDING_DOCUMENT_EXPIRING
        * `SUPPLIER_ONBOARDING_DOCUMENT_EXPIRED` - SUPPLIER_ONBOARDING_DOCUMENT_EXPIRED
        * `DRAFT_EMAIL_SIMPLE_DISMISS` - DRAFT_EMAIL_SIMPLE_DISMISS
        * `DRAFT_EMAIL_OR_COMMENT` - DRAFT_EMAIL_OR_COMMENT
        * `PO_SHOULD_HAVE_SHIPPED` - PO_SHOULD_HAVE_SHIPPED
        * `MANUAL_NOTIFICATION_SIMPLE_DISMISS` - MANUAL_NOTIFICATION_SIMPLE_DISMISS
        * `SHIPMENT_WORKFLOW_TRACKING_INFO_NOT_FOUND` - SHIPMENT_WORKFLOW_TRACKING_INFO_NOT_FOUND
        * `SHIPMENT_WORKFLOW_NO_TRACKING_NUMBER_IN_SHIPMENT_UPDATE` - SHIPMENT_WORKFLOW_NO_TRACKING_NUMBER_IN_SHIPMENT_UPDATE
        * `OPS_TASK_SHIPMENT_WORKFLOW_COMPLETED` - OPS_TASK_SHIPMENT_WORKFLOW_COMPLETED
        * `ORDER_ACKNOWLEDGEMENT_WORKFLOW_COMPLETED` - ORDER_ACKNOWLEDGEMENT_WORKFLOW_COMPLETED
        * `ORDER_ACKNOWLEDGEMENT_WORKFLOW_SUCCESS_NOTIFICATION` - ORDER_ACKNOWLEDGEMENT_WORKFLOW_SUCCESS_NOTIFICATION
        * `PO_CREATION_WORKFLOW_SUCCESS_NOTIFICATION` - PO_CREATION_WORKFLOW_SUCCESS_NOTIFICATION
        * `SHIPMENT_WORKFLOW_SUCCESS_NOTIFICATION` - SHIPMENT_WORKFLOW_SUCCESS_NOTIFICATION
        * `PICKUP_WORKFLOW_SUCCESS_NOTIFICATION` - PICKUP_WORKFLOW_SUCCESS_NOTIFICATION
        * `ADDRESS_MISMATCH_CONFIRMATION` - ADDRESS_MISMATCH_CONFIRMATION
        * `FREIGHT_CHARGE_CONFIRMATION` - FREIGHT_CHARGE_CONFIRMATION
        * `PRICE_CHANGE_CONFIRMATION` - PRICE_CHANGE_CONFIRMATION
        * `PO_CONFIRMATION_FOLLOWUP` - PO_CONFIRMATION_FOLLOWUP
        * `SHIPPING_DETAILS_FOLLOWUP` - SHIPPING_DETAILS_FOLLOWUP
        * `DELIVERY_DATE_CONFIRMATION` - DELIVERY_DATE_CONFIRMATION
        * `PART_NUMBER_ETA_FOLLOWUP` - PART_NUMBER_ETA_FOLLOWUP
        * `ORDER_ACKNOWLEDGEMENT_VALIDATION_ERROR` - ORDER_ACKNOWLEDGEMENT_VALIDATION_ERROR
        * `SHIPMENT_WORKFLOW_VALIDATION_ERROR` - SHIPMENT_WORKFLOW_VALIDATION_ERROR
        * `FOLLOW_UP` - FOLLOW_UP
        * `ERP_SYNC_ERROR` - ERP_SYNC_ERROR
        * `ERP_SYNC_SUCCESS` - ERP_SYNC_SUCCESS
        * `OA_ERP_SYNC_SUCCESS` - OA_ERP_SYNC_SUCCESS
        * `OA_ERP_SYNC_ERROR` - OA_ERP_SYNC_ERROR
        * `DOCUMENT_MATCH_REVIEW` - DOCUMENT_MATCH_REVIEW
        * `DOCUMENT_UPLOAD_REQUEST` - DOCUMENT_UPLOAD_REQUEST
        * `DATABASE_UPDATE_APPROVAL` - DATABASE_UPDATE_APPROVAL
    NewStatusEnum:
      enum:
      - draft
      - approval_pending
      - approval_denied
      - approved
      - issued
      - pending_acceptance
      - oa_mismatch
      - awaiting_shipment
      - ready_for_pickup
      - supplier_rejected
      - shipment_delayed
      - shipped
      - partially_shipped
      - delivery_delayed
      - received
      - partially_received
      - canceled
      - invoice_matched
      - issue
      - unpaid
      - invoice_received
      - ready_to_pay
      - paid
      - partially_delivered
      - delivered
      type: string
      description: |-
        * `draft` - DRAFT
        * `approval_pending` - APPROVAL_PENDING
        * `approval_denied` - APPROVAL_DENIED
        * `approved` - APPROVED
        * `issued` - ISSUED
        * `pending_acceptance` - PENDING_ACCEPTANCE
        * `oa_mismatch` - OA_MISMATCH
        * `awaiting_shipment` - AWAITING_SHIPMENT
        * `ready_for_pickup` - READY_FOR_PICKUP
        * `supplier_rejected` - SUPPLIER_REJECTED
        * `shipment_delayed` - SHIPMENT_DELAYED
        * `shipped` - SHIPPED
        * `partially_shipped` - PARTIALLY_SHIPPED
        * `delivery_delayed` - DELIVERY_DELAYED
        * `received` - RECEIVED
        * `partially_received` - PARTIALLY_RECEIVED
        * `canceled` - CANCELED
        * `invoice_matched` - INVOICE_MATCHED
        * `issue` - ISSUE
        * `unpaid` - UNPAID
        * `invoice_received` - INVOICE_RECEIVED
        * `ready_to_pay` - READY_TO_PAY
        * `paid` - PAID
        * `partially_delivered` - PARTIALLY_DELIVERED
        * `delivered` - DELIVERED
    Notification:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        created_at:
          type: string
          format: date-time
          readOnly: true
        read_at:
          type: string
          format: date-time
          readOnly: true
          nullable: true
        verb:
          type: string
          readOnly: true
          description: What the actor did (eg, "mentioned you")
        actor:
          allOf:
          - $ref: '#/components/schemas/User'
          readOnly: true
        item:
          type: string
          readOnly: true
        item_type:
          type: string
          readOnly: true
      required:
      - actor
      - created_at
      - id
      - item
      - item_type
      - read_at
      - verb
    NullEnum:
      enum:
      - null
    OaStatusEnum:
      enum:
      - EXTRACTED
      - COMPLETE
      - INCOMPLETE
      - PENDING_CLARIFICATION
      - REJECTED
      type: string
      description: |-
        * `EXTRACTED` - Extracted - data extracted from supplier response
        * `COMPLETE` - Complete - has all needed info
        * `INCOMPLETE` - Incomplete - missing item ship dates
        * `PENDING_CLARIFICATION` - Pending clarification - validation issues detected
        * `REJECTED` - Rejected - supplier cannot fulfill order
    OldStatusEnum:
      enum:
      - draft
      - approval_pending
      - approval_denied
      - approved
      - issued
      - pending_acceptance
      - oa_mismatch
      - awaiting_shipment
      - ready_for_pickup
      - supplier_rejected
      - shipment_delayed
      - shipped
      - partially_shipped
      - delivery_delayed
      - received
      - partially_received
      - canceled
      - invoice_matched
      - issue
      - unpaid
      - invoice_received
      - ready_to_pay
      - paid
      - partially_delivered
      - delivered
      type: string
      description: |-
        * `draft` - DRAFT
        * `approval_pending` - APPROVAL_PENDING
        * `approval_denied` - APPROVAL_DENIED
        * `approved` - APPROVED
        * `issued` - ISSUED
        * `pending_acceptance` - PENDING_ACCEPTANCE
        * `oa_mismatch` - OA_MISMATCH
        * `awaiting_shipment` - AWAITING_SHIPMENT
        * `ready_for_pickup` - READY_FOR_PICKUP
        * `supplier_rejected` - SUPPLIER_REJECTED
        * `shipment_delayed` - SHIPMENT_DELAYED
        * `shipped` - SHIPPED
        * `partially_shipped` - PARTIALLY_SHIPPED
        * `delivery_delayed` - DELIVERY_DELAYED
        * `received` - RECEIVED
        * `partially_received` - PARTIALLY_RECEIVED
        * `canceled` - CANCELED
        * `invoice_matched` - INVOICE_MATCHED
        * `issue` - ISSUE
        * `unpaid` - UNPAID
        * `invoice_received` - INVOICE_RECEIVED
        * `ready_to_pay` - READY_TO_PAY
        * `paid` - PAID
        * `partially_delivered` - PARTIALLY_DELIVERED
        * `delivered` - DELIVERED
    OnboardingStatusEnum:
      enum:
      - inactive
      - pending
      - active
      type: string
      description: |-
        * `inactive` - INACTIVE
        * `pending` - PENDING
        * `active` - ACTIVE
    OrderAcknowledgement:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        order_number:
          type: string
          nullable: true
          maxLength: 255
        oa_status:
          allOf:
          - $ref: '#/components/schemas/OaStatusEnum'
          description: |-
            Status of this order acknowledgment

            * `EXTRACTED` - Extracted - data extracted from supplier response
            * `COMPLETE` - Complete - has all needed info
            * `INCOMPLETE` - Incomplete - missing item ship dates
            * `PENDING_CLARIFICATION` - Pending clarification - validation issues detected
            * `REJECTED` - Rejected - supplier cannot fulfill order
        items:
          type: array
          items:
            $ref: '#/components/schemas/OrderAcknowledgementItem'
          readOnly: true
        created_at:
          type: string
          format: date-time
        modified_at:
          type: string
          format: date-time
          readOnly: true
      required:
      - id
      - items
      - modified_at
    OrderAcknowledgementItem:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        item:
          allOf:
          - $ref: '#/components/schemas/Item'
          readOnly: true
        item_number:
          type: string
          maxLength: 100
        item_description:
          type: string
        quantity:
          type: number
          format: double
        unit_price:
          type: string
          format: decimal
          pattern: ^-?\d{0,6}(?:\.\d{0,8})?$
        unit_price_currency:
          type: string
          readOnly: true
        total_price:
          type: string
          format: decimal
          pattern: ^-?\d{0,6}(?:\.\d{0,8})?$
        total_price_currency:
          type: string
          readOnly: true
        promised_ship_date:
          type: string
          format: date
          nullable: true
          description: Ship date promised by supplier for this specific item
        promised_delivery_date:
          type: string
          format: date
          nullable: true
          description: Delivery date promised by supplier for this specific item
        unit_of_measure:
          type: string
          maxLength: 50
      required:
      - id
      - item
      - item_description
      - item_number
      - quantity
      - total_price_currency
      - unit_of_measure
      - unit_price_currency
    OrderApproval:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        approver:
          allOf:
          - $ref: '#/components/schemas/User'
          readOnly: true
        approver_group:
          allOf:
          - $ref: '#/components/schemas/UserGroup'
          readOnly: true
        requested_at:
          type: string
          format: date-time
          readOnly: true
          nullable: true
        approved_at:
          type: string
          format: date-time
          readOnly: true
          nullable: true
        denied_at:
          type: string
          format: date-time
          readOnly: true
          nullable: true
      required:
      - approved_at
      - approver
      - approver_group
      - denied_at
      - id
      - requested_at
    OrderDocument:
      type: object
      properties:
        uuid:
          type: string
          format: uuid
          readOnly: true
        name:
          type: string
          maxLength: 256
        document:
          type: string
          format: uri
        content_hash:
          type: string
          nullable: true
          maxLength: 64
        filesize_bytes:
          type: integer
          maximum: 2147483647
          minimum: 0
      required:
      - document
      - filesize_bytes
      - name
      - uuid
    OrderItem:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        item:
          allOf:
          - $ref: '#/components/schemas/Item'
          readOnly: true
        item_id:
          type: string
          format: uuid
          writeOnly: true
        quantity:
          type: number
          format: double
        price:
          type: string
          format: decimal
          pattern: ^-?\d{0,8}(?:\.\d{0,8})?$
        price_currency:
          type: string
          readOnly: true
        unit_of_measure:
          type: string
          maxLength: 30
        units_per_measure:
          type: number
          format: double
        requested_date:
          type: string
          format: date
          nullable: true
          description: Interpretation inherits from PurchaseOrder's shipping_terms
      required:
      - id
      - item
      - item_id
      - price_currency
    OrderStatusEnum:
      enum:
      - draft
      - approval_pending
      - approval_denied
      - approved
      - issued
      - pending_acceptance
      - oa_mismatch
      - awaiting_shipment
      - ready_for_pickup
      - supplier_rejected
      - shipment_delayed
      - shipped
      - partially_shipped
      - delivery_delayed
      - received
      - partially_received
      - canceled
      - invoice_matched
      - issue
      type: string
      description: |-
        * `draft` - DRAFT
        * `approval_pending` - APPROVAL_PENDING
        * `approval_denied` - APPROVAL_DENIED
        * `approved` - APPROVED
        * `issued` - ISSUED
        * `pending_acceptance` - PENDING_ACCEPTANCE
        * `oa_mismatch` - OA_MISMATCH
        * `awaiting_shipment` - AWAITING_SHIPMENT
        * `ready_for_pickup` - READY_FOR_PICKUP
        * `supplier_rejected` - SUPPLIER_REJECTED
        * `shipment_delayed` - SHIPMENT_DELAYED
        * `shipped` - SHIPPED
        * `partially_shipped` - PARTIALLY_SHIPPED
        * `delivery_delayed` - DELIVERY_DELAYED
        * `received` - RECEIVED
        * `partially_received` - PARTIALLY_RECEIVED
        * `canceled` - CANCELED
        * `invoice_matched` - INVOICE_MATCHED
        * `issue` - ISSUE
    PaginatedAuditLogList:
      type: object
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=2
        results:
          type: array
          items:
            $ref: '#/components/schemas/AuditLog'
    PaginatedContactWithSupplierList:
      type: object
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=2
        results:
          type: array
          items:
            $ref: '#/components/schemas/ContactWithSupplier'
    PaginatedDocumentCommentList:
      type: object
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=2
        results:
          type: array
          items:
            $ref: '#/components/schemas/DocumentComment'
    PaginatedDocumentLinkList:
      type: object
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=2
        results:
          type: array
          items:
            $ref: '#/components/schemas/DocumentLink'
    PaginatedDocumentList:
      type: object
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=2
        results:
          type: array
          items:
            $ref: '#/components/schemas/Document'
    PaginatedEmailThreadList:
      type: object
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=2
        results:
          type: array
          items:
            $ref: '#/components/schemas/EmailThread'
    PaginatedInvoiceListList:
      type: object
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=2
        results:
          type: array
          items:
            $ref: '#/components/schemas/InvoiceList'
    PaginatedItemGeoInsightList:
      type: object
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=2
        results:
          type: array
          items:
            $ref: '#/components/schemas/ItemGeoInsight'
    PaginatedItemList:
      type: object
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=2
        results:
          type: array
          items:
            $ref: '#/components/schemas/Item'
    PaginatedNotificationList:
      type: object
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=2
        results:
          type: array
          items:
            $ref: '#/components/schemas/Notification'
    PaginatedPurchaseOrderCommentList:
      type: object
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=2
        results:
          type: array
          items:
            $ref: '#/components/schemas/PurchaseOrderComment'
    PaginatedPurchaseOrderList:
      type: object
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=2
        results:
          type: array
          items:
            $ref: '#/components/schemas/PurchaseOrder'
    PaginatedPurchaseOrderStatusUpdateList:
      type: object
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=2
        results:
          type: array
          items:
            $ref: '#/components/schemas/PurchaseOrderStatusUpdate'
    PaginatedSupplierCommentList:
      type: object
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=2
        results:
          type: array
          items:
            $ref: '#/components/schemas/SupplierComment'
    PaginatedSupplierList:
      type: object
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=2
        results:
          type: array
          items:
            $ref: '#/components/schemas/Supplier'
    PaginatedTagList:
      type: object
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=2
        results:
          type: array
          items:
            $ref: '#/components/schemas/Tag'
    PaginatedTaskList:
      type: object
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=2
        results:
          type: array
          items:
            $ref: '#/components/schemas/Task'
    PaginatedTaskV2List:
      type: object
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=2
        results:
          type: array
          items:
            $ref: '#/components/schemas/TaskV2'
    PaginatedTeamSettingList:
      type: object
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=2
        results:
          type: array
          items:
            $ref: '#/components/schemas/TeamSetting'
    PaginatedUserGroupList:
      type: object
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=2
        results:
          type: array
          items:
            $ref: '#/components/schemas/UserGroup'
    PatchedAddress:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        line_1:
          type: string
          maxLength: 128
        line_2:
          type: string
          nullable: true
          title: Address Line 2
          maxLength: 128
        city:
          type: string
          nullable: true
          maxLength: 64
        state_or_province:
          type: string
          nullable: true
          title: State/Province
          maxLength: 64
        postal_code:
          type: string
          maxLength: 64
        country:
          $ref: '#/components/schemas/ShippingAddressCountryEnum'
        phone_number:
          type: string
          pattern: ^\+?1?\d{7,15}$
          maxLength: 17
        is_default:
          type: boolean
        team:
          type: integer
          nullable: true
        supplier:
          type: integer
          nullable: true
        name:
          type: string
          nullable: true
        latitude:
          type: string
          format: decimal
          pattern: ^-?\d{0,6}(?:\.\d{0,16})?$
          nullable: true
        longitude:
          type: string
          format: decimal
          pattern: ^-?\d{0,6}(?:\.\d{0,16})?$
          nullable: true
        google_place_id:
          type: string
          nullable: true
          description: GoogleMaps place_id for the geocode result
        delivery_instructions:
          type: string
          nullable: true
          description: Special delivery instructions for this address
    PatchedBulkUploadJob:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        upload_type:
          $ref: '#/components/schemas/UploadTypeEnum'
        status:
          $ref: '#/components/schemas/BulkUploadJobStatusEnum'
        filename:
          type: string
          maxLength: 255
        total_rows:
          type: integer
          maximum: 2147483647
          minimum: -2147483648
        processed_rows:
          type: integer
          maximum: 2147483647
          minimum: -2147483648
        successful_rows:
          type: integer
          maximum: 2147483647
          minimum: -2147483648
        errors: {}
        progress_percentage:
          type: string
          readOnly: true
        created_at:
          type: string
          format: date-time
        modified_at:
          type: string
          format: date-time
          readOnly: true
    PatchedCommunication:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        team:
          type: integer
        contacts:
          type: array
          items:
            $ref: '#/components/schemas/SupplierContact'
          readOnly: true
        comm_type:
          $ref: '#/components/schemas/CommTypeEnum'
        comm_time:
          type: string
          format: date-time
        direction:
          $ref: '#/components/schemas/DirectionEnum'
        documents:
          type: string
          readOnly: true
        email_credential:
          allOf:
          - $ref: '#/components/schemas/EmailCredentialNylas'
          readOnly: true
        email_message_id:
          type: string
          maxLength: 1024
        email_subject:
          type: string
          maxLength: 1024
        email_to:
          type: array
          items:
            $ref: '#/components/schemas/CommunicationEmailRecipient'
        email_from:
          type: string
          format: email
          maxLength: 255
        email_cc:
          type: array
          items:
            $ref: '#/components/schemas/CommunicationEmailRecipient'
        email_bcc:
          type: array
          items:
            $ref: '#/components/schemas/CommunicationEmailRecipient'
        email_thread:
          type: integer
          nullable: true
        response_to:
          type: integer
          nullable: true
        email_excerpt:
          type: string
          readOnly: true
        is_draft:
          type: boolean
        user:
          type: integer
          nullable: true
        supplier:
          type: integer
        supplier_id:
          type: integer
          readOnly: true
        email_content:
          type: string
        email_thread_thread_id:
          type: string
          readOnly: true
    PatchedContactWithSupplier:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        last_contact:
          type: string
          format: date-time
          readOnly: true
        created_at:
          type: string
          format: date-time
          readOnly: true
        archived_at:
          type: string
          format: date-time
          readOnly: true
          nullable: true
        supplier:
          allOf:
          - $ref: '#/components/schemas/BasicSupplier'
          readOnly: true
        name:
          type: string
          maxLength: 256
        phone:
          type: string
          nullable: true
          maxLength: 15
        email:
          type: string
          format: email
          nullable: true
          maxLength: 256
        additional_info:
          type: string
    PatchedDocument:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        uuid:
          type: string
          format: uuid
          readOnly: true
        content_hash:
          type: string
          readOnly: true
          nullable: true
        filesize_bytes:
          type: integer
          readOnly: true
        created_at:
          type: string
          format: date-time
          readOnly: true
        links:
          type: array
          items:
            $ref: '#/components/schemas/DocumentLinkExpanded'
          readOnly: true
        upload_date:
          type: string
          format: date-time
          readOnly: true
        parsed_document_details:
          readOnly: true
        tags:
          type: array
          items:
            type: string
        name:
          type: string
        description:
          type: string
        effective_date:
          type: string
          format: date-time
          nullable: true
        expiration_date:
          type: string
          format: date-time
          nullable: true
        csv_data:
          type: string
        doc_type:
          $ref: '#/components/schemas/DocTypeEnum'
        document:
          type: string
          format: uri
        content_type:
          type: string
          maxLength: 255
        csv_meta:
          type: string
        archived_at:
          type: string
          format: date-time
          nullable: true
    PatchedDocumentComment:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        created_by:
          allOf:
          - $ref: '#/components/schemas/User'
          readOnly: true
        created_at:
          type: string
          format: date-time
          readOnly: true
        comment:
          type: string
    PatchedEmailCredentialNylas:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        user_id:
          type: integer
          nullable: true
          readOnly: true
        email_address:
          type: string
          format: email
          readOnly: true
        provider:
          type: string
          readOnly: true
        status:
          allOf:
          - $ref: '#/components/schemas/EmailCredentialNylasStatusEnum'
          readOnly: true
        expires_at:
          type: string
          format: date-time
          readOnly: true
    PatchedEmailThread:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        thread_id:
          type: string
          maxLength: 255
        most_recent_email:
          type: string
          readOnly: true
        email_count:
          type: string
          readOnly: true
        has_attachment:
          type: string
          readOnly: true
    PatchedItem:
      type: object
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        team:
          type: integer
          readOnly: true
        supplier:
          allOf:
          - $ref: '#/components/schemas/BasicSupplier'
          readOnly: true
        tags:
          type: array
          items:
            type: string
        description:
          type: string
        item_number:
          type: string
          maxLength: 30
        price:
          type: string
          format: decimal
          pattern: ^-?\d{0,8}(?:\.\d{0,8})?$
        price_currency:
          type: string
          readOnly: true
        notes:
          type: string
          nullable: true
        item_sku:
          type: string
          nullable: true
          maxLength: 30
        manufacturer_part_number:
          type: string
          nullable: true
          maxLength: 30
        supplier_part_number:
          type: string
          nullable: true
          maxLength: 30
        upc:
          type: string
          nullable: true
          maxLength: 25
        unspsc:
          type: string
          nullable: true
          maxLength: 16
        hs_code:
          type: string
          nullable: true
          maxLength: 20
        color:
          type: string
          nullable: true
          maxLength: 30
        weight_value:
          type: number
          format: double
          nullable: true
        weight_unit:
          nullable: true
          oneOf:
          - $ref: '#/components/schemas/WeightUnitEnum'
          - $ref: '#/components/schemas/BlankEnum'
          - $ref: '#/components/schemas/NullEnum'
        dimensions:
          type: string
          nullable: true
          maxLength: 50
        production_lead_time:
          type: number
          format: double
          nullable: true
        transit_lead_time:
          type: number
          format: double
          nullable: true
        total_lead_time:
          type: number
          format: double
          nullable: true
        unit_of_measure:
          type: string
          maxLength: 30
        units_per_measure:
          type: number
          format: double
        thumbnail:
          type: string
          format: uri
        archived_at:
          type: string
          format: date-time
          nullable: true
    PatchedModify:
      type: object
      properties:
        name:
          type: string
          maxLength: 256
        description:
          type: string
          nullable: true
        website_url:
          type: string
          format: uri
        notes:
          type: string
        archived_at:
          type: string
          format: date-time
          readOnly: true
          nullable: true
        payment_terms:
          type: string
          readOnly: true
        shipping_method:
          type: string
          readOnly: true
        default_contact:
          type: integer
          readOnly: true
          nullable: true
          description: Default contact for this supplier used for general communications
        default_shipping_terms:
          type: string
          readOnly: true
          nullable: true
          description: Default shipping terms for purchase orders with this supplier
        available_shipping_terms:
          type: array
          items:
            type: string
            maxLength: 50
          readOnly: true
          nullable: true
          description: List of shipping terms this supplier supports (including custom
            terms)
        id:
          type: integer
          readOnly: true
        onboarding_status:
          allOf:
          - $ref: '#/components/schemas/OnboardingStatusEnum'
          readOnly: true
        blocked_email_addresses:
          type: array
          items:
            type: string
            format: email
          readOnly: true
        domains:
          type: array
          items:
            type: string
          readOnly: true
        team:
          type: integer
          readOnly: true
        created_at:
          type: string
          format: date-time
          readOnly: true
        modified_at:
          type: string
          format: date-time
          readOnly: true
        contacts:
          type: string
          readOnly: true
        last_contact:
          type: string
          readOnly: true
        erp_suppliers:
          type: array
          items:
            $ref: '#/components/schemas/ERPSupplier'
        supplier_address:
          type: string
          readOnly: true
        tags:
          type: array
          items:
            type: string
        default_contact_detail:
          type: string
          readOnly: true
        default_email:
          type: string
          nullable: true
          description: Get the email address of the default contact, if available.
          readOnly: true
    PatchedOrderItem:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        item:
          allOf:
          - $ref: '#/components/schemas/Item'
          readOnly: true
        item_id:
          type: string
          format: uuid
          writeOnly: true
        quantity:
          type: number
          format: double
        price:
          type: string
          format: decimal
          pattern: ^-?\d{0,8}(?:\.\d{0,8})?$
        price_currency:
          type: string
          readOnly: true
        unit_of_measure:
          type: string
          maxLength: 30
        units_per_measure:
          type: number
          format: double
        requested_date:
          type: string
          format: date
          nullable: true
          description: Interpretation inherits from PurchaseOrder's shipping_terms
    PatchedPurchaseOrderComment:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        created_by:
          allOf:
          - $ref: '#/components/schemas/User'
          readOnly: true
        created_at:
          type: string
          format: date-time
          readOnly: true
        comment:
          type: string
    PatchedShipment:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        purchase_order_id:
          type: integer
          writeOnly: true
          nullable: true
        carrier:
          type: string
          nullable: true
        carrier_type:
          nullable: true
          oneOf:
          - $ref: '#/components/schemas/CarrierTypeEnum'
          - $ref: '#/components/schemas/BlankEnum'
          - $ref: '#/components/schemas/NullEnum'
        tracking_number:
          type: string
          nullable: true
        shipment_date:
          type: string
          format: date
          nullable: true
        estimated_delivery_date:
          type: string
          format: date
          nullable: true
        actual_delivery_date:
          type: string
          format: date
          nullable: true
        line_items:
          type: array
          items:
            $ref: '#/components/schemas/ShipmentLineItemInner'
        status:
          $ref: '#/components/schemas/ShipmentStatusEnum'
    PatchedSupplier:
      type: object
      properties:
        name:
          type: string
          maxLength: 256
        description:
          type: string
          nullable: true
        website_url:
          type: string
          format: uri
          nullable: true
          maxLength: 200
        notes:
          type: string
        archived_at:
          type: string
          format: date-time
          nullable: true
        payment_terms:
          type: string
        shipping_method:
          type: string
        default_contact:
          type: integer
          nullable: true
          description: Default contact for this supplier used for general communications
        default_shipping_terms:
          type: string
          nullable: true
          description: Default shipping terms for purchase orders with this supplier
          maxLength: 50
        available_shipping_terms:
          type: array
          items:
            type: string
            maxLength: 50
          nullable: true
          description: List of shipping terms this supplier supports (including custom
            terms)
        id:
          type: integer
          readOnly: true
        onboarding_status:
          allOf:
          - $ref: '#/components/schemas/OnboardingStatusEnum'
          readOnly: true
        blocked_email_addresses:
          type: array
          items:
            type: string
            format: email
          readOnly: true
        domains:
          type: array
          items:
            type: string
          readOnly: true
        team:
          type: integer
          readOnly: true
        created_at:
          type: string
          format: date-time
          readOnly: true
        modified_at:
          type: string
          format: date-time
          readOnly: true
        contacts:
          type: string
          readOnly: true
        last_contact:
          type: string
          readOnly: true
        erp_suppliers:
          type: array
          items:
            $ref: '#/components/schemas/ERPSupplier'
        supplier_address:
          type: string
          readOnly: true
        tags:
          type: array
          items:
            type: string
        default_contact_detail:
          type: string
          readOnly: true
        default_email:
          type: string
          nullable: true
          description: Get the email address of the default contact, if available.
          readOnly: true
    PatchedSupplierComment:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        created_by:
          allOf:
          - $ref: '#/components/schemas/User'
          readOnly: true
        created_at:
          type: string
          format: date-time
          readOnly: true
        supplier_id:
          type: integer
          readOnly: true
        comment:
          type: string
    PatchedSupplierOnboardingRequirement:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        supplier_id:
          type: integer
          readOnly: true
        supplier_name:
          type: string
          readOnly: true
        req_type:
          allOf:
          - $ref: '#/components/schemas/ReqTypeEnum'
          readOnly: true
        req_document_type:
          readOnly: true
          nullable: true
          oneOf:
          - $ref: '#/components/schemas/ReqDocumentTypeEnum'
          - $ref: '#/components/schemas/NullEnum'
        req_document_label:
          type: string
          readOnly: true
        req_document_doc:
          type: string
          readOnly: true
        req_information_name:
          type: string
          readOnly: true
        req_checkbox_name:
          type: string
          readOnly: true
          maxLength: 255
        status:
          type: string
          readOnly: true
        req_information_value:
          type: string
          maxLength: 255
        req_checkbox_value:
          type: boolean
    PatchedTask:
      type: object
      properties:
        user:
          allOf:
          - $ref: '#/components/schemas/User'
          readOnly: true
        user_group:
          type: integer
          nullable: true
        task_type:
          $ref: '#/components/schemas/BasicTaskType'
        task_config:
          $ref: '#/components/schemas/TaskConfig'
        status:
          $ref: '#/components/schemas/Status826Enum'
        model_type:
          type: string
          readOnly: true
        model_id:
          type: string
          maxLength: 255
        next_reminder_at:
          type: string
          format: date-time
          nullable: true
        id:
          type: integer
          readOnly: true
        created_at:
          type: string
          format: date-time
          readOnly: true
        modified_at:
          type: string
          format: date-time
          readOnly: true
    PatchedTaskV2:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        task_type_v2:
          $ref: '#/components/schemas/TaskTypeV2'
        task_config:
          $ref: '#/components/schemas/TaskConfigV2'
        model_type:
          type: string
          readOnly: true
        model_id:
          type: string
          maxLength: 255
        model_details:
          type: string
          readOnly: true
        status:
          $ref: '#/components/schemas/Status826Enum'
        next_reminder_at:
          type: string
          format: date-time
          nullable: true
        actions:
          type: string
          readOnly: true
        created_at:
          type: string
          format: date-time
        seconds_since_creation:
          type: string
          readOnly: true
        user:
          allOf:
          - $ref: '#/components/schemas/User'
          readOnly: true
        user_group:
          allOf:
          - $ref: '#/components/schemas/UserGroup'
          readOnly: true
    PatchedTeam:
      type: object
      properties:
        uuid:
          type: string
          format: uuid
          readOnly: true
        id:
          type: integer
          readOnly: true
        inbound_mailing_address:
          type: string
          format: email
          readOnly: true
        members:
          type: string
          readOnly: true
        integration_providers:
          type: array
          items:
            $ref: '#/components/schemas/TeamIntegrationProvider'
        default_address:
          type: string
          readOnly: true
        is_demo_team:
          type: boolean
          readOnly: true
        name:
          type: string
          maxLength: 32
        default_po_email_credential:
          type: integer
          nullable: true
        default_requestor:
          $ref: '#/components/schemas/User'
        logo:
          type: string
          format: uri
          nullable: true
        terms_and_conditions:
          type: string
          format: uri
          nullable: true
        legal_disclaimer:
          type: string
          nullable: true
          description: Legal disclaimer text to appear at the bottom of PO PDFs
          maxLength: 1000
    PatchedUser:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        email:
          type: string
          format: email
          readOnly: true
        uuid:
          type: string
          format: uuid
          readOnly: true
        first_name:
          type: string
          readOnly: true
        last_name:
          type: string
          readOnly: true
        phone:
          type: string
          readOnly: true
        display_name:
          type: string
          readOnly: true
    PatchedUserWorkflow:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        workflow_type:
          $ref: '#/components/schemas/WorkflowTypeEnum'
        trigger:
          description: |-
            DEPRECATED: This field is redundant as each workflow_type has a 1:1 mapping with a trigger. Will be removed in future versions.

            * `` - NULL_TRIGGER
            * `on_purchase_order_created` - ON_PURCHASE_ORDER_CREATED
            * `on_order_shipped_email_received` - ON_ORDER_SHIPPED_EMAIL_RECEIVED
            * `on_send_purchase_order_to_supplier` - ON_SEND_PURCHASE_ORDER_TO_SUPPLIER
            * `on_purchase_order_acknowledgement_email_received` - ON_PURCHASE_ORDER_ACKNOWLEDGEMENT_EMAIL_RECEIVED
            * `on_purchase_order_email_received` - ON_PURCHASE_ORDER_EMAIL_RECEIVED
            * `on_purchase_order_follow_up` - ON_PURCHASE_ORDER_FOLLOW_UP
            * `on_invoice_email_received` - ON_INVOICE_EMAIL_RECEIVED
            * `on_shipping_document_email_received` - ON_SHIPPING_DOCUMENT_EMAIL_RECEIVED
          oneOf:
          - $ref: '#/components/schemas/TriggerEnum'
          - $ref: '#/components/schemas/BlankEnum'
        team:
          type: integer
        current_snapshot:
          allOf:
          - $ref: '#/components/schemas/WorkflowSnapshot'
          readOnly: true
    PaymentStatusEnum:
      enum:
      - unpaid
      - invoice_received
      - ready_to_pay
      - paid
      type: string
      description: |-
        * `unpaid` - UNPAID
        * `invoice_received` - INVOICE_RECEIVED
        * `ready_to_pay` - READY_TO_PAY
        * `paid` - PAID
    ProviderEnum:
      enum:
      - alloy
      type: string
      description: '* `alloy` - ALLOY'
    PurchaseOrder:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        team:
          type: integer
        supplier:
          allOf:
          - $ref: '#/components/schemas/BasicSupplier'
          readOnly: true
        supplier_id:
          type: integer
          writeOnly: true
          nullable: true
        po_number:
          type: string
          maxLength: 255
        order_status:
          $ref: '#/components/schemas/OrderStatusEnum'
        payment_status:
          $ref: '#/components/schemas/PaymentStatusEnum'
        requested_date:
          type: string
          format: date
          nullable: true
        shipping_terms:
          type: string
          nullable: true
          description: Shipping terms (supports both standard Incoterms and custom
            terms)
          maxLength: 50
        date_field_label:
          type: string
          readOnly: true
        date_help_text:
          type: string
          readOnly: true
        items:
          type: array
          items:
            $ref: '#/components/schemas/OrderItem'
        line_items:
          type: array
          items:
            $ref: '#/components/schemas/LineItem'
        source:
          $ref: '#/components/schemas/SourceEnum'
        placed_by_id:
          type: integer
        placed_by:
          allOf:
          - $ref: '#/components/schemas/User'
          readOnly: true
        external_requestor_email:
          type: string
          format: email
          readOnly: true
        requestor_email:
          type: string
          readOnly: true
        requestor_display_name:
          type: string
          readOnly: true
        approvals:
          type: array
          items:
            $ref: '#/components/schemas/OrderApproval'
        internal_notes:
          type: string
          nullable: true
        vendor_notes:
          type: string
          nullable: true
        payment_terms:
          type: string
          nullable: true
        shipping_method:
          type: string
          nullable: true
        placement_time:
          type: string
          format: date-time
        tags:
          type: array
          items:
            type: string
        archived_at:
          type: string
          format: date-time
          nullable: true
        sender_address:
          type: string
        shipping_address:
          type: string
        total_cost:
          type: string
          format: decimal
          pattern: ^-?\d{0,12}(?:\.\d{0,2})?$
        total_cost_currency:
          type: string
          readOnly: true
        document:
          allOf:
          - $ref: '#/components/schemas/OrderDocument'
          readOnly: true
        shipments:
          type: array
          items:
            $ref: '#/components/schemas/Shipment'
        order_acknowledgements:
          type: array
          items:
            $ref: '#/components/schemas/OrderAcknowledgement'
          readOnly: true
        is_external_po_processing:
          type: boolean
          nullable: true
        is_po_editable:
          type: boolean
          nullable: true
      required:
      - date_field_label
      - date_help_text
      - document
      - external_requestor_email
      - id
      - order_acknowledgements
      - placed_by
      - requestor_display_name
      - requestor_email
      - source
      - supplier
      - team
      - total_cost_currency
    PurchaseOrderComment:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        created_by:
          allOf:
          - $ref: '#/components/schemas/User'
          readOnly: true
        created_at:
          type: string
          format: date-time
          readOnly: true
        comment:
          type: string
      required:
      - comment
      - created_at
      - created_by
      - id
    PurchaseOrderStatusUpdate:
      type: object
      properties:
        purchase_order:
          type: integer
          readOnly: true
        user:
          $ref: '#/components/schemas/User'
        system_actor:
          readOnly: true
          nullable: true
          description: |-
            System process that made this change; null if it was User-initiated

            * `email_parsed` - EMAIL_PARSED
            * `daily_automation` - DAILY_AUTOMATION
            * `system_logic` - SYSTEM_LOGIC
          oneOf:
          - $ref: '#/components/schemas/SystemActorEnum'
          - $ref: '#/components/schemas/NullEnum'
        triggering_communication:
          type: integer
          readOnly: true
          nullable: true
          description: The communication that triggered this update, if any
        status_changed:
          allOf:
          - $ref: '#/components/schemas/StatusChangedEnum'
          readOnly: true
        old_status:
          allOf:
          - $ref: '#/components/schemas/OldStatusEnum'
          readOnly: true
        new_status:
          allOf:
          - $ref: '#/components/schemas/NewStatusEnum'
          readOnly: true
        created_at:
          type: string
          format: date-time
          readOnly: true
      required:
      - created_at
      - new_status
      - old_status
      - purchase_order
      - status_changed
      - system_actor
      - triggering_communication
      - user
    ReqDocumentTypeEnum:
      enum:
      - comply
      - contract
      - delivery_conf
      - external_po_import
      - goodsrct
      - invoice
      - nda
      - order_ack
      - pickup_sheet
      - shipping_order
      - shipping_document
      - delivery_note
      - despatch_list
      - other
      - payment_conf
      - po
      - price_list
      - processing
      - quote_import
      - unknown
      type: string
      description: |-
        * `comply` - COMPLIANCE
        * `contract` - CONTRACT
        * `delivery_conf` - DELIVERY_CONFIRMATION
        * `external_po_import` - EXTERNAL_PO_IMPORT
        * `goodsrct` - GOODS_RECEIPT
        * `invoice` - INVOICE
        * `nda` - NDA
        * `order_ack` - ORDER_ACKNOWLEDGEMENT
        * `pickup_sheet` - PICKUP_SHEET
        * `shipping_order` - SHIPPING_ORDER
        * `shipping_document` - SHIPPING_DOCUMENT
        * `delivery_note` - DELIVERY_NOTE
        * `despatch_list` - DESPATCH_LIST
        * `other` - OTHER
        * `payment_conf` - PAYMENT_CONFIRMATION
        * `po` - PO
        * `price_list` - PRICE_LIST
        * `processing` - PROCESSING
        * `quote_import` - QUOTE_IMPORT
        * `unknown` - UNKNOWN
    ReqTypeEnum:
      enum:
      - document
      - information
      - checkbox
      type: string
      description: |-
        * `document` - DOCUMENT
        * `information` - INFORMATION
        * `checkbox` - CHECKBOX
    Shipment:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        purchase_order_id:
          type: integer
          writeOnly: true
          nullable: true
        carrier:
          type: string
          nullable: true
        carrier_type:
          nullable: true
          oneOf:
          - $ref: '#/components/schemas/CarrierTypeEnum'
          - $ref: '#/components/schemas/BlankEnum'
          - $ref: '#/components/schemas/NullEnum'
        tracking_number:
          type: string
          nullable: true
        shipment_date:
          type: string
          format: date
          nullable: true
        estimated_delivery_date:
          type: string
          format: date
          nullable: true
        actual_delivery_date:
          type: string
          format: date
          nullable: true
        line_items:
          type: array
          items:
            $ref: '#/components/schemas/ShipmentLineItemInner'
        status:
          $ref: '#/components/schemas/ShipmentStatusEnum'
      required:
      - id
    ShipmentLineItemInner:
      type: object
      properties:
        order_item:
          allOf:
          - $ref: '#/components/schemas/OrderItem'
          readOnly: true
        order_item_id:
          type: integer
          writeOnly: true
        shipped_quantity:
          type: number
          format: double
      required:
      - order_item
      - order_item_id
      - shipped_quantity
    ShipmentStatusEnum:
      enum:
      - awaiting_shipment
      - ready_for_pickup
      - shipped
      - received
      - canceled
      type: string
      description: |-
        * `awaiting_shipment` - AWAITING_SHIPMENT
        * `ready_for_pickup` - READY_FOR_PICKUP
        * `shipped` - SHIPPED
        * `received` - RECEIVED
        * `canceled` - CANCELED
    ShippingAddressCountryEnum:
      enum:
      - AF
      - AX
      - AL
      - DZ
      - AS
      - AD
      - AO
      - AI
      - AQ
      - AG
      - AR
      - AM
      - AW
      - AU
      - AT
      - AZ
      - BS
      - BH
      - BD
      - BB
      - BY
      - BE
      - BZ
      - BJ
      - BM
      - BT
      - BO
      - BQ
      - BA
      - BW
      - BV
      - BR
      - IO
      - BN
      - BG
      - BF
      - BI
      - CV
      - KH
      - CM
      - CA
      - KY
      - CF
      - TD
      - CL
      - CN
      - CX
      - CC
      - CO
      - KM
      - CG
      - CD
      - CK
      - CR
      - CI
      - HR
      - CU
      - CW
      - CY
      - CZ
      - DK
      - DJ
      - DM
      - DO
      - EC
      - EG
      - SV
      - GQ
      - ER
      - EE
      - SZ
      - ET
      - FK
      - FO
      - FJ
      - FI
      - FR
      - GF
      - PF
      - TF
      - GA
      - GM
      - GE
      - DE
      - GH
      - GI
      - GR
      - GL
      - GD
      - GP
      - GU
      - GT
      - GG
      - GN
      - GW
      - GY
      - HT
      - HM
      - VA
      - HN
      - HK
      - HU
      - IS
      - IN
      - ID
      - IR
      - IQ
      - IE
      - IM
      - IL
      - IT
      - JM
      - JP
      - JE
      - JO
      - KZ
      - KE
      - KI
      - KW
      - KG
      - LA
      - LV
      - LB
      - LS
      - LR
      - LY
      - LI
      - LT
      - LU
      - MO
      - MG
      - MW
      - MY
      - MV
      - ML
      - MT
      - MH
      - MQ
      - MR
      - MU
      - YT
      - MX
      - FM
      - MD
      - MC
      - MN
      - ME
      - MS
      - MA
      - MZ
      - MM
      - NA
      - NR
      - NP
      - NL
      - NC
      - NZ
      - NI
      - NE
      - NG
      - NU
      - NF
      - KP
      - MK
      - MP
      - 'NO'
      - OM
      - PK
      - PW
      - PS
      - PA
      - PG
      - PY
      - PE
      - PH
      - PN
      - PL
      - PT
      - PR
      - QA
      - RE
      - RO
      - RU
      - RW
      - BL
      - SH
      - KN
      - LC
      - MF
      - PM
      - VC
      - WS
      - SM
      - ST
      - SA
      - SN
      - RS
      - SC
      - SL
      - SG
      - SX
      - SK
      - SI
      - SB
      - SO
      - ZA
      - GS
      - KR
      - SS
      - ES
      - LK
      - SD
      - SR
      - SJ
      - SE
      - CH
      - SY
      - TW
      - TJ
      - TZ
      - TH
      - TL
      - TG
      - TK
      - TO
      - TT
      - TN
      - TR
      - TM
      - TC
      - TV
      - UG
      - UA
      - AE
      - GB
      - UM
      - US
      - UY
      - UZ
      - VU
      - VE
      - VN
      - VG
      - VI
      - WF
      - EH
      - YE
      - ZM
      - ZW
      type: string
      description: |-
        * `AF` - Afghanistan
        * `AX` - Åland Islands
        * `AL` - Albania
        * `DZ` - Algeria
        * `AS` - American Samoa
        * `AD` - Andorra
        * `AO` - Angola
        * `AI` - Anguilla
        * `AQ` - Antarctica
        * `AG` - Antigua and Barbuda
        * `AR` - Argentina
        * `AM` - Armenia
        * `AW` - Aruba
        * `AU` - Australia
        * `AT` - Austria
        * `AZ` - Azerbaijan
        * `BS` - Bahamas
        * `BH` - Bahrain
        * `BD` - Bangladesh
        * `BB` - Barbados
        * `BY` - Belarus
        * `BE` - Belgium
        * `BZ` - Belize
        * `BJ` - Benin
        * `BM` - Bermuda
        * `BT` - Bhutan
        * `BO` - Bolivia
        * `BQ` - Bonaire, Sint Eustatius and Saba
        * `BA` - Bosnia and Herzegovina
        * `BW` - Botswana
        * `BV` - Bouvet Island
        * `BR` - Brazil
        * `IO` - British Indian Ocean Territory
        * `BN` - Brunei
        * `BG` - Bulgaria
        * `BF` - Burkina Faso
        * `BI` - Burundi
        * `CV` - Cabo Verde
        * `KH` - Cambodia
        * `CM` - Cameroon
        * `CA` - Canada
        * `KY` - Cayman Islands
        * `CF` - Central African Republic
        * `TD` - Chad
        * `CL` - Chile
        * `CN` - China
        * `CX` - Christmas Island
        * `CC` - Cocos (Keeling) Islands
        * `CO` - Colombia
        * `KM` - Comoros
        * `CG` - Congo
        * `CD` - Congo (the Democratic Republic of the)
        * `CK` - Cook Islands
        * `CR` - Costa Rica
        * `CI` - Côte d'Ivoire
        * `HR` - Croatia
        * `CU` - Cuba
        * `CW` - Curaçao
        * `CY` - Cyprus
        * `CZ` - Czechia
        * `DK` - Denmark
        * `DJ` - Djibouti
        * `DM` - Dominica
        * `DO` - Dominican Republic
        * `EC` - Ecuador
        * `EG` - Egypt
        * `SV` - El Salvador
        * `GQ` - Equatorial Guinea
        * `ER` - Eritrea
        * `EE` - Estonia
        * `SZ` - Eswatini
        * `ET` - Ethiopia
        * `FK` - Falkland Islands (Malvinas)
        * `FO` - Faroe Islands
        * `FJ` - Fiji
        * `FI` - Finland
        * `FR` - France
        * `GF` - French Guiana
        * `PF` - French Polynesia
        * `TF` - French Southern Territories
        * `GA` - Gabon
        * `GM` - Gambia
        * `GE` - Georgia
        * `DE` - Germany
        * `GH` - Ghana
        * `GI` - Gibraltar
        * `GR` - Greece
        * `GL` - Greenland
        * `GD` - Grenada
        * `GP` - Guadeloupe
        * `GU` - Guam
        * `GT` - Guatemala
        * `GG` - Guernsey
        * `GN` - Guinea
        * `GW` - Guinea-Bissau
        * `GY` - Guyana
        * `HT` - Haiti
        * `HM` - Heard Island and McDonald Islands
        * `VA` - Holy See
        * `HN` - Honduras
        * `HK` - Hong Kong
        * `HU` - Hungary
        * `IS` - Iceland
        * `IN` - India
        * `ID` - Indonesia
        * `IR` - Iran
        * `IQ` - Iraq
        * `IE` - Ireland
        * `IM` - Isle of Man
        * `IL` - Israel
        * `IT` - Italy
        * `JM` - Jamaica
        * `JP` - Japan
        * `JE` - Jersey
        * `JO` - Jordan
        * `KZ` - Kazakhstan
        * `KE` - Kenya
        * `KI` - Kiribati
        * `KW` - Kuwait
        * `KG` - Kyrgyzstan
        * `LA` - Laos
        * `LV` - Latvia
        * `LB` - Lebanon
        * `LS` - Lesotho
        * `LR` - Liberia
        * `LY` - Libya
        * `LI` - Liechtenstein
        * `LT` - Lithuania
        * `LU` - Luxembourg
        * `MO` - Macao
        * `MG` - Madagascar
        * `MW` - Malawi
        * `MY` - Malaysia
        * `MV` - Maldives
        * `ML` - Mali
        * `MT` - Malta
        * `MH` - Marshall Islands
        * `MQ` - Martinique
        * `MR` - Mauritania
        * `MU` - Mauritius
        * `YT` - Mayotte
        * `MX` - Mexico
        * `FM` - Micronesia (Federated States of)
        * `MD` - Moldova
        * `MC` - Monaco
        * `MN` - Mongolia
        * `ME` - Montenegro
        * `MS` - Montserrat
        * `MA` - Morocco
        * `MZ` - Mozambique
        * `MM` - Myanmar
        * `NA` - Namibia
        * `NR` - Nauru
        * `NP` - Nepal
        * `NL` - Netherlands
        * `NC` - New Caledonia
        * `NZ` - New Zealand
        * `NI` - Nicaragua
        * `NE` - Niger
        * `NG` - Nigeria
        * `NU` - Niue
        * `NF` - Norfolk Island
        * `KP` - North Korea
        * `MK` - North Macedonia
        * `MP` - Northern Mariana Islands
        * `NO` - Norway
        * `OM` - Oman
        * `PK` - Pakistan
        * `PW` - Palau
        * `PS` - Palestine, State of
        * `PA` - Panama
        * `PG` - Papua New Guinea
        * `PY` - Paraguay
        * `PE` - Peru
        * `PH` - Philippines
        * `PN` - Pitcairn
        * `PL` - Poland
        * `PT` - Portugal
        * `PR` - Puerto Rico
        * `QA` - Qatar
        * `RE` - Réunion
        * `RO` - Romania
        * `RU` - Russia
        * `RW` - Rwanda
        * `BL` - Saint Barthélemy
        * `SH` - Saint Helena, Ascension and Tristan da Cunha
        * `KN` - Saint Kitts and Nevis
        * `LC` - Saint Lucia
        * `MF` - Saint Martin (French part)
        * `PM` - Saint Pierre and Miquelon
        * `VC` - Saint Vincent and the Grenadines
        * `WS` - Samoa
        * `SM` - San Marino
        * `ST` - Sao Tome and Principe
        * `SA` - Saudi Arabia
        * `SN` - Senegal
        * `RS` - Serbia
        * `SC` - Seychelles
        * `SL` - Sierra Leone
        * `SG` - Singapore
        * `SX` - Sint Maarten (Dutch part)
        * `SK` - Slovakia
        * `SI` - Slovenia
        * `SB` - Solomon Islands
        * `SO` - Somalia
        * `ZA` - South Africa
        * `GS` - South Georgia and the South Sandwich Islands
        * `KR` - South Korea
        * `SS` - South Sudan
        * `ES` - Spain
        * `LK` - Sri Lanka
        * `SD` - Sudan
        * `SR` - Suriname
        * `SJ` - Svalbard and Jan Mayen
        * `SE` - Sweden
        * `CH` - Switzerland
        * `SY` - Syria
        * `TW` - Taiwan
        * `TJ` - Tajikistan
        * `TZ` - Tanzania
        * `TH` - Thailand
        * `TL` - Timor-Leste
        * `TG` - Togo
        * `TK` - Tokelau
        * `TO` - Tonga
        * `TT` - Trinidad and Tobago
        * `TN` - Tunisia
        * `TR` - Türkiye
        * `TM` - Turkmenistan
        * `TC` - Turks and Caicos Islands
        * `TV` - Tuvalu
        * `UG` - Uganda
        * `UA` - Ukraine
        * `AE` - United Arab Emirates
        * `GB` - United Kingdom
        * `UM` - United States Minor Outlying Islands
        * `US` - United States of America
        * `UY` - Uruguay
        * `UZ` - Uzbekistan
        * `VU` - Vanuatu
        * `VE` - Venezuela
        * `VN` - Vietnam
        * `VG` - Virgin Islands (British)
        * `VI` - Virgin Islands (U.S.)
        * `WF` - Wallis and Futuna
        * `EH` - Western Sahara
        * `YE` - Yemen
        * `ZM` - Zambia
        * `ZW` - Zimbabwe
    ShippingDocument:
      type: object
      description: Main serializer for ShippingDocument
      properties:
        id:
          type: integer
          readOnly: true
        document_type:
          allOf:
          - $ref: '#/components/schemas/DocumentTypeEnum'
          description: |-
            Type of shipping document

            * `bill_of_lading` - Bill of Lading
            * `packing_list` - Packing List
            * `advance_shipping_notice` - Advance Shipping Notice
            * `goods_receipt_note` - Goods Receipt Note
            * `delivery_note` - Delivery Note
        reference_number:
          type: string
          description: Document reference number (BOL#, Packing List#, ASN#, etc.)
          maxLength: 255
        document:
          allOf:
          - $ref: '#/components/schemas/BasicDocument'
          readOnly: true
        purchase_order:
          type: integer
          nullable: true
          description: Related purchase order if applicable
        po_number:
          type: string
          readOnly: true
        shipment:
          type: integer
          nullable: true
          description: Related shipment if applicable
        supplier:
          allOf:
          - $ref: '#/components/schemas/BasicSupplier'
          readOnly: true
        carrier_name:
          type: string
          description: Name of carrier/shipping company
          maxLength: 255
        tracking_number:
          type: string
          description: Shipment tracking number
          maxLength: 255
        document_date:
          type: string
          format: date
          readOnly: true
        received_date:
          type: string
          format: date-time
          readOnly: true
        notes:
          type: string
          description: General notes about the shipment
        extracted_data:
          description: Document-type-specific fields extracted by AI
        status:
          allOf:
          - $ref: '#/components/schemas/ShippingDocumentStatusEnum'
          description: |-
            Current processing status

            * `pending` - Pending Processing
            * `processing` - Processing
            * `processed` - Processed
            * `matched` - Matched to PO
            * `rejected` - Rejected
            * `requires_review` - Requires Review
        processed_at:
          type: string
          format: date-time
          readOnly: true
        processed_by:
          type: integer
          readOnly: true
          nullable: true
          description: User who processed the document
        shipping_items:
          type: array
          items:
            $ref: '#/components/schemas/ShippingItem'
          readOnly: true
        total_items:
          type: integer
          description: Get count of shipping items using prefetched data to avoid
            N+1 queries
          readOnly: true
        created_at:
          type: string
          format: date-time
          readOnly: true
        modified_at:
          type: string
          format: date-time
          readOnly: true
      required:
      - created_at
      - document
      - document_date
      - document_type
      - id
      - modified_at
      - po_number
      - processed_at
      - processed_by
      - received_date
      - reference_number
      - shipping_items
      - supplier
      - total_items
    ShippingDocumentStatusEnum:
      enum:
      - pending
      - processing
      - processed
      - matched
      - rejected
      - requires_review
      type: string
      description: |-
        * `pending` - Pending Processing
        * `processing` - Processing
        * `processed` - Processed
        * `matched` - Matched to PO
        * `rejected` - Rejected
        * `requires_review` - Requires Review
    ShippingItem:
      type: object
      description: Serializer for ShippingItem model
      properties:
        id:
          type: integer
          readOnly: true
        order_item:
          allOf:
          - $ref: '#/components/schemas/OrderItem'
          readOnly: true
        item_number:
          type: string
          readOnly: true
        item_description:
          type: string
          readOnly: true
        ordered_quantity:
          type: number
          format: double
          readOnly: true
        received_quantity:
          type: number
          format: double
          description: Quantity actually received
        condition:
          allOf:
          - $ref: '#/components/schemas/ConditionEnum'
          description: |-
            Condition of received items

            * `good` - Good Condition
            * `damaged` - Damaged
            * `partial_damage` - Partially Damaged
        notes:
          type: string
          description: Notes about condition, discrepancies, etc.
      required:
      - id
      - item_description
      - item_number
      - order_item
      - ordered_quantity
      - received_quantity
    SourceEnum:
      enum:
      - email
      - erp
      - external_po_import
      - didero
      - netsuite_sync
      type: string
      description: |-
        * `email` - EMAIL
        * `erp` - ERP
        * `external_po_import` - EXTERNAL_PO_IMPORT
        * `didero` - DIDERO
        * `netsuite_sync` - NETSUITE_SYNC
    Status826Enum:
      enum:
      - PENDING
      - ON_HOLD
      - COMPLETED
      - FAILED
      type: string
      description: |-
        * `PENDING` - PENDING
        * `ON_HOLD` - ON_HOLD
        * `COMPLETED` - COMPLETED
        * `FAILED` - FAILED
    StatusChangedEnum:
      enum:
      - order_status
      - payment_status
      type: string
      description: |-
        * `order_status` - Order Status
        * `payment_status` - Payment Status
    StyleEnum:
      enum:
      - PRIMARY
      - SUCCESS
      - DANGER
      - WARNING
      type: string
      description: |-
        * `PRIMARY` - PRIMARY
        * `SUCCESS` - SUCCESS
        * `DANGER` - DANGER
        * `WARNING` - WARNING
    Supplier:
      type: object
      properties:
        name:
          type: string
          maxLength: 256
        description:
          type: string
          nullable: true
        website_url:
          type: string
          format: uri
          nullable: true
          maxLength: 200
        notes:
          type: string
        archived_at:
          type: string
          format: date-time
          nullable: true
        payment_terms:
          type: string
        shipping_method:
          type: string
        default_contact:
          type: integer
          nullable: true
          description: Default contact for this supplier used for general communications
        default_shipping_terms:
          type: string
          nullable: true
          description: Default shipping terms for purchase orders with this supplier
          maxLength: 50
        available_shipping_terms:
          type: array
          items:
            type: string
            maxLength: 50
          nullable: true
          description: List of shipping terms this supplier supports (including custom
            terms)
        id:
          type: integer
          readOnly: true
        onboarding_status:
          allOf:
          - $ref: '#/components/schemas/OnboardingStatusEnum'
          readOnly: true
        blocked_email_addresses:
          type: array
          items:
            type: string
            format: email
          readOnly: true
        domains:
          type: array
          items:
            type: string
          readOnly: true
        team:
          type: integer
          readOnly: true
        created_at:
          type: string
          format: date-time
          readOnly: true
        modified_at:
          type: string
          format: date-time
          readOnly: true
        contacts:
          type: string
          readOnly: true
        last_contact:
          type: string
          readOnly: true
        erp_suppliers:
          type: array
          items:
            $ref: '#/components/schemas/ERPSupplier'
        supplier_address:
          type: string
          readOnly: true
        tags:
          type: array
          items:
            type: string
        default_contact_detail:
          type: string
          readOnly: true
        default_email:
          type: string
          nullable: true
          description: Get the email address of the default contact, if available.
          readOnly: true
      required:
      - blocked_email_addresses
      - contacts
      - created_at
      - default_contact_detail
      - default_email
      - domains
      - id
      - last_contact
      - modified_at
      - name
      - onboarding_status
      - supplier_address
      - team
      - website_url
    SupplierComment:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        created_by:
          allOf:
          - $ref: '#/components/schemas/User'
          readOnly: true
        created_at:
          type: string
          format: date-time
          readOnly: true
        supplier_id:
          type: integer
          readOnly: true
        comment:
          type: string
      required:
      - comment
      - created_at
      - created_by
      - id
      - supplier_id
    SupplierContact:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        last_contact:
          type: string
          format: date-time
          readOnly: true
        created_at:
          type: string
          format: date-time
          readOnly: true
        name:
          type: string
          maxLength: 256
        phone:
          type: string
          nullable: true
          maxLength: 15
        email:
          type: string
          format: email
          nullable: true
          maxLength: 256
        additional_info:
          type: string
        archived_at:
          type: string
          format: date-time
          nullable: true
      required:
      - created_at
      - id
      - last_contact
    SupplierOnboardingRequirement:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        supplier_id:
          type: integer
          readOnly: true
        supplier_name:
          type: string
          readOnly: true
        req_type:
          allOf:
          - $ref: '#/components/schemas/ReqTypeEnum'
          readOnly: true
        req_document_type:
          readOnly: true
          nullable: true
          oneOf:
          - $ref: '#/components/schemas/ReqDocumentTypeEnum'
          - $ref: '#/components/schemas/NullEnum'
        req_document_label:
          type: string
          readOnly: true
        req_document_doc:
          type: string
          readOnly: true
        req_information_name:
          type: string
          readOnly: true
        req_checkbox_name:
          type: string
          readOnly: true
          maxLength: 255
        status:
          type: string
          readOnly: true
        req_information_value:
          type: string
          maxLength: 255
        req_checkbox_value:
          type: boolean
      required:
      - id
      - req_checkbox_name
      - req_document_doc
      - req_document_label
      - req_document_type
      - req_information_name
      - req_type
      - status
      - supplier_id
      - supplier_name
    SystemActorEnum:
      enum:
      - email_parsed
      - daily_automation
      - system_logic
      type: string
      description: |-
        * `email_parsed` - EMAIL_PARSED
        * `daily_automation` - DAILY_AUTOMATION
        * `system_logic` - SYSTEM_LOGIC
    Tag:
      type: object
      properties:
        name:
          type: string
          maxLength: 100
        color:
          type: string
          maxLength: 7
      required:
      - name
    Task:
      type: object
      properties:
        user:
          allOf:
          - $ref: '#/components/schemas/User'
          readOnly: true
        user_group:
          type: integer
          nullable: true
        task_type:
          $ref: '#/components/schemas/BasicTaskType'
        task_config:
          $ref: '#/components/schemas/TaskConfig'
        status:
          $ref: '#/components/schemas/Status826Enum'
        model_type:
          type: string
          readOnly: true
        model_id:
          type: string
          maxLength: 255
        next_reminder_at:
          type: string
          format: date-time
          nullable: true
        id:
          type: integer
          readOnly: true
        created_at:
          type: string
          format: date-time
          readOnly: true
        modified_at:
          type: string
          format: date-time
          readOnly: true
      required:
      - created_at
      - id
      - model_id
      - model_type
      - modified_at
      - status
      - task_config
      - task_type
      - user
    TaskAction:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        action_type:
          $ref: '#/components/schemas/TaskActionType'
        title:
          type: string
          maxLength: 255
        sub_text:
          type: string
          maxLength: 255
        status:
          $ref: '#/components/schemas/TaskActionStatusEnum'
        execution_type:
          allOf:
          - $ref: '#/components/schemas/ExecutionTypeEnum'
          description: |-
            Determines where this action is executed (server or client)

            * `server` - SERVER
            * `server_async` - SERVER_ASYNC
            * `client` - CLIENT
        execution_params:
          nullable: true
        executed_by:
          allOf:
          - $ref: '#/components/schemas/User'
          readOnly: true
        executed_at:
          type: string
          format: date-time
        execution_status:
          nullable: true
          oneOf:
          - $ref: '#/components/schemas/ExecutionStatusEnum'
          - $ref: '#/components/schemas/BlankEnum'
          - $ref: '#/components/schemas/NullEnum'
      required:
      - action_type
      - executed_at
      - executed_by
      - id
      - sub_text
      - title
    TaskActionStatusEnum:
      enum:
      - PENDING
      - COMPLETED
      - FAILED
      - INVALIDATED
      type: string
      description: |-
        * `PENDING` - PENDING
        * `COMPLETED` - COMPLETED
        * `FAILED` - FAILED
        * `INVALIDATED` - INVALIDATED
    TaskActionType:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        name:
          $ref: '#/components/schemas/TaskActionTypeNameEnum'
      required:
      - id
      - name
    TaskActionTypeNameEnum:
      enum:
      - SEND_EMAIL
      - OPS_ADD_COMMENT
      - ADD_COMMENT
      - AUTO_COMMENT
      - UPDATE_NETSUITE
      - APPROVE_PURCHASE_ORDER
      - DENY_PURCHASE_ORDER
      - CONFIRM_PURCHASE_ORDER
      - CANCEL_PURCHASE_ORDER
      - CONFIRM_ORDER_ACKNOWLEDGEMENT
      - REJECT_ORDER_ACKNOWLEDGEMENT
      - APPROVE_OA_MISMATCH
      - APPROVE_FREIGHT_CHARGE
      - APPROVE_PRICE_CHANGE
      - APPROVE_ADDRESS_CHANGE
      - CONFIRM_SHIPMENT
      - CANCEL_SHIPMENT
      - APPROVE_DOCUMENT_MATCH
      - REQUEST_DOCUMENT_CLARIFICATION
      - UPLOAD_DOCUMENT
      - EXECUTE_DATABASE_UPDATE
      - REJECT_DATABASE_UPDATE
      type: string
      description: |-
        * `SEND_EMAIL` - SEND_EMAIL
        * `OPS_ADD_COMMENT` - OPS_ADD_COMMENT
        * `ADD_COMMENT` - ADD_COMMENT
        * `AUTO_COMMENT` - AUTO_COMMENT
        * `UPDATE_NETSUITE` - UPDATE_NETSUITE
        * `APPROVE_PURCHASE_ORDER` - APPROVE_PURCHASE_ORDER
        * `DENY_PURCHASE_ORDER` - DENY_PURCHASE_ORDER
        * `CONFIRM_PURCHASE_ORDER` - CONFIRM_PURCHASE_ORDER
        * `CANCEL_PURCHASE_ORDER` - CANCEL_PURCHASE_ORDER
        * `CONFIRM_ORDER_ACKNOWLEDGEMENT` - CONFIRM_ORDER_ACKNOWLEDGEMENT
        * `REJECT_ORDER_ACKNOWLEDGEMENT` - REJECT_ORDER_ACKNOWLEDGEMENT
        * `APPROVE_OA_MISMATCH` - APPROVE_OA_MISMATCH
        * `APPROVE_FREIGHT_CHARGE` - APPROVE_FREIGHT_CHARGE
        * `APPROVE_PRICE_CHANGE` - APPROVE_PRICE_CHANGE
        * `APPROVE_ADDRESS_CHANGE` - APPROVE_ADDRESS_CHANGE
        * `CONFIRM_SHIPMENT` - CONFIRM_SHIPMENT
        * `CANCEL_SHIPMENT` - CANCEL_SHIPMENT
        * `APPROVE_DOCUMENT_MATCH` - APPROVE_DOCUMENT_MATCH
        * `REQUEST_DOCUMENT_CLARIFICATION` - REQUEST_DOCUMENT_CLARIFICATION
        * `UPLOAD_DOCUMENT` - UPLOAD_DOCUMENT
        * `EXECUTE_DATABASE_UPDATE` - EXECUTE_DATABASE_UPDATE
        * `REJECT_DATABASE_UPDATE` - REJECT_DATABASE_UPDATE
    TaskConfig:
      type: object
      properties:
        buttons:
          type: array
          items:
            $ref: '#/components/schemas/Button'
        description:
          type: string
      required:
      - buttons
      - description
    TaskConfigV2:
      type: object
      properties:
        title:
          type: string
        description:
          type: string
          nullable: true
        task_type:
          type: string
          nullable: true
        preview_title:
          type: string
        preview_description:
          type: string
        category:
          type: string
          nullable: true
        task_params:
          nullable: true
        context_panels:
          nullable: true
        action_data:
          nullable: true
      required:
      - category
      - description
      - preview_description
      - preview_title
      - task_type
      - title
    TaskTypeV2:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        name:
          $ref: '#/components/schemas/Name247Enum'
      required:
      - id
      - name
    TaskV2:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        task_type_v2:
          $ref: '#/components/schemas/TaskTypeV2'
        task_config:
          $ref: '#/components/schemas/TaskConfigV2'
        model_type:
          type: string
          readOnly: true
        model_id:
          type: string
          maxLength: 255
        model_details:
          type: string
          readOnly: true
        status:
          $ref: '#/components/schemas/Status826Enum'
        next_reminder_at:
          type: string
          format: date-time
          nullable: true
        actions:
          type: string
          readOnly: true
        created_at:
          type: string
          format: date-time
        seconds_since_creation:
          type: string
          readOnly: true
        user:
          allOf:
          - $ref: '#/components/schemas/User'
          readOnly: true
        user_group:
          allOf:
          - $ref: '#/components/schemas/UserGroup'
          readOnly: true
      required:
      - actions
      - created_at
      - id
      - model_details
      - model_id
      - model_type
      - seconds_since_creation
      - status
      - task_config
      - task_type_v2
      - user
      - user_group
    Team:
      type: object
      properties:
        uuid:
          type: string
          format: uuid
          readOnly: true
        id:
          type: integer
          readOnly: true
        inbound_mailing_address:
          type: string
          format: email
          readOnly: true
        members:
          type: string
          readOnly: true
        integration_providers:
          type: array
          items:
            $ref: '#/components/schemas/TeamIntegrationProvider'
        default_address:
          type: string
          readOnly: true
        is_demo_team:
          type: boolean
          readOnly: true
        name:
          type: string
          maxLength: 32
        default_po_email_credential:
          type: integer
          nullable: true
        default_requestor:
          $ref: '#/components/schemas/User'
        logo:
          type: string
          format: uri
          nullable: true
        terms_and_conditions:
          type: string
          format: uri
          nullable: true
        legal_disclaimer:
          type: string
          nullable: true
          description: Legal disclaimer text to appear at the bottom of PO PDFs
          maxLength: 1000
      required:
      - default_address
      - default_requestor
      - id
      - inbound_mailing_address
      - is_demo_team
      - members
      - name
      - uuid
    TeamIntegrationProvider:
      type: object
      properties:
        provider:
          $ref: '#/components/schemas/ProviderEnum'
        provider_user_id:
          type: string
          maxLength: 256
      required:
      - provider
      - provider_user_id
    TeamSetting:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        team:
          type: integer
        user:
          type: integer
          nullable: true
        name:
          $ref: '#/components/schemas/TeamSettingNameEnum'
        value:
          nullable: true
      required:
      - id
      - name
      - team
    TeamSettingNameEnum:
      enum:
      - TEAM_AUTO_ARCHIVE_AFTER_DAYS
      - TEAM_PO_DEFAULT_EMAIL_TEMPLATE
      - TEAM_PO_DEFAULT_SUPPLIER_NOTE
      - TEAM_PO_DEFAULT_TERMS
      - TEAM_SUPPLIER_ONBOARDING_REQUIREMENTS
      - TEAM_AI_PROCESS_EMAILS_ENABLED
      - TEAM_AI_PARSE_DOCUMENT_DETAILS_ENABLED
      - TEAM_QUOTE_TO_PO_ENABLED
      - TEAM_TASK_EMAIL_NOTIFICATIONS_ENABLED
      - TEAM_FOLLOWUP_WAIT_TIME_HOURS
      - TEAM_FOLLOWUP_GLOBAL_CONFIG
      - TEAM_FOLLOWUP_OA_CONFIG
      - TEAM_FOLLOWUP_SHIPMENT_CONFIG
      - TEAM_FOLLOWUP_DELIVERY_CONFIG
      - TEAM_PO_CREATION_HUMAN_VALIDATION_ENABLED
      - TEAM_ORDER_ACK_HUMAN_VALIDATION_ENABLED
      - TEAM_SHIPMENT_HUMAN_VALIDATION_ENABLED
      - TEAM_INVOICE_PROCESSING_HUMAN_VALIDATION_ENABLED
      - TEAM_SHIPPING_DOCUMENT_HUMAN_VALIDATION_ENABLED
      - USER_EMAIL_NOTIFICATIONS_REMINDER_ENABLED
      - USER_EMAIL_PO_APPROVAL_REMINDER_ENABLED
      - USER_EMAIL_PO_APPROVAL_REQUEST_ENABLED
      - USER_EMAIL_PO_UPDATE_ENABLED
      - USER_EMAIL_TASK_NOTIFICATIONS_ENABLED
      type: string
      description: |-
        * `TEAM_AUTO_ARCHIVE_AFTER_DAYS` - TEAM_AUTO_ARCHIVE_AFTER_DAYS
        * `TEAM_PO_DEFAULT_EMAIL_TEMPLATE` - TEAM_PO_DEFAULT_EMAIL_TEMPLATE
        * `TEAM_PO_DEFAULT_SUPPLIER_NOTE` - TEAM_PO_DEFAULT_SUPPLIER_NOTE
        * `TEAM_PO_DEFAULT_TERMS` - TEAM_PO_DEFAULT_TERMS
        * `TEAM_SUPPLIER_ONBOARDING_REQUIREMENTS` - TEAM_SUPPLIER_ONBOARDING_REQUIREMENTS
        * `TEAM_AI_PROCESS_EMAILS_ENABLED` - TEAM_AI_PROCESS_EMAILS_ENABLED
        * `TEAM_AI_PARSE_DOCUMENT_DETAILS_ENABLED` - TEAM_AI_PARSE_DOCUMENT_DETAILS_ENABLED
        * `TEAM_QUOTE_TO_PO_ENABLED` - TEAM_QUOTE_TO_PO_ENABLED
        * `TEAM_TASK_EMAIL_NOTIFICATIONS_ENABLED` - TEAM_TASK_EMAIL_NOTIFICATIONS_ENABLED
        * `TEAM_FOLLOWUP_WAIT_TIME_HOURS` - TEAM_FOLLOWUP_WAIT_TIME_HOURS
        * `TEAM_FOLLOWUP_GLOBAL_CONFIG` - TEAM_FOLLOWUP_GLOBAL_CONFIG
        * `TEAM_FOLLOWUP_OA_CONFIG` - TEAM_FOLLOWUP_OA_CONFIG
        * `TEAM_FOLLOWUP_SHIPMENT_CONFIG` - TEAM_FOLLOWUP_SHIPMENT_CONFIG
        * `TEAM_FOLLOWUP_DELIVERY_CONFIG` - TEAM_FOLLOWUP_DELIVERY_CONFIG
        * `TEAM_PO_CREATION_HUMAN_VALIDATION_ENABLED` - TEAM_PO_CREATION_HUMAN_VALIDATION_ENABLED
        * `TEAM_ORDER_ACK_HUMAN_VALIDATION_ENABLED` - TEAM_ORDER_ACK_HUMAN_VALIDATION_ENABLED
        * `TEAM_SHIPMENT_HUMAN_VALIDATION_ENABLED` - TEAM_SHIPMENT_HUMAN_VALIDATION_ENABLED
        * `TEAM_INVOICE_PROCESSING_HUMAN_VALIDATION_ENABLED` - TEAM_INVOICE_PROCESSING_HUMAN_VALIDATION_ENABLED
        * `TEAM_SHIPPING_DOCUMENT_HUMAN_VALIDATION_ENABLED` - TEAM_SHIPPING_DOCUMENT_HUMAN_VALIDATION_ENABLED
        * `USER_EMAIL_NOTIFICATIONS_REMINDER_ENABLED` - USER_EMAIL_NOTIFICATIONS_REMINDER_ENABLED
        * `USER_EMAIL_PO_APPROVAL_REMINDER_ENABLED` - USER_EMAIL_PO_APPROVAL_REMINDER_ENABLED
        * `USER_EMAIL_PO_APPROVAL_REQUEST_ENABLED` - USER_EMAIL_PO_APPROVAL_REQUEST_ENABLED
        * `USER_EMAIL_PO_UPDATE_ENABLED` - USER_EMAIL_PO_UPDATE_ENABLED
        * `USER_EMAIL_TASK_NOTIFICATIONS_ENABLED` - USER_EMAIL_TASK_NOTIFICATIONS_ENABLED
    TriggerEnum:
      enum:
      - on_purchase_order_created
      - on_order_shipped_email_received
      - on_send_purchase_order_to_supplier
      - on_purchase_order_acknowledgement_email_received
      - on_purchase_order_email_received
      - on_purchase_order_follow_up
      - on_invoice_email_received
      - on_shipping_document_email_received
      type: string
      description: |-
        * `` - NULL_TRIGGER
        * `on_purchase_order_created` - ON_PURCHASE_ORDER_CREATED
        * `on_order_shipped_email_received` - ON_ORDER_SHIPPED_EMAIL_RECEIVED
        * `on_send_purchase_order_to_supplier` - ON_SEND_PURCHASE_ORDER_TO_SUPPLIER
        * `on_purchase_order_acknowledgement_email_received` - ON_PURCHASE_ORDER_ACKNOWLEDGEMENT_EMAIL_RECEIVED
        * `on_purchase_order_email_received` - ON_PURCHASE_ORDER_EMAIL_RECEIVED
        * `on_purchase_order_follow_up` - ON_PURCHASE_ORDER_FOLLOW_UP
        * `on_invoice_email_received` - ON_INVOICE_EMAIL_RECEIVED
        * `on_shipping_document_email_received` - ON_SHIPPING_DOCUMENT_EMAIL_RECEIVED
    TypeEnum:
      enum:
      - GET
      - POST
      - PUT
      - DELETE
      type: string
      description: |-
        * `GET` - GET
        * `POST` - POST
        * `PUT` - PUT
        * `DELETE` - DELETE
    UploadTypeEnum:
      enum:
      - items
      - suppliers
      - supplier_contacts
      type: string
      description: |-
        * `items` - Items
        * `suppliers` - Suppliers
        * `supplier_contacts` - Supplier Contacts
    User:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        email:
          type: string
          format: email
          readOnly: true
        uuid:
          type: string
          format: uuid
          readOnly: true
        first_name:
          type: string
          readOnly: true
        last_name:
          type: string
          readOnly: true
        phone:
          type: string
          readOnly: true
        display_name:
          type: string
          readOnly: true
      required:
      - display_name
      - email
      - first_name
      - id
      - last_name
      - phone
      - uuid
    UserGroup:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        name:
          type: string
          readOnly: true
        users:
          type: array
          items:
            $ref: '#/components/schemas/User'
          readOnly: true
        created_at:
          type: string
          format: date-time
          readOnly: true
        modified_at:
          type: string
          format: date-time
          readOnly: true
      required:
      - created_at
      - id
      - modified_at
      - name
      - users
    UserWorkflow:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        workflow_type:
          $ref: '#/components/schemas/WorkflowTypeEnum'
        trigger:
          description: |-
            DEPRECATED: This field is redundant as each workflow_type has a 1:1 mapping with a trigger. Will be removed in future versions.

            * `` - NULL_TRIGGER
            * `on_purchase_order_created` - ON_PURCHASE_ORDER_CREATED
            * `on_order_shipped_email_received` - ON_ORDER_SHIPPED_EMAIL_RECEIVED
            * `on_send_purchase_order_to_supplier` - ON_SEND_PURCHASE_ORDER_TO_SUPPLIER
            * `on_purchase_order_acknowledgement_email_received` - ON_PURCHASE_ORDER_ACKNOWLEDGEMENT_EMAIL_RECEIVED
            * `on_purchase_order_email_received` - ON_PURCHASE_ORDER_EMAIL_RECEIVED
            * `on_purchase_order_follow_up` - ON_PURCHASE_ORDER_FOLLOW_UP
            * `on_invoice_email_received` - ON_INVOICE_EMAIL_RECEIVED
            * `on_shipping_document_email_received` - ON_SHIPPING_DOCUMENT_EMAIL_RECEIVED
          oneOf:
          - $ref: '#/components/schemas/TriggerEnum'
          - $ref: '#/components/schemas/BlankEnum'
        team:
          type: integer
        current_snapshot:
          allOf:
          - $ref: '#/components/schemas/WorkflowSnapshot'
          readOnly: true
      required:
      - current_snapshot
      - id
      - team
      - trigger
      - workflow_type
    WeightUnitEnum:
      enum:
      - milligram
      - gram
      - kilogram
      - tonne
      - grain
      - dram
      - ounce
      - pound
      - stone
      - hundredweight
      - us_ton
      - imperial_ton
      - kilotonne
      type: string
      description: |-
        * `milligram` - MILLIGRAM
        * `gram` - GRAM
        * `kilogram` - KILOGRAM
        * `tonne` - TONNE
        * `grain` - GRAIN
        * `dram` - DRAM
        * `ounce` - OUNCE
        * `pound` - POUND
        * `stone` - STONE
        * `hundredweight` - HUNDREDWEIGHT
        * `us_ton` - US_TON
        * `imperial_ton` - IMPERIAL_TON
        * `kilotonne` - KILOTONNE
    WorkflowSnapshot:
      type: object
      properties:
        uuid:
          type: string
          format: uuid
          readOnly: true
        graph: {}
        params: {}
        workflow:
          type: integer
          nullable: true
        created_at:
          type: string
          format: date-time
          readOnly: true
      required:
      - created_at
      - graph
      - params
      - uuid
    WorkflowTypeEnum:
      enum:
      - purchase_order_approval_flow
      - purchase_order_shipped
      - purchase_order_supplier_acknowledgement
      - purchase_order_acknowledgement
      - purchase_order_creation
      - purchase_order_follow_up
      - invoice_processing
      - shipping_document_processing
      type: string
      description: |-
        * `purchase_order_approval_flow` - PURCHASE_ORDER_APPROVAL_FLOW
        * `purchase_order_shipped` - PURCHASE_ORDER_SHIPPED
        * `purchase_order_supplier_acknowledgement` - PURCHASE_ORDER_SUPPLIER_ACKNOWLEDGEMENT
        * `purchase_order_acknowledgement` - PURCHASE_ORDER_ACKNOWLEDGEMENT
        * `purchase_order_creation` - PURCHASE_ORDER_CREATION
        * `purchase_order_follow_up` - PURCHASE_ORDER_FOLLOW_UP
        * `invoice_processing` - INVOICE_PROCESSING
        * `shipping_document_processing` - SHIPPING_DOCUMENT_PROCESSING
  securitySchemes:
    cookieAuth:
      type: apiKey
      in: cookie
      name: sessionid
    tokenAuth:
      type: apiKey
      in: header
      name: Authorization
      description: Token-based authentication with required prefix "Token"
