#!/usr/bin/env python
"""
Script to introspect PurchaseOrder model relationships and fields.
"""

import os
import sys
import django

# Setup Django
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "didero.settings.common")
django.setup()

from django.db.models.fields.related import ForeignKey, ManyToManyField, OneToOneField
from didero.orders.models import PurchaseOrder, Shipment
from didero.invoices.models import Invoice


def analyze_model_relationships(model_class):
    """Analyze all relationships for a Django model."""
    print(f"\n🔍 Analyzing {model_class.__name__} Model")
    print("=" * 60)

    # Get all fields
    all_fields = model_class._meta.get_fields()

    # Categorize fields
    scalar_fields = []
    fk_fields = []
    reverse_fk_fields = []
    m2m_fields = []
    o2o_fields = []

    for field in all_fields:
        if isinstance(field, ForeignKey):
            fk_fields.append(field)
        elif isinstance(field, ManyToManyField):
            m2m_fields.append(field)
        elif isinstance(field, OneToOneField):
            o2o_fields.append(field)
        elif hasattr(field, "related_model") and field.related_model:
            # Reverse relationships
            reverse_fk_fields.append(field)
        else:
            # Scalar fields
            scalar_fields.append(field)

    # Print scalar fields
    print(f"\n📝 Scalar Fields ({len(scalar_fields)}):")
    for field in scalar_fields:
        field_type = (
            field.get_internal_type()
            if hasattr(field, "get_internal_type")
            else type(field).__name__
        )
        print(f"  • {field.name} ({field_type})")

    # Print ForeignKey fields
    print(f"\n🔗 ForeignKey Fields ({len(fk_fields)}):")
    for field in fk_fields:
        related_model = field.related_model.__name__
        print(f"  • {field.name} -> {related_model}")

    # Print reverse ForeignKey relationships
    print(f"\n🔄 Reverse ForeignKey Fields ({len(reverse_fk_fields)}):")
    for field in reverse_fk_fields:
        if hasattr(field, "related_model") and field.related_model:
            related_model = field.related_model.__name__
            accessor_name = (
                field.get_accessor_name()
                if hasattr(field, "get_accessor_name")
                else field.name
            )
            print(f"  • {accessor_name} <- {related_model}")

    # Print ManyToMany fields
    if m2m_fields:
        print(f"\n🔗🔗 ManyToMany Fields ({len(m2m_fields)}):")
        for field in m2m_fields:
            related_model = field.related_model.__name__
            print(f"  • {field.name} <-> {related_model}")

    # Print OneToOne fields
    if o2o_fields:
        print(f"\n🔗 OneToOne Fields ({len(o2o_fields)}):")
        for field in o2o_fields:
            related_model = field.related_model.__name__
            print(f"  • {field.name} <-> {related_model}")

    return {
        "scalar_fields": [f.name for f in scalar_fields],
        "fk_fields": [(f.name, f.related_model.__name__) for f in fk_fields],
        "reverse_fk_fields": [
            (
                f.get_accessor_name() if hasattr(f, "get_accessor_name") else f.name,
                f.related_model.__name__
                if hasattr(f, "related_model") and f.related_model
                else "Unknown",
            )
            for f in reverse_fk_fields
        ],
        "m2m_fields": [(f.name, f.related_model.__name__) for f in m2m_fields],
        "o2o_fields": [(f.name, f.related_model.__name__) for f in o2o_fields],
    }


def suggest_select_related(analysis):
    """Suggest select_related fields based on analysis."""
    print(f"\n💡 Suggested select_related Configuration:")
    print("=" * 60)

    fk_fields = [name for name, _ in analysis["fk_fields"]]

    # Common fields that are likely needed for AI context
    important_fields = []
    optional_fields = []

    for field_name, related_model in analysis["fk_fields"]:
        if field_name in ["team", "supplier", "placed_by", "user"]:
            important_fields.append(field_name)
        elif "address" in field_name.lower():
            important_fields.append(field_name)
        else:
            optional_fields.append(field_name)

    print("🔥 High Priority (likely needed for AI context):")
    for field in important_fields:
        print(f"  • {field}")

    print("\n⚡ Optional (may be needed depending on use case):")
    for field in optional_fields:
        print(f"  • {field}")

    print(f"\n📋 Complete select_related suggestion:")
    all_suggested = important_fields + optional_fields
    print(f'  "select_related": {all_suggested}')

    return all_suggested


if __name__ == "__main__":
    # Analyze all models we support
    models_to_analyze = [
        ("PurchaseOrder", PurchaseOrder),
        ("Invoice", Invoice),
        ("Shipment", Shipment),
    ]

    all_suggestions = {}

    for model_name, model_class in models_to_analyze:
        analysis = analyze_model_relationships(model_class)
        suggested_fields = suggest_select_related(analysis)
        all_suggestions[model_name] = suggested_fields

        print(f"\n🎯 {model_name} Summary:")
        print(f"  • Total scalar fields: {len(analysis['scalar_fields'])}")
        print(f"  • Total ForeignKey fields: {len(analysis['fk_fields'])}")
        print(f"  • Total reverse relationships: {len(analysis['reverse_fk_fields'])}")
        print(f"  • Suggested select_related fields: {len(suggested_fields)}")
        print("\n" + "=" * 80)

    # Print final configuration suggestions
    print(f"\n🚀 FINAL LOOKUP_CONFIG SUGGESTIONS:")
    print("=" * 80)
    for model_name, fields in all_suggestions.items():
        print(f'{model_name}: "select_related": {fields}')
