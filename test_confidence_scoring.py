#!/usr/bin/env python3
"""
Test the confidence scoring implementation directly.
"""

import os
import sys

# Set up Django environment
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "didero.settings.common")

import django
django.setup()

def test_confidence_scoring():
    """Test the confidence scoring algorithm."""
    print("🧪 TESTING: Confidence Scoring Algorithm")
    print("=" * 50)
    
    # Import the confidence scoring function directly
    try:
        from didero.ai.reasoning_engine.tools.actions.database_tools import _calculate_confidence_score
        print("✅ Successfully imported _calculate_confidence_score")
    except ImportError as e:
        print(f"❌ Import failed: {e}")
        return False

    # Test cases for confidence scoring
    test_cases = [
        {
            "name": "High confidence case",
            "reason": "Email received from supplier containing updated vendor information with specific details about order changes including new contact information and delivery dates",
            "field_info": {
                "field_type": "TextField",
                "verbose_name": "Vendor Notes",
                "max_length": 500
            },
            "converted_value": "Updated vendor notes from email",
            "built_context": {
                "entities": {"PO-123": {"exists": True}},
                "references": {"po": [{"number": "PO-123"}]}
            },
            "current_value": "Old notes",
            "expected_range": (80, 95)
        },
        {
            "name": "Medium confidence case", 
            "reason": "Document shows updated information",
            "field_info": {
                "field_type": "CharField",
                "verbose_name": "Notes",
                "max_length": 200
            },
            "converted_value": "Updated notes",
            "built_context": {"entities": {"PO-123": {"exists": True}}},
            "current_value": "Old notes", 
            "expected_range": (60, 80)
        },
        {
            "name": "Low confidence case",
            "reason": "Update needed",
            "field_info": {
                "field_type": "CharField",
                "verbose_name": "Notes"
            },
            "converted_value": "New value",
            "built_context": {},
            "current_value": "Old value",
            "expected_range": (25, 60)
        },
        {
            "name": "Risky operation (removing data)",
            "reason": "Clear field based on document analysis",
            "field_info": {
                "field_type": "TextField",
                "verbose_name": "Notes"
            },
            "converted_value": None,  # Removing data
            "built_context": {"entities": {"PO-123": {"exists": True}}},
            "current_value": "Some important data",
            "expected_range": (25, 55)  # Should be penalized
        },
        {
            "name": "Unsupported field type",
            "reason": "Need to update foreign key relationship",
            "field_info": {
                "field_type": "ForeignKey",  # Not supported in V1
                "verbose_name": "Supplier"
            },
            "converted_value": "123",
            "built_context": {"entities": {"PO-123": {"exists": True}}},
            "current_value": "456",
            "expected_range": (25, 50)  # Should be penalized
        }
    ]
    
    print("\n📊 Confidence Scoring Test Results:")
    print("-" * 40)
    
    results = []
    
    for i, test_case in enumerate(test_cases, 1):
        try:
            confidence = _calculate_confidence_score(
                reason=test_case["reason"],
                field_info=test_case["field_info"],
                converted_value=test_case["converted_value"],
                built_context=test_case["built_context"],
                current_value=test_case["current_value"]
            )
            
            min_expected, max_expected = test_case["expected_range"]
            in_range = min_expected <= confidence <= max_expected
            
            status = "✅ PASS" if in_range else "❌ FAIL"
            print(f"{status} Test {i}: {test_case['name']}")
            print(f"     Score: {confidence:.1f} (expected {min_expected}-{max_expected})")
            
            results.append(in_range)
            
        except Exception as e:
            print(f"❌ Test {i}: {test_case['name']} - Exception: {e}")
            results.append(False)
    
    # Test the scoring components individually
    print("\n🔍 Component Analysis:")
    print("-" * 25)
    
    # Test base components
    base_case = {
        "reason": "Detailed email analysis showing supplier updated their contact information and delivery schedule",
        "field_info": {"field_type": "TextField", "max_length": 1000},
        "converted_value": "Updated contact info",
        "built_context": {
            "entities": {"PO-123": {"exists": True}},
            "references": {"po": [{"number": "PO-123"}]}
        },
        "current_value": "Old contact info"
    }
    
    base_confidence = _calculate_confidence_score(**base_case)
    print(f"✅ Base detailed case: {base_confidence:.1f}")
    
    # Test brief reasoning
    brief_case = dict(base_case)
    brief_case["reason"] = "Update needed"
    brief_confidence = _calculate_confidence_score(**brief_case)
    print(f"✅ Brief reasoning impact: {brief_confidence:.1f} (should be lower)")
    
    # Test context impact
    no_context_case = dict(base_case)
    no_context_case["built_context"] = {}
    no_context_confidence = _calculate_confidence_score(**no_context_case)
    print(f"✅ No context impact: {no_context_confidence:.1f} (should be lower)")
    
    # Results summary
    print("\n" + "=" * 50)
    print("📈 CONFIDENCE SCORING TEST SUMMARY")
    print("=" * 50)
    
    passed_tests = sum(results)
    total_tests = len(results)
    score_percentage = (passed_tests / total_tests) * 100
    
    print(f"🎯 Tests Passed: {passed_tests}/{total_tests} ({score_percentage:.1f}%)")
    
    if score_percentage >= 80:
        print("🎉 EXCELLENT! Confidence scoring is working correctly")
        return True
    elif score_percentage >= 60:
        print("✅ GOOD! Confidence scoring is mostly working")
        return True
    else:
        print("⚠️ NEEDS WORK! Confidence scoring has issues")
        return False

if __name__ == "__main__":
    print("Database Tools V1 - Confidence Scoring Test")
    print("Testing the confidence scoring algorithm implementation")
    print()
    
    success = test_confidence_scoring()
    sys.exit(0 if success else 1)