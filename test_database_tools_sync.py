#!/usr/bin/env python3
"""
Synchronous comprehensive test for Database Tools V1 implementation.
Tests the complete workflow using sync methods.
"""

import os
import sys
from datetime import date, datetime
from decimal import Decimal

# Setup Django
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
import django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'didero.settings.common')
django.setup()

# Now import Django models and tools
from didero.orders.models import PurchaseOrder
from didero.suppliers.models import Supplier
from didero.users.models.team_models import Team
from didero.tasks.models import Task, TaskAction
from didero.tasks.schemas import TaskStatus, TaskActionStatus, TaskType as TaskTypeName
from didero.ai.reasoning_engine.tools.utils.field_converters import FieldConverter, FieldConversionError
from didero.utils.utils import get_didero_ai_user


class MockRunContext:
    """Mock RunContext for testing."""
    def __init__(self, team_id: int):
        self.deps = type('Dependencies', (), {'team_id': team_id})()


def test_lookup_tool_sync():
    """Test lookup functionality with real data synchronously."""
    print("🔍 Testing lookup functionality...")
    
    # Get a real PO from the database
    po = PurchaseOrder.objects.filter(team_id=1).first()
    if not po:
        print("❌ No PurchaseOrder found for testing")
        return False, None
    
    print(f"   Using PO: {po.po_number} (ID: {po.id}, Team: {po.team_id})")
    
    # Test field metadata extraction
    from didero.ai.reasoning_engine.tools.context_builder.database_tools import _get_field_metadata
    
    updatable_fields = [
        "order_status", "requested_date", "total_cost", 
        "vendor_notes", "internal_notes", "payment_terms"
    ]
    
    field_metadata = _get_field_metadata(PurchaseOrder, updatable_fields)
    
    print("✅ Field metadata extracted")
    print(f"   Total fields: {len(field_metadata)}")
    
    # Show supported fields
    supported_fields = [k for k, v in field_metadata.items() if v.get('supported_in_v1')]
    print(f"   V1 supported fields: {supported_fields}")
    
    # Show field details for a few fields
    for field_name in supported_fields[:3]:
        metadata = field_metadata[field_name]
        print(f"   - {field_name}: {metadata['field_type']} ({'updatable' if metadata.get('updatable') else 'read-only'})")
    
    return True, (po, field_metadata)


def test_suggest_database_update_sync(po_data):
    """Test database update suggestion creation synchronously."""
    print("\n💡 Testing database update suggestion...")
    
    po, field_metadata = po_data
    
    # Test field conversion for vendor_notes (CharField)
    current_notes = po.vendor_notes or ""
    suggested_notes = f"Updated by AI test - {datetime.now().isoformat()}"
    
    print("   Testing CharField conversion:")
    print(f"   Current: '{current_notes}'")
    print(f"   Suggested: '{suggested_notes}'")
    
    try:
        # Test field conversion
        converted_value, display_string, field_info = FieldConverter.validate_and_convert(
            PurchaseOrder, "vendor_notes", suggested_notes
        )
        
        print("✅ Field conversion successful")
        print(f"   Field type: {field_info['field_type']}")
        print(f"   Converted value: '{converted_value}'")
        print(f"   Display string: '{display_string}'")
        
        # Create task manually for testing
        from didero.tasks.utils import create_task_v2
        from didero.tasks.schemas import TaskActionButtonType, TaskActionType
        from django.contrib.contenttypes.models import ContentType
        
        # Get user for task assignment
        team = Team.objects.get(id=po.team_id)
        user = get_didero_ai_user(team)
        if not user:
            user = team.users.filter(is_staff=True).first()
        
        if not user:
            print("❌ No user found for task assignment")
            return False, None
        
        print(f"   Assigning task to user: {user}")
        
        # Prepare task parameters
        task_params = {
            "model": "orders.PurchaseOrder",
            "record_id": po.id,
            "record_display": f"Purchase Order {po.po_number}",
            "field_name": "vendor_notes",
            "field_type": field_info["field_type"],
            "current_value": current_notes,
            "current_display": current_notes or "(empty)",
            "suggested_value": converted_value,
            "suggested_display": display_string,
            "ai_reasoning": "Test update to verify AI database tools functionality",
            "confidence": 95.0,
            "source_context": {
                "test_run": True,
                "original_po_number": po.po_number,
                "timestamp": datetime.now().isoformat()
            },
        }
        
        # Create task actions
        actions = [
            {
                "action_type": TaskActionType.EXECUTE_DATABASE_UPDATE,
                "action_params": {},
                "action_execution_params": {
                    "model": "orders.PurchaseOrder",
                    "record_id": po.id,
                    "field_name": "vendor_notes",
                    "field_type": field_info["field_type"],
                    "suggested_value": str(converted_value),
                },
                "button_type": TaskActionButtonType.GREEN,
            },
            {
                "action_type": TaskActionType.REJECT_DATABASE_UPDATE,
                "action_params": {},
                "action_execution_params": {
                    "rejection_reason": "",
                },
                "button_type": TaskActionButtonType.RED,
            },
        ]
        
        # Get content type
        content_type = ContentType.objects.get_for_model(PurchaseOrder)
        
        # Create the task
        task = create_task_v2(
            task_type=TaskTypeName.DATABASE_UPDATE_APPROVAL,
            user=user,
            model_type=content_type,
            model_id=str(po.id),
            task_type_params=task_params,
            actions=actions,
        )
        
        print("✅ Task created successfully")
        print(f"   Task ID: {task.id}")
        
        return True, task
        
    except Exception as e:
        print(f"❌ Error in suggestion creation: {e}")
        import traceback
        traceback.print_exc()
        return False, None


def test_task_in_database_sync(task):
    """Verify the task was created correctly in the database."""
    print(f"\n🗄️  Testing task {task.id} in database...")
    
    try:
        # Refresh from database
        task.refresh_from_db()
        
        print("✅ Task found in database")
        print(f"   Status: {task.status}")
        print(f"   User: {task.user}")
        print(f"   Task type: {task.task_type_v2.name if task.task_type_v2 else 'N/A'}")
        
        # Check task config
        config = task.task_config
        print(f"   Title: {config.get('title', 'N/A')}")
        print(f"   Description preview: {config.get('description', 'N/A')[:100]}...")
        
        # Check task actions
        actions = list(task.actions.all())
        print(f"   Actions count: {len(actions)}")
        
        for i, action in enumerate(actions, 1):
            print(f"   Action {i}: {action.action_type.name}")
            print(f"     Title: {action.title}")
            print(f"     Status: {action.status}")
            print(f"     Button type: {action.button_type}")
        
        return True, task
        
    except Exception as e:
        print(f"❌ Error checking task: {e}")
        return False, None


def test_task_action_handlers_sync(task):
    """Test task action handlers."""
    print("\n⚡ Testing task action handlers...")
    
    try:
        # Get the approve action
        approve_action = task.actions.filter(action_type__name="EXECUTE_DATABASE_UPDATE").first()
        reject_action = task.actions.filter(action_type__name="REJECT_DATABASE_UPDATE").first()
        
        if not approve_action:
            print("❌ EXECUTE_DATABASE_UPDATE action not found")
            return False
        
        if not reject_action:
            print("❌ REJECT_DATABASE_UPDATE action not found")
            return False
        
        print("✅ Found both actions:")
        print(f"   Approve: {approve_action.title}")
        print(f"   Reject: {reject_action.title}")
        
        # Test handler import
        from didero.tasks.actions import execute_database_update, reject_database_update
        print("✅ Successfully imported action handlers")
        
        # Get execution parameters
        exec_params = approve_action.execution_params
        model_path = exec_params["model"]
        record_id = int(exec_params["record_id"])
        field_name = exec_params["field_name"]
        suggested_value = exec_params["suggested_value"]
        
        print("   Execution parameters:")
        print(f"   - Model: {model_path}")
        print(f"   - Record ID: {record_id}")
        print(f"   - Field: {field_name}")
        print(f"   - Suggested value: '{suggested_value}'")
        
        # Get current value from database
        po = PurchaseOrder.objects.get(pk=record_id)
        current_value = getattr(po, field_name)
        print(f"   - Current value: '{current_value}'")
        
        # Test handler context
        handler_context = {
            "user": task.user,
            "task_action_id": str(approve_action.id),
        }
        
        print(f"   Handler context prepared for user: {task.user}")
        
        # We'll simulate execution without actually changing the database
        print(f"   📋 Simulation: Would update {field_name} from '{current_value}' to '{suggested_value}'")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing action handlers: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_field_conversion_accuracy():
    """Test field conversion accuracy with various data types."""
    print("\n🔬 Testing field conversion accuracy...")
    
    test_cases = [
        # MoneyField tests
        ("MoneyField", "$1,234.56", "USD"),
        ("MoneyField", "999.99", "USD"),
        ("MoneyField", "0", "USD"),
        
        # CharField tests  
        ("CharField", "Test vendor notes", None),
        ("CharField", "   Trimmed spaces   ", None),
        ("CharField", 12345, None),
        
        # DateField tests
        ("DateField", "2024-12-25", None),
        ("DateField", "12/25/2024", None),
        ("DateField", "December 25, 2024", None),
        
        # BooleanField tests
        ("BooleanField", "true", None),
        ("BooleanField", "false", None),
        ("BooleanField", "yes", None),
        ("BooleanField", "no", None),
        ("BooleanField", 1, None),
        ("BooleanField", 0, None),
    ]
    
    success_count = 0
    total_count = len(test_cases)
    
    for field_type, value, extra in test_cases:
        try:
            if field_type == "MoneyField":
                converted, display = FieldConverter.convert_money_field(value, extra)
            elif field_type == "CharField":
                converted, display = FieldConverter.convert_char_field(value, extra)
            elif field_type == "DateField":
                converted, display = FieldConverter.convert_date_field(value)
            elif field_type == "BooleanField":
                converted, display = FieldConverter.convert_boolean_field(value)
            
            print(f"   ✅ {field_type}: '{value}' -> {converted} ('{display}')")
            success_count += 1
            
        except Exception as e:
            print(f"   ❌ {field_type}: '{value}' -> Error: {e}")
    
    print(f"   Conversion accuracy: {success_count}/{total_count} ({success_count/total_count*100:.1f}%)")
    return success_count == total_count


def test_database_query():
    """Test that we can actually query the database and get the records."""
    print("\n🔎 Testing database connectivity and data...")
    
    try:
        # Count records
        po_count = PurchaseOrder.objects.count()
        team_count = Team.objects.exclude(id=0).count()
        task_count = Task.objects.count()
        
        print("   Database connectivity: ✅")
        print(f"   Purchase Orders: {po_count}")
        print(f"   Teams: {team_count}")
        print(f"   Tasks: {task_count}")
        
        # Get sample data
        sample_po = PurchaseOrder.objects.first()
        if sample_po:
            print(f"   Sample PO: {sample_po.po_number} (Team: {sample_po.team_id})")
            print(f"   Total cost: {sample_po.total_cost}")
            print(f"   Vendor notes: '{sample_po.vendor_notes or '(empty)'}'")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Database error: {e}")
        return False


def main():
    """Main synchronous test function."""
    print("🧪 Comprehensive Database Tools V1 Testing (Synchronous)")
    print("=" * 65)
    
    success = True
    
    # Test 0: Database connectivity
    print("🔎 Test 0: Database Connectivity")
    if not test_database_query():
        print("❌ Database connectivity failed, skipping remaining tests")
        return False
    
    # Test 1: Lookup functionality
    print("\n🔍 Test 1: Lookup Functionality")
    lookup_success, po_data = test_lookup_tool_sync()
    if not lookup_success:
        success = False
        print("❌ Skipping remaining tests due to lookup failure")
        return success
    
    # Test 2: Database update suggestion
    print("\n💡 Test 2: Database Update Suggestion")
    suggest_success, task = test_suggest_database_update_sync(po_data)
    if not suggest_success:
        success = False
    else:
        # Test 3: Task in database
        print("\n🗄️  Test 3: Task Database Verification")
        task_success, verified_task = test_task_in_database_sync(task)
        if not task_success:
            success = False
        else:
            # Test 4: Task action handlers
            print("\n⚡ Test 4: Task Action Handlers")
            handler_success = test_task_action_handlers_sync(verified_task)
            if not handler_success:
                success = False
    
    # Test 5: Field conversion accuracy
    print("\n🔬 Test 5: Field Conversion Accuracy")
    conversion_success = test_field_conversion_accuracy()
    if not conversion_success:
        success = False
    
    # Summary
    print("\n" + "=" * 65)
    if success:
        print("🎉 ALL TESTS PASSED! Database Tools V1 is fully functional.")
        print("\n📋 Summary of successful tests:")
        print("   ✅ Database connectivity and data access")
        print("   ✅ Field metadata extraction")
        print("   ✅ Database update suggestion creation")
        print("   ✅ Task creation and database storage")
        print("   ✅ Task action structure validation")
        print("   ✅ Action handler accessibility")
        print("   ✅ Field conversion accuracy")
        print("\n🚀 The database tools are ready for AI agent integration!")
        
        if 'task' in locals() and task:
            print(f"\n📊 Created test task ID: {task.id}")
            print("   You can inspect this task in the Django admin or database")
    else:
        print("❌ Some tests failed. Please check the implementation.")
    
    return success


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)